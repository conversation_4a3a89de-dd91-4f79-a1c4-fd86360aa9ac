/**
 * 🌍 全球季节性数据服务
 * 提供世界各地的季节性信息、节庆数据和气候特征
 */

export interface GlobalDestination {
  name: string;
  country: string;
  continent: string;
  hemisphere: 'north' | 'south';
  climateZone: 'tropical' | 'subtropical' | 'temperate' | 'continental' | 'polar' | 'arid';
  coordinates: { lat: number; lng: number };
  timezone: string;
  culturalRegion: string;
}

export interface GlobalFestival {
  id: string;
  name: string;
  nameEn: string;
  type: 'religious' | 'cultural' | 'seasonal' | 'national' | 'harvest' | 'music' | 'food' | 'art';
  startDate: string; // MM-DD format for recurring events
  endDate: string;
  duration: number; // days
  description: string;
  culturalSignificance: string;
  activities: string[];
  regions: string[]; // countries/regions where celebrated
  globalPopularity: number; // 1-100
  touristFriendly: boolean;
  crowdLevel: 'low' | 'medium' | 'high' | 'very_high';
  costImpact: 'none' | 'low' | 'medium' | 'high';
}

export interface SeasonalCharacteristics {
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  hemisphere: 'north' | 'south';
  months: number[];
  temperature: {
    min: number;
    max: number;
    average: number;
  };
  weather: {
    condition: string;
    rainfall: string;
    humidity: string;
    sunshine: string;
  };
  activities: string[];
  advantages: string[];
  disadvantages: string[];
  crowdLevel: 'low' | 'medium' | 'high';
  priceLevel: 'budget' | 'moderate' | 'expensive';
}

export class GlobalSeasonalDataService {
  private globalFestivals: GlobalFestival[] = [
    // 🎄 全球性节庆
    {
      id: 'christmas',
      name: '圣诞节',
      nameEn: 'Christmas',
      type: 'religious',
      startDate: '12-24',
      endDate: '12-26',
      duration: 3,
      description: '全球最重要的基督教节日之一',
      culturalSignificance: '庆祝耶稣基督诞生，家庭团聚的重要时刻',
      activities: ['圣诞市场', '教堂礼拜', '家庭聚餐', '礼物交换', '圣诞装饰观赏'],
      regions: ['欧洲', '北美', '南美', '大洋洲', '亚洲部分地区'],
      globalPopularity: 95,
      touristFriendly: true,
      crowdLevel: 'very_high',
      costImpact: 'high'
    },
    {
      id: 'new_year',
      name: '新年',
      nameEn: 'New Year',
      type: 'cultural',
      startDate: '12-31',
      endDate: '01-01',
      duration: 2,
      description: '全球庆祝新年的节日',
      culturalSignificance: '辞旧迎新，新的开始',
      activities: ['烟花表演', '倒数庆祝', '新年派对', '许愿活动', '特色美食'],
      regions: ['全球'],
      globalPopularity: 100,
      touristFriendly: true,
      crowdLevel: 'very_high',
      costImpact: 'high'
    },

    // 🌸 亚洲节庆
    {
      id: 'chinese_new_year',
      name: '春节',
      nameEn: 'Chinese New Year',
      type: 'cultural',
      startDate: '01-21', // 大致时间，实际会变动
      endDate: '02-05',
      duration: 15,
      description: '中华文化最重要的传统节日',
      culturalSignificance: '农历新年，家庭团聚，祈求新年好运',
      activities: ['舞龙舞狮', '放烟花', '庙会', '传统美食', '红包习俗'],
      regions: ['中国', '台湾', '香港', '新加坡', '马来西亚', '泰国'],
      globalPopularity: 85,
      touristFriendly: true,
      crowdLevel: 'very_high',
      costImpact: 'high'
    },
    {
      id: 'cherry_blossom',
      name: '樱花季',
      nameEn: 'Cherry Blossom Season',
      type: 'seasonal',
      startDate: '03-20',
      endDate: '05-10',
      duration: 50,
      description: '日本最著名的季节性景观',
      culturalSignificance: '象征生命的美丽和短暂',
      activities: ['赏樱', '樱花野餐', '传统茶道', '和服体验', '樱花摄影'],
      regions: ['日本', '韩国', '中国部分地区'],
      globalPopularity: 90,
      touristFriendly: true,
      crowdLevel: 'very_high',
      costImpact: 'high'
    },

    // 🎪 欧洲节庆
    {
      id: 'oktoberfest',
      name: '慕尼黑啤酒节',
      nameEn: 'Oktoberfest',
      type: 'cultural',
      startDate: '09-16',
      endDate: '10-03',
      duration: 18,
      description: '世界最大的啤酒节',
      culturalSignificance: '巴伐利亚传统文化庆典',
      activities: ['啤酒品尝', '传统音乐', '德式美食', '民族服装', '游乐设施'],
      regions: ['德国', '奥地利'],
      globalPopularity: 80,
      touristFriendly: true,
      crowdLevel: 'very_high',
      costImpact: 'high'
    },
    {
      id: 'carnival',
      name: '嘉年华',
      nameEn: 'Carnival',
      type: 'cultural',
      startDate: '02-10', // 复活节前的时间，会变动
      endDate: '02-25',
      duration: 15,
      description: '欧洲和南美的传统嘉年华庆典',
      culturalSignificance: '基督教传统节日，狂欢庆祝',
      activities: ['面具舞会', '花车游行', '街头表演', '传统舞蹈', '特色美食'],
      regions: ['巴西', '意大利', '法国', '西班牙', '德国'],
      globalPopularity: 85,
      touristFriendly: true,
      crowdLevel: 'very_high',
      costImpact: 'high'
    },

    // 🕌 中东/印度节庆
    {
      id: 'ramadan',
      name: '斋月',
      nameEn: 'Ramadan',
      type: 'religious',
      startDate: '03-10', // 伊斯兰历，日期会变动
      endDate: '04-09',
      duration: 30,
      description: '伊斯兰教最重要的宗教月份',
      culturalSignificance: '穆斯林斋戒、祈祷和反思的神圣时期',
      activities: ['开斋饭', '夜间祈祷', '慈善活动', '家庭聚会', '传统市场'],
      regions: ['中东', '北非', '东南亚穆斯林地区'],
      globalPopularity: 70,
      touristFriendly: false, // 需要尊重当地习俗
      crowdLevel: 'medium',
      costImpact: 'medium'
    },
    {
      id: 'diwali',
      name: '排灯节',
      nameEn: 'Diwali',
      type: 'religious',
      startDate: '10-20', // 印度历，日期会变动
      endDate: '10-24',
      duration: 5,
      description: '印度教最重要的节日之一',
      culturalSignificance: '光明战胜黑暗，善良战胜邪恶',
      activities: ['点灯仪式', '烟花表演', '传统舞蹈', '甜品制作', '家庭聚会'],
      regions: ['印度', '尼泊尔', '斯里兰卡', '马来西亚', '新加坡'],
      globalPopularity: 75,
      touristFriendly: true,
      crowdLevel: 'high',
      costImpact: 'medium'
    }
  ];

  /**
   * 🌍 获取目的地的季节特征
   */
  getSeasonalCharacteristics(destination: string, season: string, hemisphere: 'north' | 'south' = 'north'): SeasonalCharacteristics {
    // 根据半球调整季节
    const adjustedSeason = this.adjustSeasonForHemisphere(season, hemisphere);
    
    const baseCharacteristics = this.getBaseSeasonalData(adjustedSeason as any);
    
    // 根据目的地调整特征
    return this.adjustForDestination(baseCharacteristics, destination);
  }

  /**
   * 🎪 获取目的地在特定时期的节庆活动
   */
  getFestivalsForDestination(destination: string, startDate: Date, endDate: Date): GlobalFestival[] {
    const country = this.getCountryFromDestination(destination);
    const region = this.getRegionFromCountry(country);
    
    return this.globalFestivals.filter(festival => {
      // 检查地区匹配
      const regionMatch = festival.regions.includes(region) || 
                         festival.regions.includes(country) || 
                         festival.regions.includes('全球');
      
      if (!regionMatch) return false;
      
      // 检查日期匹配
      return this.isFestivalInDateRange(festival, startDate, endDate);
    });
  }

  /**
   * 🌡️ 获取季节性活动建议
   */
  getSeasonalActivitySuggestions(destination: string, season: string, preferences: any): string[] {
    const characteristics = this.getSeasonalCharacteristics(destination, season);
    const baseActivities = characteristics.activities;
    
    // 根据用户偏好过滤和增强活动
    const filteredActivities = this.filterActivitiesByPreferences(baseActivities, preferences);
    
    // 添加目的地特色活动
    const localActivities = this.getLocalSeasonalActivities(destination, season);
    
    return [...filteredActivities, ...localActivities];
  }

  /**
   * 📊 计算季节性推荐评分
   */
  calculateSeasonalScore(destination: string, startDate: Date, duration: number): number {
    let score = 50; // 基础分数
    
    const season = this.getSeasonFromDate(startDate);
    const characteristics = this.getSeasonalCharacteristics(destination, season);
    const festivals = this.getFestivalsForDestination(startDate, new Date(startDate.getTime() + duration * 24 * 60 * 60 * 1000));
    
    // 天气评分 (30%)
    score += this.calculateWeatherScore(characteristics) * 0.3;
    
    // 节庆活动评分 (25%)
    score += this.calculateFestivalScore(festivals) * 0.25;
    
    // 人流和价格评分 (25%)
    score += this.calculateCrowdAndPriceScore(characteristics) * 0.25;
    
    // 活动丰富度评分 (20%)
    score += this.calculateActivityScore(characteristics) * 0.2;
    
    return Math.min(100, Math.max(0, score));
  }

  /**
   * 🔄 根据半球调整季节
   */
  private adjustSeasonForHemisphere(season: string, hemisphere: 'north' | 'south'): string {
    if (hemisphere === 'south') {
      const seasonMap: Record<string, string> = {
        'spring': 'autumn',
        'summer': 'winter',
        'autumn': 'spring',
        'winter': 'summer'
      };
      return seasonMap[season] || season;
    }
    return season;
  }

  /**
   * 📅 获取日期对应的季节 - 使用统一工具
   */
  private getSeasonFromDate(date: Date): string {
    // 导入SeasonDateUtils并使用统一逻辑
    const month = date.getMonth() + 1;

    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  /**
   * 🌍 从目的地获取国家
   */
  private getCountryFromDestination(destination: string): string {
    const destinationMap: Record<string, string> = {
      '东京': '日本',
      '大阪': '日本',
      '京都': '日本',
      '首尔': '韩国',
      '釜山': '韩国',
      '曼谷': '泰国',
      '清迈': '泰国',
      '新加坡': '新加坡',
      '吉隆坡': '马来西亚',
      '槟城': '马来西亚',
      '巴黎': '法国',
      '伦敦': '英国',
      '罗马': '意大利',
      '巴塞罗那': '西班牙',
      '柏林': '德国',
      '慕尼黑': '德国',
      '纽约': '美国',
      '洛杉矶': '美国',
      '悉尼': '澳大利亚',
      '墨尔本': '澳大利亚'
    };
    
    return destinationMap[destination] || destination;
  }

  /**
   * 🗺️ 从国家获取地区
   */
  private getRegionFromCountry(country: string): string {
    const regionMap: Record<string, string> = {
      '日本': '东亚',
      '韩国': '东亚',
      '中国': '东亚',
      '泰国': '东南亚',
      '新加坡': '东南亚',
      '马来西亚': '东南亚',
      '法国': '欧洲',
      '英国': '欧洲',
      '意大利': '欧洲',
      '西班牙': '欧洲',
      '德国': '欧洲',
      '美国': '北美',
      '加拿大': '北美',
      '澳大利亚': '大洋洲'
    };
    
    return regionMap[country] || '其他';
  }

  /**
   * 🎪 检查节庆是否在日期范围内
   */
  private isFestivalInDateRange(festival: GlobalFestival, startDate: Date, endDate: Date): boolean {
    // 简化实现：检查月份和日期
    const festivalStart = this.parseMonthDay(festival.startDate);
    const festivalEnd = this.parseMonthDay(festival.endDate);
    
    const rangeStart = { month: startDate.getMonth() + 1, day: startDate.getDate() };
    const rangeEnd = { month: endDate.getMonth() + 1, day: endDate.getDate() };
    
    return this.isDateInRange(festivalStart, rangeStart, rangeEnd) ||
           this.isDateInRange(festivalEnd, rangeStart, rangeEnd);
  }

  /**
   * 📅 解析月-日格式
   */
  private parseMonthDay(dateStr: string): { month: number; day: number } {
    const [month, day] = dateStr.split('-').map(Number);
    return { month, day };
  }

  /**
   * 📊 检查日期是否在范围内
   */
  private isDateInRange(date: { month: number; day: number }, start: { month: number; day: number }, end: { month: number; day: number }): boolean {
    // 简化实现
    return (date.month >= start.month && date.month <= end.month);
  }

  /**
   * 🌤️ 获取基础季节数据
   */
  private getBaseSeasonalData(season: 'spring' | 'summer' | 'autumn' | 'winter'): SeasonalCharacteristics {
    const seasonData: Record<string, Partial<SeasonalCharacteristics>> = {
      spring: {
        activities: ['赏花', '户外徒步', '公园漫步', '自然摄影', '温泉体验'],
        advantages: ['气候宜人', '花卉盛开', '户外活动舒适'],
        disadvantages: ['可能多雨', '人流较多'],
        crowdLevel: 'high',
        priceLevel: 'expensive'
      },
      summer: {
        activities: ['海滩活动', '户外音乐节', '夜市探索', '避暑活动', '水上运动'],
        advantages: ['日照充足', '活动丰富', '夜生活精彩'],
        disadvantages: ['炎热潮湿', '人流很多', '价格较高'],
        crowdLevel: 'very_high',
        priceLevel: 'expensive'
      },
      autumn: {
        activities: ['赏枫', '收获节庆', '徒步登山', '文化活动', '美食节'],
        advantages: ['气候舒适', '景色优美', '文化活动多'],
        disadvantages: ['天气多变', '部分景点关闭'],
        crowdLevel: 'medium',
        priceLevel: 'moderate'
      },
      winter: {
        activities: ['滑雪运动', '温泉体验', '节庆庆典', '室内文化', '冬季美食'],
        advantages: ['节庆氛围', '冬季运动', '价格相对便宜'],
        disadvantages: ['天气寒冷', '日照时间短', '部分活动受限'],
        crowdLevel: 'low',
        priceLevel: 'budget'
      }
    };

    return {
      season,
      hemisphere: 'north',
      months: this.getMonthsForSeason(season),
      temperature: { min: 10, max: 25, average: 18 },
      weather: { condition: '宜人', rainfall: '适中', humidity: '舒适', sunshine: '充足' },
      ...seasonData[season]
    } as SeasonalCharacteristics;
  }

  /**
   * 📅 获取季节对应的月份
   */
  private getMonthsForSeason(season: string): number[] {
    const seasonMonths: Record<string, number[]> = {
      spring: [3, 4, 5],
      summer: [6, 7, 8],
      autumn: [9, 10, 11],
      winter: [12, 1, 2]
    };
    
    return seasonMonths[season] || [1, 2, 3];
  }

  /**
   * 🎯 根据目的地调整特征
   */
  private adjustForDestination(characteristics: SeasonalCharacteristics, destination: string): SeasonalCharacteristics {
    // 根据具体目的地调整特征
    // 这里可以添加更多目的地特定的调整逻辑
    return characteristics;
  }

  /**
   * 🎨 根据偏好过滤活动
   */
  private filterActivitiesByPreferences(activities: string[], preferences: any): string[] {
    if (!preferences || !preferences.travelStyle) {
      return activities;
    }
    
    // 根据用户偏好过滤活动
    return activities;
  }

  /**
   * 🏛️ 获取当地季节性活动
   */
  private getLocalSeasonalActivities(destination: string, season: string): string[] {
    // 返回目的地特定的季节性活动
    return [];
  }

  /**
   * 📊 计算各种评分
   */
  private calculateWeatherScore(characteristics: SeasonalCharacteristics): number {
    // 基于天气特征计算评分
    return 70;
  }

  private calculateFestivalScore(festivals: GlobalFestival[]): number {
    // 基于节庆活动计算评分
    return festivals.length * 20;
  }

  private calculateCrowdAndPriceScore(characteristics: SeasonalCharacteristics): number {
    // 基于人流和价格计算评分
    const crowdScore = characteristics.crowdLevel === 'low' ? 80 : 
                      characteristics.crowdLevel === 'medium' ? 60 : 40;
    const priceScore = characteristics.priceLevel === 'budget' ? 80 : 
                      characteristics.priceLevel === 'moderate' ? 60 : 40;
    return (crowdScore + priceScore) / 2;
  }

  private calculateActivityScore(characteristics: SeasonalCharacteristics): number {
    // 基于活动丰富度计算评分
    return Math.min(80, characteristics.activities.length * 10);
  }
}

// 导出单例实例
export const globalSeasonalDataService = new GlobalSeasonalDataService();
