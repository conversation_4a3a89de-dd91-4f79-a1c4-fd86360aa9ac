/**
 * 🧪 简化集成测试脚本
 * 
 * 验证核心问题是否已解决：
 * 1. 时间线12:48-12:48异常时间问题
 * 2. 交通安排完整性
 * 3. 用餐安排合理性
 * 4. 免费景点预算显示
 * 5. 活动图标分类
 * 6. 预算重复显示修复
 * 
 * <AUTHOR> Think System
 * @version 1.0.0
 * @created 2025-01-30
 */

console.log('🧪🧪🧪 开始简化集成测试 🧪🧪🧪');
console.log('='.repeat(60));

// 测试1: 验证新组件是否可以导入
console.log('📦 测试1: 验证组件导入');
try {
  const { UnifiedTimeAllocationEngine } = require('../services/time/UnifiedTimeAllocationEngine');
  const { EnhancedTransportationGenerator } = require('../services/transport/EnhancedTransportationGenerator');
  const { IntelligentMealScheduler } = require('../services/meal/IntelligentMealScheduler');
  const { IntelligentActivityClassifier } = require('../services/classification/IntelligentActivityClassifier');
  const { TrulyUnifiedBudgetEngine } = require('../utils/TrulyUnifiedBudgetEngine');
  
  console.log('✅ 所有组件导入成功');
  
  // 测试关键方法是否存在
  const methods = [
    { component: UnifiedTimeAllocationEngine, method: 'allocateIntelligentTimes' },
    { component: EnhancedTransportationGenerator, method: 'generateCompleteTransportPlan' },
    { component: IntelligentMealScheduler, method: 'scheduleDailyMeals' },
    { component: IntelligentActivityClassifier, method: 'classifyActivity' },
    { component: TrulyUnifiedBudgetEngine, method: 'getUnifiedActivityBudget' }
  ];
  
  for (const { component, method } of methods) {
    if (typeof component[method] === 'function') {
      console.log(`✅ ${component.name}.${method} 方法存在`);
    } else {
      console.log(`❌ ${component.name}.${method} 方法不存在`);
    }
  }
  
} catch (error) {
  console.error('❌ 组件导入失败:', error.message);
}

// 测试2: 验证免费景点预算显示
console.log('\n💰 测试2: 验证免费景点预算显示');
try {
  const { TrulyUnifiedBudgetEngine } = require('../utils/TrulyUnifiedBudgetEngine');
  
  // 测试免费景点
  const freePlaces = [
    {
      id: 'free-1',
      name: '浅草寺',
      cost: { amount: 0, currency: 'JPY' },
      location: { name: '浅草寺' },
      description: '东京著名的免费寺庙'
    },
    {
      id: 'free-2',
      name: '涩谷十字路口',
      cost: { amount: 0, currency: 'JPY' },
      location: { name: '涩谷十字路口' },
      description: '世界著名的免费观光点'
    }
  ];

  for (const place of freePlaces) {
    const budgetInfo = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(place);
    
    if (budgetInfo.isFree) {
      console.log(`✅ ${place.name} 正确识别为免费`);
    } else {
      console.log(`❌ ${place.name} 应该被识别为免费，但显示为收费`);
    }
  }
  
  // 测试收费景点
  const paidPlace = {
    id: 'paid-1',
    name: '东京塔',
    cost: { amount: 1200, currency: 'JPY' },
    location: { name: '东京塔' },
    description: '东京著名的收费观景台'
  };

  const paidBudgetInfo = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(paidPlace);
  
  if (!paidBudgetInfo.isFree) {
    console.log(`✅ ${paidPlace.name} 正确识别为收费`);
  } else {
    console.log(`❌ ${paidPlace.name} 应该被识别为收费，但显示为免费`);
  }
  
} catch (error) {
  console.error('❌ 免费景点测试失败:', error.message);
}

// 测试3: 验证活动分类
console.log('\n🏷️ 测试3: 验证活动分类');
try {
  const { IntelligentActivityClassifier } = require('../services/classification/IntelligentActivityClassifier');
  
  // 测试美食活动
  const mealActivity = {
    id: 'meal-1',
    name: '一兰拉面',
    name_zh: '一兰拉面',
    type: 'meal',
    category: 'dining',
    description: '著名的日式拉面店',
    location: { name: '一兰拉面新宿店', address: '新宿区' }
  };

  const mealClassification = IntelligentActivityClassifier.classifyActivity(mealActivity);
  
  if (mealClassification.primaryType === 'meal') {
    console.log(`✅ 一兰拉面 正确分类为 meal`);
  } else {
    console.log(`❌ 一兰拉面 应该分类为meal，实际为${mealClassification.primaryType}`);
  }
  
  if (mealClassification.iconType === 'restaurant') {
    console.log(`✅ 一兰拉面 图标正确为 restaurant`);
  } else {
    console.log(`❌ 一兰拉面 图标应该为restaurant，实际为${mealClassification.iconType}`);
  }

  // 测试景点活动
  const attractionActivity = {
    id: 'attraction-1',
    name: '东京塔',
    name_zh: '东京塔',
    type: 'attraction',
    category: 'sightseeing',
    description: '东京著名地标建筑',
    location: { name: '东京塔', address: '港区' }
  };

  const attractionClassification = IntelligentActivityClassifier.classifyActivity(attractionActivity);
  
  if (attractionClassification.primaryType === 'attraction') {
    console.log(`✅ 东京塔 正确分类为 attraction`);
  } else {
    console.log(`❌ 东京塔 应该分类为attraction，实际为${attractionClassification.primaryType}`);
  }
  
} catch (error) {
  console.error('❌ 活动分类测试失败:', error.message);
}

// 测试4: 验证UltraThinkMasterSolverV2是否集成了新组件
console.log('\n🔧 测试4: 验证UltraThinkMasterSolverV2集成');
try {
  const fs = require('fs');
  const path = require('path');
  
  const masterSolverPath = path.join(__dirname, '../services/ultra-think/UltraThinkMasterSolverV2.ts');
  const masterSolverContent = fs.readFileSync(masterSolverPath, 'utf8');
  
  const integrationChecks = [
    { name: 'UnifiedTimeAllocationEngine', pattern: /UnifiedTimeAllocationEngine\.allocateIntelligentTimes/ },
    { name: 'EnhancedTransportationGenerator', pattern: /EnhancedTransportationGenerator\.generateCompleteTransportPlan/ },
    { name: 'IntelligentMealScheduler', pattern: /IntelligentMealScheduler\.scheduleDailyMeals/ },
    { name: 'IntelligentActivityClassifier', pattern: /IntelligentActivityClassifier\.classifyActivity/ }
  ];
  
  for (const { name, pattern } of integrationChecks) {
    if (pattern.test(masterSolverContent)) {
      console.log(`✅ ${name} 已集成到 UltraThinkMasterSolverV2`);
    } else {
      console.log(`❌ ${name} 未集成到 UltraThinkMasterSolverV2`);
    }
  }
  
} catch (error) {
  console.error('❌ UltraThinkMasterSolverV2集成检查失败:', error.message);
}

// 测试5: 验证ActivityCard是否使用了统一预算引擎
console.log('\n🎨 测试5: 验证ActivityCard预算显示修改');
try {
  const fs = require('fs');
  const path = require('path');
  
  const activityCardPath = path.join(__dirname, '../components/journey/ActivityCard.tsx');
  const activityCardContent = fs.readFileSync(activityCardPath, 'utf8');
  
  const uiChecks = [
    { name: 'TrulyUnifiedBudgetEngine导入', pattern: /import.*TrulyUnifiedBudgetEngine/ },
    { name: 'getUnifiedActivityBudget调用', pattern: /getUnifiedActivityBudget/ },
    { name: '免费显示逻辑', pattern: /unifiedBudget\.isFree/ },
    { name: '免费图标', pattern: /"gift"/ }
  ];
  
  for (const { name, pattern } of uiChecks) {
    if (pattern.test(activityCardContent)) {
      console.log(`✅ ${name} 已实现`);
    } else {
      console.log(`❌ ${name} 未实现`);
    }
  }
  
} catch (error) {
  console.error('❌ ActivityCard修改检查失败:', error.message);
}

console.log('\n📊 简化集成测试完成');
console.log('='.repeat(60));
console.log('✅ 如果看到大部分测试通过，说明集成基本成功');
console.log('❌ 如果有测试失败，请检查相应的组件和集成');
console.log('\n💡 建议：');
console.log('1. 运行实际的行程生成测试验证端到端功能');
console.log('2. 检查控制台日志确认新组件被调用');
console.log('3. 在UI中测试免费景点和预算显示');
console.log('4. 验证美食活动的图标显示');