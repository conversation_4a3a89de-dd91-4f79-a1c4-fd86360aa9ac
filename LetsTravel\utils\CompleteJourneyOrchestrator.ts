/**
 * 🎼 完整的行程编排引擎
 * 整合所有优化组件，提供完整的行程规划解决方案
 */

import { PreferenceAwareJourneyPlanner, UserPreferences, PreferenceBasedJourney } from './PreferenceAwareJourneyPlanner';
import { UnifiedTimeAllocationEngine } from '../services/time/UnifiedTimeAllocationEngine';
import { TransportationIntegrator, TransportationPlan } from './TransportationIntegrator';
import { TrulyUnifiedBudgetEngine, TrulyUnifiedBudget } from './TrulyUnifiedBudgetEngine';

export interface CompleteJourneyResult {
  journey: PreferenceBasedJourney;
  schedule: any; // 临时使用any，避免类型冲突
  transportation: TransportationPlan;
  budget: TrulyUnifiedBudget;
  summary: JourneySummary;
  recommendations: string[];
  warnings: string[];
}

export interface JourneySummary {
  totalDays: number;
  totalActivities: number;
  totalBudget: number;
  preferenceAlignment: number;
  schedulingEfficiency: number;
  transportationCoverage: number;
  dataQuality: 'high' | 'medium' | 'low';
}

export class CompleteJourneyOrchestrator {
  
  /**
   * 🎼 完整的行程编排 - 整合所有优化 (支持真实API)
   */
  static async orchestrateCompleteJourney(
    rawActivities: any[],
    userPreferences: UserPreferences,
    tripDuration: number = 3
  ): Promise<CompleteJourneyResult> {
    
    console.log('🎼 开始完整行程编排');
    console.log('📊 输入参数:', {
      rawActivities: rawActivities.length,
      userPreferences,
      tripDuration
    });
    
    const warnings: string[] = [];
    const recommendations: string[] = [];
    
    try {
      // Phase 1: 偏好驱动的行程规划
      console.log('🎯 Phase 1: 偏好驱动行程规划');
      const journey = await PreferenceAwareJourneyPlanner.planJourney(
        rawActivities,
        userPreferences,
        tripDuration,
        userPreferences.destination || '东京'
      );
      
      console.log(`✅ 偏好规划完成: ${journey.activities.length}个活动，对齐度${journey.preferenceAlignment.overallScore}%`);
      
      // Phase 2: 智能时间调度
      console.log('⏰ Phase 2: 智能时间调度 - 使用统一时间分配引擎');
      
      // 转换为统一时间分配引擎所需的格式
      const timeRequirements = journey.activities.map(activity => ({
        id: activity.id,
        name: activity.name,
        type: activity.type || 'attraction',
        category: activity.category || 'sightseeing',
        location: activity.location,
        priority: 5,
        preferenceScore: 0.8,
        isPreferenceMatch: true,
        duration: activity.duration || 90,
        flexibilityLevel: 'flexible' as const,
        optimalTimeSlots: ['morning', 'afternoon'] as const,
        weatherSensitive: false,
        bookingRequired: false,
        cost: activity.cost || 0
      }));
      
      const allocationResult = UnifiedTimeAllocationEngine.allocateIntelligentTimes(
        timeRequirements,
        tripDuration,
        new Date(),
        { pacePreference: 'moderate', mealImportance: 'medium' }
      );

      // 🔧 增强数据验证和错误处理
      if (!allocationResult.success) {
        console.error('❌ 统一时间分配失败，原因:', allocationResult.error || '未知错误');
        warnings.push('时间分配失败，使用基础时间安排');

        // 生成降级的时间安排
        const fallbackSchedule = this.generateFallbackTimeSchedule(timeRequirements, tripDuration);
        const scheduleData = {
          schedule: fallbackSchedule,
          statistics: {
            scheduledActivities: timeRequirements.length,
            totalActivities: timeRequirements.length,
            totalDays: tripDuration,
            averageEfficiency: 0.6 // 降级方案的效率较低
          }
        };

        console.log('🔄 使用降级时间安排');
        return this.continueWithFallbackSchedule(journey, scheduleData, userPreferences, tripDuration, warnings);
      }

      // 验证分配结果的完整性
      if (!allocationResult.allocations || allocationResult.allocations.length === 0) {
        console.error('❌ 时间分配结果为空');
        warnings.push('时间分配结果异常，使用降级方案');
        throw new Error('时间分配结果为空');
      }

      if (allocationResult.warnings && allocationResult.warnings.length > 0) {
        warnings.push(...allocationResult.warnings);
      }

      // 🔧 使用正确的数据结构
      const scheduleData = {
        schedule: allocationResult.allocations,
        statistics: {
          scheduledActivities: allocationResult.summary.totalActivities,
          totalActivities: allocationResult.summary.totalActivities,
          totalDays: allocationResult.summary.totalDays,
          averageEfficiency: allocationResult.summary.averageEfficiency
        }
      };

      console.log(`✅ 时间调度完成: ${scheduleData.statistics.scheduledActivities}/${scheduleData.statistics.totalActivities}个活动已调度`);
      
      // Phase 3: 交通信息集成 - 使用增强交通生成器
      console.log('🚇 Phase 3: 交通信息集成');

      // 🔧 准备交通生成请求数据
      const allActivities = [];
      if (Array.isArray(scheduleData.schedule)) {
        scheduleData.schedule.forEach((dayData: any) => {
          if (dayData && dayData.activities) {
            allActivities.push(...dayData.activities);
          }
        });
      }

      const transportRequest = {
        activities: allActivities,
        destination: '东京',
        totalDays: tripDuration,
        userPreferences: {
          preferredTransport: 'mixed' as const,
          budget: 500,
          comfortLevel: 'standard' as const
        }
      };

      console.log(`🚇 交通请求数据: ${allActivities.length}个活动, ${tripDuration}天`);

      // 使用增强交通生成器
      const { EnhancedTransportationGenerator } = require('../services/transport/EnhancedTransportationGenerator');
      const transportation = EnhancedTransportationGenerator.generateCompleteTransportPlan(transportRequest);

      // 计算交通统计
      const totalTransports = Object.values(transportation).flat().length;
      const totalCost = Object.values(transportation).flat().reduce((sum: number, transport: any) =>
        sum + (transport.cost || 0), 0);

      console.log(`✅ 交通集成完成: ${totalTransports}段交通，总费用RM${totalCost}`);

      // Phase 4: 真正统一的预算计算
      console.log('💰 Phase 4: 真正统一预算计算');

      // 🔧 转换数据格式：从数组转换为按天分组的对象
      const scheduledActivitiesByDay: { [day: number]: any[] } = {};

      if (Array.isArray(scheduleData.schedule)) {
        // 如果schedule是数组，按天分组
        scheduleData.schedule.forEach((dayData: any) => {
          if (dayData && dayData.day && dayData.activities) {
            scheduledActivitiesByDay[dayData.day] = dayData.activities;
          }
        });
      } else if (scheduleData.schedule && typeof scheduleData.schedule === 'object') {
        // 如果schedule已经是按天分组的对象，直接使用
        Object.assign(scheduledActivitiesByDay, scheduleData.schedule);
      }

      // 如果没有按天分组的数据，创建默认分组
      if (Object.keys(scheduledActivitiesByDay).length === 0) {
        console.warn('⚠️ 无法解析时间安排数据，使用降级分组');
        // 将所有活动分配到第1天
        scheduledActivitiesByDay[1] = Array.isArray(scheduleData.schedule) ?
          scheduleData.schedule : [];
      }

      console.log('📊 转换后的活动分组:', {
        totalDays: Object.keys(scheduledActivitiesByDay).length,
        activitiesByDay: Object.entries(scheduledActivitiesByDay).map(([day, activities]) =>
          `Day${day}: ${activities.length}个活动`
        )
      });

      const budget = TrulyUnifiedBudgetEngine.calculateTrulyUnifiedBudget(
        scheduledActivitiesByDay,
        transportation
      );
      
      if (!budget.validation.isConsistent) {
        warnings.push(...budget.validation.warnings);
      }
      
      console.log(`✅ 预算计算完成: 总预算RM${budget.total}，验证${budget.validation.isConsistent ? '通过' : '有警告'}`);
      
      // Phase 5: 生成综合摘要
      const summary = this.generateJourneySummary(journey, scheduleData, transportation, budget);

      // Phase 6: 生成最终建议
      const finalRecommendations = this.generateFinalRecommendations(
        journey,
        scheduleData,
        transportation,
        budget,
        userPreferences
      );
      
      recommendations.push(...finalRecommendations);
      
      console.log('🎉 完整行程编排成功完成');

      // 🔍 [DEBUG] 详细返回数据调试
      const returnData = {
        journey,
        schedule: scheduleData,
        transportation,
        budget,
        summary,
        recommendations,
        warnings
      };

      console.log('🔍 [DEBUG] CompleteJourneyOrchestrator返回数据结构:', {
        hasJourney: !!returnData.journey,
        hasSchedule: !!returnData.schedule,
        scheduleType: typeof returnData.schedule,
        scheduleKeys: returnData.schedule ? Object.keys(returnData.schedule) : [],
        journeyActivitiesCount: returnData.journey?.activities?.length || 0,
        scheduleActivitiesCount: returnData.schedule?.schedule ?
          (Array.isArray(returnData.schedule.schedule) ?
            returnData.schedule.schedule.length :
            Object.keys(returnData.schedule.schedule).length) : 0,
        budgetTotal: returnData.budget?.total || 0,
        summaryTotalActivities: returnData.summary?.totalActivities || 0
      });

      console.log('🔍 [DEBUG] 详细schedule数据:', JSON.stringify(returnData.schedule, null, 2));
      console.log('🔍 [DEBUG] 详细journey活动:', JSON.stringify(returnData.journey?.activities?.slice(0, 3), null, 2));

      return returnData;
      
    } catch (error) {
      console.error('❌ 完整行程编排失败:', error);
      
      // 返回降级结果
      return this.generateFallbackResult(rawActivities, userPreferences, tripDuration, error);
    }
  }
  
  /**
   * 📊 生成行程摘要
   */
  private static generateJourneySummary(
    journey: PreferenceBasedJourney,
    scheduleData: any, // 临时使用any，避免类型冲突
    transportation: TransportationPlan,
    budget: TrulyUnifiedBudget
  ): JourneySummary {

    const totalTransports = Object.values(transportation).flat().length;
    const totalScheduledDays = scheduleData.schedule ? scheduleData.schedule.length : 0;

    return {
      totalDays: totalScheduledDays,
      totalActivities: scheduleData.statistics.scheduledActivities,
      totalBudget: budget.total,
      preferenceAlignment: journey.preferenceAlignment.overallScore,
      schedulingEfficiency: scheduleData.statistics.averageEfficiency || 0,
      transportationCoverage: totalTransports > 0 ? 100 : 0,
      dataQuality: budget.transparency.dataQuality
    };
  }
  
  /**
   * 💡 生成最终建议
   */
  private static generateFinalRecommendations(
    journey: PreferenceBasedJourney,
    scheduleData: any, // 临时使用any，避免类型冲突
    transportation: TransportationPlan,
    budget: TrulyUnifiedBudget,
    userPreferences: UserPreferences
  ): string[] {

    const recommendations: string[] = [];

    // 偏好对齐建议
    if (journey.preferenceAlignment.overallScore >= 80) {
      recommendations.push('🎯 行程与您的偏好高度匹配，建议按计划执行');
    } else if (journey.preferenceAlignment.overallScore >= 60) {
      recommendations.push('👍 行程较好地符合您的偏好，可适当调整');
    } else {
      recommendations.push('💡 建议增加更多符合您偏好的活动');
    }

    // 时间安排建议
    const efficiency = scheduleData.statistics.averageEfficiency * 100 || 0;
    if (efficiency < 80) {
      recommendations.push('⏰ 部分活动未能安排，建议延长行程或减少活动');
    }

    // 简化日程检查，避免访问不存在的属性
    const totalDays = scheduleData.statistics.totalDays || 1;
    const totalActivities = scheduleData.statistics.totalActivities || 0;
    const avgActivitiesPerDay = totalActivities / totalDays;

    if (avgActivitiesPerDay > 8) {
      recommendations.push('🕐 部分日程较满，建议适当安排休息时间');
    }

    // 预算建议
    const dailyAverage = budget.total / totalDays;
    if (dailyAverage > 800) {
      recommendations.push('💰 日均预算较高，可考虑选择更经济的选项');
    } else if (dailyAverage < 200) {
      recommendations.push('💰 预算较为经济，可考虑增加一些优质体验');
    }
    
    // 偏好特定建议
    if (userPreferences.travelStyle?.includes('文化深度')) {
      const culturalScore = journey.preferenceAlignment.culturalDepthScore;
      if (culturalScore < 30) {
        recommendations.push('🏛️ 建议增加更多文化体验活动');
      } else {
        recommendations.push(`🏛️ 文化深度体验占比${culturalScore}%，符合您的偏好`);
      }
    }
    
    if (userPreferences.travelStyle?.includes('美食探索')) {
      const foodScore = journey.preferenceAlignment.foodExplorationScore;
      if (foodScore < 25) {
        recommendations.push('🍽️ 建议增加更多美食探索体验');
      } else {
        recommendations.push(`🍽️ 美食探索体验占比${foodScore}%，符合您的偏好`);
      }
    }
    
    // 交通建议
    const transportStats = TransportationIntegrator.generateTransportStatistics(transportation);
    if (transportStats.methodBreakdown['地铁'] >= 3) {
      recommendations.push('🚇 建议购买地铁一日券，可节省交通费用');
    }
    
    return recommendations;
  }
  
  /**
   * 🔄 生成降级时间安排
   */
  private static generateFallbackTimeSchedule(activities: any[], tripDuration: number): any[] {
    console.log('🔄 生成降级时间安排');

    const activitiesPerDay = Math.ceil(activities.length / tripDuration);
    const fallbackSchedule = [];

    for (let day = 1; day <= tripDuration; day++) {
      const startIndex = (day - 1) * activitiesPerDay;
      const endIndex = Math.min(startIndex + activitiesPerDay, activities.length);
      const dayActivities = activities.slice(startIndex, endIndex);

      const daySchedule = {
        day,
        date: new Date(Date.now() + (day - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        activities: dayActivities.map((activity, index) => ({
          ...activity,
          timeSlot: {
            startTime: this.calculateFallbackTime(index * 2 + 9), // 从9点开始，每个活动间隔2小时
            endTime: this.calculateFallbackTime(index * 2 + 10.5),
            duration: activity.duration || 90,
            confidence: 0.6
          }
        })),
        totalDuration: dayActivities.length * 90,
        efficiency: 0.6
      };

      fallbackSchedule.push(daySchedule);
    }

    return fallbackSchedule;
  }

  /**
   * 🕐 计算降级时间
   */
  private static calculateFallbackTime(hours: number): string {
    const h = Math.floor(hours);
    const m = Math.floor((hours - h) * 60);
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
  }

  /**
   * 🔄 使用降级安排继续流程
   */
  private static continueWithFallbackSchedule(
    journey: any,
    scheduleData: any,
    userPreferences: any,
    tripDuration: number,
    warnings: string[]
  ): CompleteJourneyResult {
    console.log('🔄 使用降级安排继续流程');

    try {
      // 简化的交通和预算计算
      const transportation = {}; // 简化的交通方案

      // 🔧 为降级流程创建正确的预算数据结构
      const scheduledActivitiesByDay: { [day: number]: any[] } = {};

      if (Array.isArray(scheduleData.schedule)) {
        scheduleData.schedule.forEach((dayData: any, index: number) => {
          const dayNumber = dayData.day || (index + 1);
          scheduledActivitiesByDay[dayNumber] = dayData.activities || [];
        });
      }

      // 如果有数据，尝试使用真实的预算计算
      let budget;
      if (Object.keys(scheduledActivitiesByDay).length > 0) {
        try {
          budget = TrulyUnifiedBudgetEngine.calculateTrulyUnifiedBudget(
            scheduledActivitiesByDay,
            transportation
          );
        } catch (budgetError) {
          console.error('❌ 降级预算计算也失败，使用最简预算:', budgetError);
          budget = {
            total: 1000 * tripDuration,
            validation: { isConsistent: false, warnings: ['使用最简预算估算'] },
            transparency: { dataQuality: 0.3 }
          };
        }
      } else {
        budget = {
          total: 1000 * tripDuration, // 简化的预算估算
          validation: { isConsistent: false, warnings: ['使用降级预算估算'] },
          transparency: { dataQuality: 0.5 }
        };
      }

      const summary = this.generateJourneySummary(journey, scheduleData, transportation, budget);
      const recommendations = ['系统使用了降级方案，建议重新生成行程以获得更好的体验'];

      return {
        journey,
        schedule: scheduleData,
        transportation,
        budget,
        summary,
        recommendations,
        warnings
      };

    } catch (error) {
      console.error('❌ 降级流程也失败了:', error);
      throw error;
    }
  }

  /**
   * 🔧 生成降级结果
   */
  private static generateFallbackResult(
    rawActivities: any[],
    userPreferences: UserPreferences,
    tripDuration: number,
    error: any
  ): CompleteJourneyResult {
    
    console.log('🔧 生成降级结果');
    
    // 简化的降级处理
    const fallbackActivities = rawActivities.slice(0, Math.min(rawActivities.length, tripDuration * 4));
    
    return {
      journey: {
        activities: fallbackActivities.map((activity, index) => ({
          id: `fallback_${index}`,
          name: activity.name || `活动${index + 1}`,
          type: activity.type || 'attraction',
          duration: activity.duration || 90,
          cost: activity.cost || 30,
          preferenceScore: 1.0,
          isPreferenceMatch: false
        })),
        preferenceAlignment: {
          overallScore: 50,
          culturalDepthScore: 0,
          foodExplorationScore: 0,
          recommendations: ['系统遇到问题，使用了简化的行程安排']
        },
        recommendations: ['建议重新生成行程以获得更好的体验'],
        generatedContent: []
      },
      schedule: {
        schedule: {},
        statistics: {
          totalActivities: fallbackActivities.length,
          scheduledActivities: 0,
          unscheduledActivities: fallbackActivities.length,
          dailyHours: {},
          efficiency: 0
        },
        warnings: ['系统遇到问题，时间调度失败']
      },
      transportation: {},
      budget: {
        total: fallbackActivities.reduce((sum, activity) => sum + (activity.cost || 30), 0),
        byDay: {},
        byCategory: {},
        breakdown: [],
        validation: {
          isConsistent: false,
          warnings: ['预算计算失败，使用估算值'],
          sources: ['fallback'],
          checksPerformed: [],
          totalValidation: { calculated: 0, verified: 0, difference: 0 }
        },
        transparency: {
          calculationSource: 'fallback',
          lastUpdated: new Date().toISOString(),
          methodology: '简化估算',
          assumptions: ['使用默认费用估算'],
          dataQuality: 'low'
        }
      },
      summary: {
        totalDays: tripDuration,
        totalActivities: fallbackActivities.length,
        totalBudget: fallbackActivities.reduce((sum, activity) => sum + (activity.cost || 30), 0),
        preferenceAlignment: 50,
        schedulingEfficiency: 0,
        transportationCoverage: 0,
        dataQuality: 'low'
      },
      recommendations: [
        '系统遇到问题，建议重新生成行程',
        '如问题持续，请联系技术支持'
      ],
      warnings: [
        '行程编排过程中遇到错误',
        '当前显示的是简化版本',
        `错误详情: ${error.message || '未知错误'}`
      ]
    };
  }
  
  /**
   * 📋 生成完整报告
   */
  static generateCompleteReport(result: CompleteJourneyResult): string {
    const report = [
      '🎼 完整行程编排报告',
      '='.repeat(50),
      '',
      '📊 行程摘要:',
      `   🗓️ 总天数: ${result.summary.totalDays}天`,
      `   🎯 总活动: ${result.summary.totalActivities}个`,
      `   💰 总预算: RM${result.summary.totalBudget}`,
      `   🎯 偏好对齐度: ${result.summary.preferenceAlignment}%`,
      `   ⏰ 调度效率: ${result.summary.schedulingEfficiency}%`,
      `   🚇 交通覆盖: ${result.summary.transportationCoverage}%`,
      `   📊 数据质量: ${result.summary.dataQuality}`,
      '',
      '💡 主要建议:',
      ...result.recommendations.map(rec => `   • ${rec}`),
      '',
      '⚠️ 注意事项:',
      ...result.warnings.map(warning => `   • ${warning}`),
      '',
      '🔍 详细预算信息:',
      TrulyUnifiedBudgetEngine.generateBudgetReport(result.budget),
      '',
      '📈 生成时间:',
      `   ${new Date().toLocaleString()}`
    ];
    
    return report.join('\n');
  }
}
