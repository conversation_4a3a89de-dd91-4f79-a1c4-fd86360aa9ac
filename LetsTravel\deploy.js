#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始 Trekmate 完整部署流程...\n');

// 部署配置
const config = {
  // Expo/EAS 配置
  expo: {
    buildProfiles: ['development', 'preview', 'production'],
    platforms: ['android', 'ios']
  },
  // Vercel 配置
  vercel: {
    projectPath: './my-vercel-project',
    domain: 'trekmate.vercel.app'
  },
  // Backend 配置
  backend: {
    schedulerPath: '../scheduler',
    scraperPath: '../backend_scraper'
  }
};

function runCommand(command, options = {}) {
  console.log(`📋 执行: ${command}`);
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: options.cwd || process.cwd(),
      ...options 
    });
    return result;
  } catch (error) {
    console.error(`❌ 命令执行失败: ${command}`);
    console.error(error.message);
    if (!options.continueOnError) {
      process.exit(1);
    }
  }
}

function checkPrerequisites() {
  console.log('🔍 检查部署先决条件...');
  
  // 检查必要的CLI工具
  const requiredTools = [
    { command: 'expo --version', name: 'Expo CLI' },
    { command: 'eas --version', name: 'EAS CLI' },
    { command: 'vercel --version', name: 'Vercel CLI', optional: true }
  ];
  
  for (const tool of requiredTools) {
    try {
      runCommand(tool.command, { stdio: 'pipe' });
      console.log(`✅ ${tool.name} 已安装`);
    } catch (error) {
      if (tool.optional) {
        console.log(`⚠️  ${tool.name} 未安装 (可选)`);
      } else {
        console.error(`❌ ${tool.name} 未安装，请先安装`);
        process.exit(1);
      }
    }
  }
}

function setupEnvironment() {
  console.log('\n🔧 设置环境配置...');
  
  // 检查环境文件
  const envFiles = ['.env', '.env.local', '.env.production'];
  
  for (const envFile of envFiles) {
    if (fs.existsSync(envFile)) {
      console.log(`✅ 找到环境文件: ${envFile}`);
    } else {
      console.log(`⚠️  未找到环境文件: ${envFile}`);
    }
  }
  
  // 创建部署环境文件模板
  if (!fs.existsSync('.env.deploy')) {
    const envTemplate = `# LetsTravel 部署环境配置
EXPO_PROJECT_ID=9b03ac62-03f6-4a91-af81-750047aa5bf2
EAS_BUILD_PROFILE=production
VERCEL_PROJECT_NAME=letstravel
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
`;
    
    fs.writeFileSync('.env.deploy', envTemplate);
    console.log('📝 创建部署环境配置模板: .env.deploy');
  }
}

function buildMobileApp() {
  console.log('\n📱 构建移动应用...');
  
  // 安装依赖
  console.log('📦 安装依赖...');
  runCommand('npm install');
  
  // 修复依赖问题
  console.log('🔧 修复依赖问题...');
  runCommand('npx expo install --fix', { continueOnError: true });
  
  // 类型检查
  console.log('🔍 类型检查...');
  runCommand('npm run type-check', { continueOnError: true });
  
  // 预览构建 (Android APK)
  console.log('🏗️  构建预览版本...');
  runCommand('eas build --profile preview --platform android --local', { continueOnError: true });
  
  console.log('✅ 移动应用构建完成');
}

function deployWeb() {
  console.log('\n🌐 部署Web应用...');
  
  const vercelPath = config.vercel.projectPath;
  
  if (!fs.existsSync(vercelPath)) {
    console.log('⚠️  Vercel项目目录不存在，跳过Web部署');
    return;
  }
  
  // 构建Web版本
  console.log('🏗️  构建Web版本...');
  runCommand('npx expo export:web', { continueOnError: true });
  
  // 复制构建文件到Vercel项目
  if (fs.existsSync('web-build')) {
    console.log('📁 复制构建文件到Vercel项目...');
    runCommand(`xcopy /E /I /Y web-build\\* ${vercelPath}\\`, { continueOnError: true });
  }
  
  // 部署到Vercel
  if (fs.existsSync('vercel')) {
    console.log('🚀 部署到Vercel...');
    runCommand('vercel --prod', { cwd: vercelPath, continueOnError: true });
  } else {
    console.log('⚠️  Vercel CLI未安装，请手动部署');
  }
  
  console.log('✅ Web应用部署完成');
}

function deployBackend() {
  console.log('\n🔧 部署后端服务...');
  
  // 检查后端目录
  const backendPaths = ['../backend_scraper', '../scheduler'];
  
  for (const backendPath of backendPaths) {
    if (fs.existsSync(backendPath)) {
      console.log(`📁 发现后端服务: ${backendPath}`);
      
      // 安装后端依赖
      if (fs.existsSync(path.join(backendPath, 'requirements.txt'))) {
        console.log('🐍 安装Python依赖...');
        runCommand('pip install -r requirements.txt', { 
          cwd: backendPath, 
          continueOnError: true 
        });
      }
      
      if (fs.existsSync(path.join(backendPath, 'package.json'))) {
        console.log('📦 安装Node依赖...');
        runCommand('npm install', { 
          cwd: backendPath, 
          continueOnError: true 
        });
      }
    }
  }
  
  console.log('✅ 后端服务配置完成');
}

function generateDeploymentReport() {
  console.log('\n📊 生成部署报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    status: 'completed',
    components: {
      mobileApp: {
        platform: 'Expo/EAS',
        buildProfile: 'preview',
        status: 'built'
      },
      webApp: {
        platform: 'Vercel',
        domain: config.vercel.domain,
        status: 'deployed'
      },
      backend: {
        scraper: 'configured',
        scheduler: 'configured',
        status: 'ready'
      }
    },
    nextSteps: [
      '1. 配置环境变量 (.env.deploy)',
      '2. 上传应用到应用商店',
      '3. 配置域名和SSL',
      '4. 设置监控和日志',
      '5. 运行端到端测试'
    ]
  };
  
  fs.writeFileSync('deployment-report.json', JSON.stringify(report, null, 2));
  
  console.log('\n🎉 部署完成！');
  console.log('📋 部署报告已生成: deployment-report.json');
  console.log('\n后续步骤:');
  report.nextSteps.forEach((step, index) => {
    console.log(`   ${step}`);
  });
}

// 主部署流程
async function main() {
  try {
    checkPrerequisites();
    setupEnvironment();
    buildMobileApp();
    deployWeb();
    deployBackend();
    generateDeploymentReport();
    
    console.log('\n🎊 LetsTravel 部署流程完成！');
  } catch (error) {
    console.error('\n❌ 部署过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { main, config }; 