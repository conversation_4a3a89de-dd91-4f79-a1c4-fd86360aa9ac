/**
 * 📊 内容质量验证器 - Ultra Think系统性修复
 * 验证活动内容质量，确保使用具体名称而非通用模板
 */

export interface ContentQualityMetrics {
  overallScore: number;
  nameQuality: number;
  descriptionQuality: number;
  highlightsQuality: number;
  tipsQuality: number;
  isGeneric: boolean;
  hasRealPlaceName: boolean;
  issues: string[];
  suggestions: string[];
}

export interface ContentQualityReport {
  totalActivities: number;
  highQualityCount: number;
  mediumQualityCount: number;
  lowQualityCount: number;
  genericContentCount: number;
  averageScore: number;
  detailedResults: Array<{
    activityId: string;
    activityName: string;
    metrics: ContentQualityMetrics;
  }>;
  recommendations: string[];
}

export class ContentQualityValidator {
  
  /**
   * 📊 验证单个活动的内容质量
   */
  static validateActivityContent(activity: any): ContentQualityMetrics {
    const issues: string[] = [];
    const suggestions: string[] = [];
    
    // 1. 验证名称质量
    const nameQuality = this.validateNameQuality(activity, issues, suggestions);
    
    // 2. 验证描述质量
    const descriptionQuality = this.validateDescriptionQuality(activity, issues, suggestions);
    
    // 3. 验证亮点质量
    const highlightsQuality = this.validateHighlightsQuality(activity, issues, suggestions);
    
    // 4. 验证建议质量
    const tipsQuality = this.validateTipsQuality(activity, issues, suggestions);
    
    // 5. 检查是否为通用内容
    const isGeneric = this.isGenericContent(activity);
    if (isGeneric) {
      issues.push('使用了通用模板内容');
      suggestions.push('使用具体的地点名称和描述');
    }
    
    // 6. 检查是否有真实地点名称
    const hasRealPlaceName = this.hasRealPlaceName(activity);
    if (!hasRealPlaceName) {
      issues.push('缺乏具体的地点名称');
      suggestions.push('提供真实存在的地点名称');
    }
    
    // 计算总体评分
    const overallScore = (nameQuality + descriptionQuality + highlightsQuality + tipsQuality) / 4;
    
    return {
      overallScore,
      nameQuality,
      descriptionQuality,
      highlightsQuality,
      tipsQuality,
      isGeneric,
      hasRealPlaceName,
      issues,
      suggestions
    };
  }

  /**
   * 🏷️ 验证名称质量
   */
  private static validateNameQuality(activity: any, issues: string[], suggestions: string[]): number {
    const name = activity.name || activity.name_zh || '';
    let score = 0;
    
    if (!name) {
      issues.push('缺少活动名称');
      suggestions.push('添加具体的活动名称');
      return 0;
    }
    
    // 检查通用模板模式
    const genericPatterns = [
      /\{destination\}/i,
      /文化中心$/,
      /市场$/,
      /公园$/,
      /博物馆$/,
      /购物区$/,
      /观景台$/,
      /寺庙$/
    ];
    
    const isGenericName = genericPatterns.some(pattern => pattern.test(name));
    if (isGenericName) {
      issues.push('使用了通用的地点名称模板');
      suggestions.push('使用具体的地点名称，如"双子塔购物中心"而非"吉隆坡购物区"');
      score = 0.3;
    } else {
      score = 0.8;
    }
    
    // 检查是否包含具体信息
    if (name.length > 10 && !isGenericName) {
      score += 0.2;
    }
    
    // 检查是否有评级或特色标识
    if (name.includes('星级') || name.includes('推荐') || name.includes('老字号')) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 📝 验证描述质量
   */
  private static validateDescriptionQuality(activity: any, issues: string[], suggestions: string[]): number {
    const description = activity.description || activity.description_zh || '';
    let score = 0;
    
    if (!description) {
      issues.push('缺少活动描述');
      suggestions.push('添加详细的活动描述');
      return 0;
    }
    
    // 检查描述长度
    if (description.length < 20) {
      issues.push('描述过于简短');
      suggestions.push('提供更详细的描述（建议50-80字）');
      score = 0.3;
    } else if (description.length >= 50) {
      score = 0.8;
    } else {
      score = 0.6;
    }
    
    // 检查通用描述模式
    const genericDescriptions = [
      '探索.*独特魅力',
      '精彩体验',
      '深度体验.*独特魅力',
      '值得一游',
      '不容错过'
    ];
    
    const hasGenericDescription = genericDescriptions.some(pattern => 
      new RegExp(pattern).test(description)
    );
    
    if (hasGenericDescription) {
      issues.push('使用了通用描述模板');
      suggestions.push('提供更具体和个性化的描述');
      score *= 0.5;
    }
    
    // 检查是否包含具体信息
    if (description.includes('评分') || description.includes('评论') || /\d+星/.test(description)) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * ✨ 验证亮点质量
   */
  private static validateHighlightsQuality(activity: any, issues: string[], suggestions: string[]): number {
    const highlights = activity.details?.highlights || [];
    let score = 0;
    
    if (!highlights || highlights.length === 0) {
      issues.push('缺少活动亮点');
      suggestions.push('添加3-5个具体的活动亮点');
      return 0;
    }
    
    // 检查亮点数量
    if (highlights.length < 3) {
      issues.push('亮点数量不足');
      suggestions.push('至少提供3个亮点');
      score = 0.5;
    } else if (highlights.length >= 5) {
      score = 0.9;
    } else {
      score = 0.7;
    }
    
    // 检查亮点质量
    const genericHighlights = ['精彩体验', '难忘回忆', '文化探索', '独特体验'];
    const genericCount = highlights.filter((highlight: string) => 
      genericHighlights.some(generic => highlight.includes(generic))
    ).length;
    
    if (genericCount > highlights.length * 0.5) {
      issues.push('亮点过于通用');
      suggestions.push('提供更具体的亮点，如"百年传承工艺"、"海景用餐环境"');
      score *= 0.6;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 💡 验证建议质量
   */
  private static validateTipsQuality(activity: any, issues: string[], suggestions: string[]): number {
    const tips = activity.details?.tips || [];
    let score = 0;
    
    if (!tips || tips.length === 0) {
      issues.push('缺少实用建议');
      suggestions.push('添加2-3个实用的旅行建议');
      return 0;
    }
    
    // 检查建议数量
    if (tips.length < 2) {
      issues.push('建议数量不足');
      suggestions.push('至少提供2个实用建议');
      score = 0.5;
    } else if (tips.length >= 3) {
      score = 0.9;
    } else {
      score = 0.7;
    }
    
    // 检查建议质量
    const genericTips = ['提前预订', '注意开放时间', '穿舒适鞋子'];
    const specificTips = tips.filter((tip: string) => 
      !genericTips.some(generic => tip.includes(generic))
    );
    
    if (specificTips.length === 0) {
      issues.push('建议过于通用');
      suggestions.push('提供更具体的建议，如"早上海鲜最新鲜"、"可以讨价还价"');
      score *= 0.6;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 🔍 检查是否为通用内容
   */
  private static isGenericContent(activity: any): boolean {
    const name = activity.name || activity.name_zh || '';
    const description = activity.description || activity.description_zh || '';
    
    const genericPatterns = [
      /\{destination\}/i,
      /东京\s*lunch/i,
      /文化中心$/,
      /市场$/,
      /公园$/,
      /博物馆$/,
      /精彩体验/,
      /独特魅力/,
      /深度体验.*独特魅力/
    ];

    return genericPatterns.some(pattern => 
      pattern.test(name) || pattern.test(description)
    );
  }

  /**
   * 🏢 检查是否有真实地点名称
   */
  private static hasRealPlaceName(activity: any): boolean {
    const name = activity.name || activity.name_zh || '';
    
    // 真实地点名称的特征
    const realPlaceIndicators = [
      /\d+星/, // 星级
      /老字号/, // 老字号
      /广场/, // 广场
      /大厦/, // 大厦
      /中心(?!$)/, // 中心但不是结尾
      /酒店/, // 酒店
      /餐厅/, // 餐厅
      /\w+\s+\w+/, // 包含空格的复合名称
      activity.metadata?.isRealPlace === true // 标记为真实地点
    ];

    return realPlaceIndicators.some(indicator => 
      typeof indicator === 'boolean' ? indicator : indicator.test(name)
    );
  }

  /**
   * 📊 批量验证活动内容质量
   */
  static validateActivitiesContent(activities: any[]): ContentQualityReport {
    console.log(`📊 开始验证${activities.length}个活动的内容质量`);
    
    const detailedResults = activities.map(activity => ({
      activityId: activity.id,
      activityName: activity.name || activity.name_zh,
      metrics: this.validateActivityContent(activity)
    }));

    // 统计质量分布
    let highQualityCount = 0;
    let mediumQualityCount = 0;
    let lowQualityCount = 0;
    let genericContentCount = 0;
    let totalScore = 0;

    detailedResults.forEach(result => {
      const score = result.metrics.overallScore;
      totalScore += score;
      
      if (score >= 0.8) {
        highQualityCount++;
      } else if (score >= 0.6) {
        mediumQualityCount++;
      } else {
        lowQualityCount++;
      }
      
      if (result.metrics.isGeneric) {
        genericContentCount++;
      }
    });

    const averageScore = totalScore / activities.length;

    // 生成建议
    const recommendations = this.generateRecommendations(
      detailedResults,
      highQualityCount,
      mediumQualityCount,
      lowQualityCount,
      genericContentCount
    );

    const report: ContentQualityReport = {
      totalActivities: activities.length,
      highQualityCount,
      mediumQualityCount,
      lowQualityCount,
      genericContentCount,
      averageScore,
      detailedResults,
      recommendations
    };

    console.log(`✅ 内容质量验证完成: 平均分${(averageScore * 100).toFixed(1)}%, 高质量${highQualityCount}个, 通用内容${genericContentCount}个`);

    return report;
  }

  /**
   * 💡 生成改进建议
   */
  private static generateRecommendations(
    results: any[],
    highQuality: number,
    mediumQuality: number,
    lowQuality: number,
    generic: number
  ): string[] {
    const recommendations: string[] = [];
    
    if (generic > 0) {
      recommendations.push(`发现${generic}个通用内容，建议使用具体的地点名称和描述`);
    }
    
    if (lowQuality > 0) {
      recommendations.push(`${lowQuality}个活动内容质量较低，需要重点改进`);
    }
    
    if (mediumQuality > highQuality) {
      recommendations.push('大部分活动内容质量中等，建议进一步优化描述和亮点');
    }
    
    // 具体问题建议
    const commonIssues = new Map<string, number>();
    results.forEach(result => {
      result.metrics.issues.forEach((issue: string) => {
        commonIssues.set(issue, (commonIssues.get(issue) || 0) + 1);
      });
    });
    
    const topIssues = Array.from(commonIssues.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);
    
    topIssues.forEach(([issue, count]) => {
      recommendations.push(`${count}个活动存在"${issue}"问题，需要统一改进`);
    });
    
    return recommendations;
  }

  /**
   * 📋 生成质量报告
   */
  static generateQualityReport(report: ContentQualityReport): string {
    const lines: string[] = [];
    
    lines.push('📊 活动内容质量报告');
    lines.push('='.repeat(40));
    lines.push(`总活动数: ${report.totalActivities}`);
    lines.push(`平均质量分: ${(report.averageScore * 100).toFixed(1)}%`);
    lines.push('');
    
    lines.push('📈 质量分布:');
    lines.push(`  高质量 (≥80%): ${report.highQualityCount} (${((report.highQualityCount / report.totalActivities) * 100).toFixed(1)}%)`);
    lines.push(`  中等质量 (60-79%): ${report.mediumQualityCount} (${((report.mediumQualityCount / report.totalActivities) * 100).toFixed(1)}%)`);
    lines.push(`  低质量 (<60%): ${report.lowQualityCount} (${((report.lowQualityCount / report.totalActivities) * 100).toFixed(1)}%)`);
    lines.push(`  通用内容: ${report.genericContentCount} (${((report.genericContentCount / report.totalActivities) * 100).toFixed(1)}%)`);
    lines.push('');
    
    if (report.recommendations.length > 0) {
      lines.push('💡 改进建议:');
      report.recommendations.forEach(rec => lines.push(`  • ${rec}`));
      lines.push('');
    }
    
    // 显示质量最低的活动
    const lowQualityActivities = report.detailedResults
      .filter(r => r.metrics.overallScore < 0.6)
      .sort((a, b) => a.metrics.overallScore - b.metrics.overallScore)
      .slice(0, 5);
    
    if (lowQualityActivities.length > 0) {
      lines.push('⚠️ 需要重点改进的活动:');
      lowQualityActivities.forEach(activity => {
        lines.push(`  • ${activity.activityName} (${(activity.metrics.overallScore * 100).toFixed(1)}%)`);
        activity.metrics.issues.slice(0, 2).forEach((issue: string) => {
          lines.push(`    - ${issue}`);
        });
      });
    }
    
    return lines.join('\n');
  }
}
