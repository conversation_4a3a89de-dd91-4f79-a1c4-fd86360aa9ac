/**
 * 🔗 数据融合引擎
 * 
 * 实现多源数据智能合并，基于名称、地址、坐标相似度匹配不同API的数据
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 接口定义 =====

export interface DataSource {
  id: string;
  provider: string;
  data: any;
  confidence: number;
  lastUpdated: Date;
  metadata: SourceMetadata;
}

export interface SourceMetadata {
  reliability: number;
  completeness: number;
  freshness: number;
  coverage: string[];
}

export interface FusionResult {
  id: string;
  fusedData: any;
  sources: DataSource[];
  confidence: number;
  fusionMethod: string;
  conflicts: DataConflict[];
  quality: QualityMetrics;
}

export interface DataConflict {
  field: string;
  values: Array<{ source: string; value: any; confidence: number }>;
  resolution: 'highest_confidence' | 'most_recent' | 'majority_vote' | 'manual';
  resolvedValue: any;
}

export interface QualityMetrics {
  accuracy: number;
  completeness: number;
  consistency: number;
  timeliness: number;
  overall: number;
}

export interface FusionConfig {
  strategy: 'conservative' | 'aggressive' | 'balanced';
  conflictResolution: 'auto' | 'manual';
  qualityThreshold: number;
  sourceWeights: Record<string, number>;
  fieldPriorities: Record<string, number>;
}

// ===== 数据融合引擎 =====

export class DataFusionEngine {
  private static instance: DataFusionEngine;
  private config: FusionConfig;
  private sourceReliability: Map<string, number> = new Map();

  private constructor() {
    this.config = {
      strategy: 'balanced',
      conflictResolution: 'auto',
      qualityThreshold: 0.7,
      sourceWeights: {
        'osm': 0.8,
        'yelp': 0.9,
        'google': 0.95,
        'wikipedia': 0.85,
        'nominatim': 0.8,
        'cache': 0.6,
        'ai_generated': 0.5
      },
      fieldPriorities: {
        'name': 1.0,
        'coordinates': 0.95,
        'address': 0.9,
        'rating': 0.85,
        'phone': 0.8,
        'website': 0.75,
        'description': 0.7,
        'opening_hours': 0.8,
        'price': 0.85
      }
    };

    this.initializeSourceReliability();
  }

  /**
   * 🎯 获取单例实例
   */
  static getInstance(): DataFusionEngine {
    if (!DataFusionEngine.instance) {
      DataFusionEngine.instance = new DataFusionEngine();
    }
    return DataFusionEngine.instance;
  }

  /**
   * 🔗 融合多源数据
   */
  async fuseData(sources: DataSource[]): Promise<FusionResult> {
    if (sources.length === 0) {
      throw new Error('至少需要一个数据源');
    }

    if (sources.length === 1) {
      return this.createSingleSourceResult(sources[0]);
    }

    // 1. 预处理数据源
    const processedSources = this.preprocessSources(sources);

    // 2. 检测数据冲突
    const conflicts = this.detectConflicts(processedSources);

    // 3. 解决冲突
    const resolvedConflicts = await this.resolveConflicts(conflicts);

    // 4. 融合数据
    const fusedData = this.performFusion(processedSources, resolvedConflicts);

    // 5. 计算质量指标
    const quality = this.calculateQualityMetrics(fusedData, processedSources);

    // 6. 计算整体置信度
    const confidence = this.calculateOverallConfidence(processedSources, quality);

    return {
      id: `fused_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fusedData,
      sources: processedSources,
      confidence,
      fusionMethod: this.config.strategy,
      conflicts: resolvedConflicts,
      quality
    };
  }

  /**
   * 🔍 匹配相似数据源
   */
  async matchSimilarSources(
    candidates: DataSource[],
    threshold: number = 0.8
  ): Promise<Array<DataSource[]>> {
    
    const groups: Array<DataSource[]> = [];
    const processed = new Set<string>();

    for (const candidate of candidates) {
      if (processed.has(candidate.id)) continue;

      const group = [candidate];
      processed.add(candidate.id);

      // 查找相似的数据源
      for (const other of candidates) {
        if (processed.has(other.id)) continue;

        const similarity = this.calculateSimilarity(candidate, other);
        if (similarity >= threshold) {
          group.push(other);
          processed.add(other.id);
        }
      }

      groups.push(group);
    }

    return groups;
  }

  /**
   * 📊 批量融合数据
   */
  async batchFuseData(
    sourceGroups: Array<DataSource[]>
  ): Promise<FusionResult[]> {
    
    const results: FusionResult[] = [];
    
    for (const sources of sourceGroups) {
      try {
        const fusionResult = await this.fuseData(sources);
        results.push(fusionResult);
      } catch (error) {
        console.error('批量融合失败:', error);
        // 创建降级结果
        if (sources.length > 0) {
          results.push(this.createSingleSourceResult(sources[0]));
        }
      }
    }

    return results;
  }

  // ===== 私有方法 =====

  /**
   * 🔄 预处理数据源
   */
  private preprocessSources(sources: DataSource[]): DataSource[] {
    return sources.map(source => ({
      ...source,
      confidence: this.adjustConfidenceBySource(source),
      data: this.normalizeData(source.data, source.provider)
    })).sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * ⚡ 标准化数据
   */
  private normalizeData(data: any, provider: string): any {
    const normalized = { ...data };

    // 标准化坐标
    if (data.lat && data.lon) {
      normalized.latitude = parseFloat(data.lat);
      normalized.longitude = parseFloat(data.lon);
      delete normalized.lat;
      delete normalized.lon;
    }

    // 标准化名称
    if (data.title && !data.name) {
      normalized.name = data.title;
      delete normalized.title;
    }

    // 标准化评分
    if (provider === 'yelp' && data.rating) {
      normalized.rating = parseFloat(data.rating);
    }

    // 标准化价格
    if (provider === 'yelp' && data.price) {
      normalized.priceLevel = data.price.length;
    }

    // 标准化地址
    if (data.location && data.location.display_address) {
      normalized.address = data.location.display_address.join(', ');
    }

    return normalized;
  }

  /**
   * 🔍 检测数据冲突
   */
  private detectConflicts(sources: DataSource[]): DataConflict[] {
    const conflicts: DataConflict[] = [];
    const fields = this.extractAllFields(sources);

    for (const field of fields) {
      const values = this.extractFieldValues(sources, field);
      
      if (values.length > 1 && this.hasConflict(values)) {
        conflicts.push({
          field,
          values,
          resolution: this.determineResolutionStrategy(field, values),
          resolvedValue: null
        });
      }
    }

    return conflicts;
  }

  /**
   * 🔧 解决冲突
   */
  private async resolveConflicts(conflicts: DataConflict[]): Promise<DataConflict[]> {
    const resolved: DataConflict[] = [];

    for (const conflict of conflicts) {
      const resolvedConflict = { ...conflict };

      switch (conflict.resolution) {
        case 'highest_confidence':
          resolvedConflict.resolvedValue = this.resolveByHighestConfidence(conflict.values);
          break;
        
        case 'most_recent':
          resolvedConflict.resolvedValue = this.resolveByMostRecent(conflict.values);
          break;
        
        case 'majority_vote':
          resolvedConflict.resolvedValue = this.resolveByMajorityVote(conflict.values);
          break;
        
        default:
          resolvedConflict.resolvedValue = conflict.values[0].value;
      }

      resolved.push(resolvedConflict);
    }

    return resolved;
  }

  /**
   * 🔗 执行数据融合
   */
  private performFusion(
    sources: DataSource[],
    resolvedConflicts: DataConflict[]
  ): any {
    
    const fusedData: any = {};
    const conflictMap = new Map(
      resolvedConflicts.map(c => [c.field, c.resolvedValue])
    );

    // 1. 从最高置信度的源开始
    const primarySource = sources[0];
    Object.assign(fusedData, primarySource.data);

    // 2. 应用冲突解决结果
    for (const [field, value] of conflictMap) {
      fusedData[field] = value;
    }

    // 3. 补充缺失字段
    for (const source of sources) {
      for (const [key, value] of Object.entries(source.data)) {
        if (fusedData[key] === undefined && value !== null && value !== undefined) {
          fusedData[key] = value;
        }
      }
    }

    // 4. 添加融合元数据
    fusedData._fusion = {
      sources: sources.map(s => s.provider),
      timestamp: new Date(),
      method: this.config.strategy
    };

    return fusedData;
  }

  /**
   * 📊 计算质量指标
   */
  private calculateQualityMetrics(
    fusedData: any,
    sources: DataSource[]
  ): QualityMetrics {
    
    const accuracy = this.calculateAccuracy(fusedData, sources);
    const completeness = this.calculateCompleteness(fusedData);
    const consistency = this.calculateConsistency(sources);
    const timeliness = this.calculateTimeliness(sources);
    
    const overall = (accuracy * 0.3 + completeness * 0.25 + consistency * 0.25 + timeliness * 0.2);

    return {
      accuracy,
      completeness,
      consistency,
      timeliness,
      overall
    };
  }

  /**
   * 📈 计算相似度
   */
  private calculateSimilarity(source1: DataSource, source2: DataSource): number {
    let similarity = 0;
    let factors = 0;

    // 名称相似度
    if (source1.data.name && source2.data.name) {
      similarity += this.calculateStringSimilarity(source1.data.name, source2.data.name) * 0.4;
      factors += 0.4;
    }

    // 坐标相似度
    if (this.hasCoordinates(source1.data) && this.hasCoordinates(source2.data)) {
      const distance = this.calculateDistance(
        source1.data.latitude, source1.data.longitude,
        source2.data.latitude, source2.data.longitude
      );
      const coordSimilarity = Math.max(0, 1 - distance / 100); // 100米内认为相似
      similarity += coordSimilarity * 0.4;
      factors += 0.4;
    }

    // 地址相似度
    if (source1.data.address && source2.data.address) {
      similarity += this.calculateStringSimilarity(source1.data.address, source2.data.address) * 0.2;
      factors += 0.2;
    }

    return factors > 0 ? similarity / factors : 0;
  }

  // ===== 辅助方法 =====

  private initializeSourceReliability(): void {
    this.sourceReliability.set('google', 0.95);
    this.sourceReliability.set('yelp', 0.9);
    this.sourceReliability.set('osm', 0.8);
    this.sourceReliability.set('wikipedia', 0.85);
    this.sourceReliability.set('nominatim', 0.8);
    this.sourceReliability.set('cache', 0.6);
    this.sourceReliability.set('ai_generated', 0.5);
  }

  private adjustConfidenceBySource(source: DataSource): number {
    const baseReliability = this.sourceReliability.get(source.provider) || 0.5;
    const ageWeight = this.calculateAgeWeight(source.lastUpdated);
    const metadataWeight = this.calculateMetadataWeight(source.metadata);
    
    return Math.min(1.0, source.confidence * baseReliability * ageWeight * metadataWeight);
  }

  private calculateAgeWeight(lastUpdated: Date): number {
    const ageInDays = (Date.now() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24);
    if (ageInDays <= 1) return 1.0;
    if (ageInDays <= 7) return 0.9;
    if (ageInDays <= 30) return 0.8;
    if (ageInDays <= 90) return 0.7;
    return 0.6;
  }

  private calculateMetadataWeight(metadata: SourceMetadata): number {
    return (metadata.reliability + metadata.completeness + metadata.freshness) / 3;
  }

  private extractAllFields(sources: DataSource[]): string[] {
    const fields = new Set<string>();
    for (const source of sources) {
      Object.keys(source.data).forEach(key => fields.add(key));
    }
    return Array.from(fields);
  }

  private extractFieldValues(sources: DataSource[], field: string): Array<{ source: string; value: any; confidence: number }> {
    return sources
      .filter(source => source.data[field] !== undefined)
      .map(source => ({
        source: source.provider,
        value: source.data[field],
        confidence: source.confidence
      }));
  }

  private hasConflict(values: Array<{ source: string; value: any; confidence: number }>): boolean {
    if (values.length <= 1) return false;
    
    const firstValue = values[0].value;
    return values.some(v => !this.areValuesEqual(v.value, firstValue));
  }

  private areValuesEqual(value1: any, value2: any): boolean {
    if (typeof value1 === 'string' && typeof value2 === 'string') {
      return this.calculateStringSimilarity(value1, value2) > 0.9;
    }
    if (typeof value1 === 'number' && typeof value2 === 'number') {
      return Math.abs(value1 - value2) < 0.01;
    }
    return value1 === value2;
  }

  private determineResolutionStrategy(field: string, values: any[]): 'highest_confidence' | 'most_recent' | 'majority_vote' {
    const priority = this.config.fieldPriorities[field] || 0.5;
    
    if (priority > 0.9) return 'highest_confidence';
    if (values.length >= 3) return 'majority_vote';
    return 'most_recent';
  }

  private resolveByHighestConfidence(values: Array<{ source: string; value: any; confidence: number }>): any {
    return values.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    ).value;
  }

  private resolveByMostRecent(values: Array<{ source: string; value: any; confidence: number }>): any {
    // 简化实现，返回第一个值
    return values[0].value;
  }

  private resolveByMajorityVote(values: Array<{ source: string; value: any; confidence: number }>): any {
    const counts = new Map();
    for (const { value } of values) {
      counts.set(value, (counts.get(value) || 0) + 1);
    }
    
    let maxCount = 0;
    let majorityValue = values[0].value;
    
    for (const [value, count] of counts) {
      if (count > maxCount) {
        maxCount = count;
        majorityValue = value;
      }
    }
    
    return majorityValue;
  }

  private calculateAccuracy(fusedData: any, sources: DataSource[]): number {
    // 简化实现
    return 0.85;
  }

  private calculateCompleteness(fusedData: any): number {
    const requiredFields = ['name', 'latitude', 'longitude', 'address'];
    const presentFields = requiredFields.filter(field => fusedData[field] !== undefined);
    return presentFields.length / requiredFields.length;
  }

  private calculateConsistency(sources: DataSource[]): number {
    // 简化实现
    return 0.8;
  }

  private calculateTimeliness(sources: DataSource[]): number {
    const avgAge = sources.reduce((sum, source) => {
      const ageInDays = (Date.now() - source.lastUpdated.getTime()) / (1000 * 60 * 60 * 24);
      return sum + ageInDays;
    }, 0) / sources.length;
    
    return Math.max(0, 1 - avgAge / 30); // 30天内认为是及时的
  }

  private calculateOverallConfidence(sources: DataSource[], quality: QualityMetrics): number {
    const avgSourceConfidence = sources.reduce((sum, s) => sum + s.confidence, 0) / sources.length;
    return (avgSourceConfidence + quality.overall) / 2;
  }

  private createSingleSourceResult(source: DataSource): FusionResult {
    return {
      id: `single_${source.id}`,
      fusedData: source.data,
      sources: [source],
      confidence: source.confidence,
      fusionMethod: 'single_source',
      conflicts: [],
      quality: {
        accuracy: source.confidence,
        completeness: this.calculateCompleteness(source.data),
        consistency: 1.0,
        timeliness: this.calculateAgeWeight(source.lastUpdated),
        overall: source.confidence
      }
    };
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private hasCoordinates(data: any): boolean {
    return data.latitude !== undefined && data.longitude !== undefined;
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3;
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  /**
   * ⚙️ 更新配置
   */
  updateConfig(newConfig: Partial<FusionConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 📊 获取融合统计
   */
  getFusionStats(): {
    totalFusions: number;
    avgQuality: number;
    conflictRate: number;
    sourceDistribution: Record<string, number>;
  } {
    // 简化实现
    return {
      totalFusions: 1000,
      avgQuality: 0.82,
      conflictRate: 0.15,
      sourceDistribution: {
        'osm': 0.4,
        'yelp': 0.3,
        'google': 0.2,
        'wikipedia': 0.1
      }
    };
  }
}

export default DataFusionEngine;
