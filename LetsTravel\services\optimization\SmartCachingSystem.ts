/**
 * 🧠 智能缓存系统
 * 
 * 实现预测性缓存、智能失效和多层缓存架构
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// ===== 接口定义 =====

export interface CacheEntry {
  key: string;
  data: any;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  priority: CachePriority;
  tags: string[];
  size: number;
  metadata: CacheMetadata;
}

export interface CacheMetadata {
  source: string;
  version: string;
  dependencies: string[];
  invalidationRules: InvalidationRule[];
  compressionType?: 'gzip' | 'lz4' | 'none';
  encryptionLevel?: 'none' | 'basic' | 'advanced';
}

export interface InvalidationRule {
  type: 'time' | 'dependency' | 'event' | 'usage';
  condition: any;
  action: 'invalidate' | 'refresh' | 'notify';
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  compressionRatio: number;
  avgAccessTime: number;
  memoryUsage: number;
}

export interface PredictiveConfig {
  enabled: boolean;
  lookAheadTime: number; // 毫秒
  confidenceThreshold: number;
  maxPredictiveEntries: number;
  learningRate: number;
}

export enum CachePriority {
  CRITICAL = 5,
  HIGH = 4,
  NORMAL = 3,
  LOW = 2,
  BACKGROUND = 1
}

export enum CacheLayer {
  MEMORY = 'memory',
  STORAGE = 'storage',
  NETWORK = 'network',
  PREDICTIVE = 'predictive'
}

// ===== 智能缓存系统 =====

export class SmartCachingSystem {
  private static instance: SmartCachingSystem;
  private memoryCache: Map<string, CacheEntry> = new Map();
  private accessPatterns: Map<string, AccessPattern> = new Map();
  private predictiveConfig: PredictiveConfig;
  private stats: CacheStats;
  private maxMemorySize: number = 50 * 1024 * 1024; // 50MB
  private currentMemorySize: number = 0;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.predictiveConfig = {
      enabled: true,
      lookAheadTime: 5 * 60 * 1000, // 5分钟
      confidenceThreshold: 0.7,
      maxPredictiveEntries: 100,
      learningRate: 0.1
    };

    this.stats = {
      totalEntries: 0,
      totalSize: 0,
      hitRate: 0,
      missRate: 0,
      evictionCount: 0,
      compressionRatio: 1.0,
      avgAccessTime: 0,
      memoryUsage: 0
    };

    this.startCleanupTimer();
  }

  /**
   * 🎯 获取单例实例
   */
  static getInstance(): SmartCachingSystem {
    if (!SmartCachingSystem.instance) {
      SmartCachingSystem.instance = new SmartCachingSystem();
    }
    return SmartCachingSystem.instance;
  }

  /**
   * 💾 设置缓存
   */
  async set(
    key: string,
    data: any,
    options: {
      ttl?: number;
      priority?: CachePriority;
      tags?: string[];
      layer?: CacheLayer;
      compress?: boolean;
      encrypt?: boolean;
    } = {}
  ): Promise<void> {
    
    const {
      ttl = 60 * 60 * 1000, // 1小时默认
      priority = CachePriority.NORMAL,
      tags = [],
      layer = CacheLayer.MEMORY,
      compress = false,
      encrypt = false
    } = options;

    const entry: CacheEntry = {
      key,
      data: compress ? this.compress(data) : data,
      timestamp: Date.now(),
      ttl,
      accessCount: 0,
      lastAccessed: Date.now(),
      priority,
      tags,
      size: this.calculateSize(data),
      metadata: {
        source: 'user',
        version: '1.0',
        dependencies: [],
        invalidationRules: [],
        compressionType: compress ? 'gzip' : 'none',
        encryptionLevel: encrypt ? 'basic' : 'none'
      }
    };

    // 根据层级存储
    switch (layer) {
      case CacheLayer.MEMORY:
        await this.setMemoryCache(entry);
        break;
      case CacheLayer.STORAGE:
        await this.setStorageCache(entry);
        break;
      case CacheLayer.PREDICTIVE:
        await this.setPredictiveCache(entry);
        break;
    }

    // 更新访问模式
    this.updateAccessPattern(key);
    
    // 更新统计
    this.updateStats('set', entry);
  }

  /**
   * 📖 获取缓存
   */
  async get(key: string, options: { layer?: CacheLayer } = {}): Promise<any> {
    const startTime = Date.now();
    
    try {
      // 按优先级检查各层缓存
      const layers = options.layer ? [options.layer] : [
        CacheLayer.MEMORY,
        CacheLayer.STORAGE,
        CacheLayer.PREDICTIVE
      ];

      for (const layer of layers) {
        const entry = await this.getFromLayer(key, layer);
        if (entry) {
          // 更新访问信息
          entry.accessCount++;
          entry.lastAccessed = Date.now();
          
          // 如果是从存储层获取，提升到内存层
          if (layer === CacheLayer.STORAGE && this.shouldPromoteToMemory(entry)) {
            await this.setMemoryCache(entry);
          }
          
          // 更新访问模式
          this.updateAccessPattern(key);
          
          // 更新统计
          this.updateStats('hit', entry);
          this.stats.avgAccessTime = (this.stats.avgAccessTime + (Date.now() - startTime)) / 2;
          
          return entry.metadata.compressionType !== 'none' ? this.decompress(entry.data) : entry.data;
        }
      }

      // 缓存未命中
      this.updateStats('miss');
      
      // 触发预测性缓存
      if (this.predictiveConfig.enabled) {
        this.triggerPredictiveCaching(key);
      }
      
      return null;

    } catch (error) {
      console.error('缓存获取失败:', error);
      return null;
    }
  }

  /**
   * 🗑️ 删除缓存
   */
  async delete(key: string): Promise<boolean> {
    let deleted = false;

    // 从内存缓存删除
    if (this.memoryCache.has(key)) {
      const entry = this.memoryCache.get(key)!;
      this.currentMemorySize -= entry.size;
      this.memoryCache.delete(key);
      deleted = true;
    }

    // 从存储缓存删除
    try {
      await AsyncStorage.removeItem(`cache_${key}`);
      deleted = true;
    } catch (error) {
      console.warn('存储缓存删除失败:', error);
    }

    if (deleted) {
      this.updateStats('delete');
    }

    return deleted;
  }

  /**
   * 🏷️ 按标签删除
   */
  async deleteByTag(tag: string): Promise<number> {
    let deletedCount = 0;

    // 删除内存缓存中的匹配项
    for (const [key, entry] of this.memoryCache) {
      if (entry.tags.includes(tag)) {
        await this.delete(key);
        deletedCount++;
      }
    }

    return deletedCount;
  }

  /**
   * 🔄 刷新缓存
   */
  async refresh(key: string, newData: any): Promise<void> {
    const entry = await this.getEntryFromAnyLayer(key);
    if (entry) {
      await this.set(key, newData, {
        ttl: entry.ttl,
        priority: entry.priority,
        tags: entry.tags
      });
    }
  }

  /**
   * 🧠 预测性缓存
   */
  async triggerPredictiveCaching(key: string): Promise<void> {
    if (!this.predictiveConfig.enabled) return;

    const pattern = this.accessPatterns.get(key);
    if (!pattern) return;

    // 预测下一个可能访问的键
    const predictions = this.predictNextAccess(pattern);
    
    for (const prediction of predictions) {
      if (prediction.confidence >= this.predictiveConfig.confidenceThreshold) {
        // 异步预加载
        this.preloadData(prediction.key).catch(error => {
          console.warn('预测性缓存失败:', error);
        });
      }
    }
  }

  /**
   * 📊 获取缓存统计
   */
  getStats(): CacheStats {
    this.stats.memoryUsage = this.currentMemorySize;
    this.stats.totalEntries = this.memoryCache.size;
    this.stats.totalSize = this.currentMemorySize;
    return { ...this.stats };
  }

  /**
   * 🧹 清理过期缓存
   */
  async cleanup(): Promise<number> {
    let cleanedCount = 0;
    const now = Date.now();

    // 清理内存缓存
    for (const [key, entry] of this.memoryCache) {
      if (this.isExpired(entry, now)) {
        await this.delete(key);
        cleanedCount++;
      }
    }

    // 清理存储缓存
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      for (const cacheKey of cacheKeys) {
        const entryData = await AsyncStorage.getItem(cacheKey);
        if (entryData) {
          const entry: CacheEntry = JSON.parse(entryData);
          if (this.isExpired(entry, now)) {
            await AsyncStorage.removeItem(cacheKey);
            cleanedCount++;
          }
        }
      }
    } catch (error) {
      console.warn('存储缓存清理失败:', error);
    }

    this.stats.evictionCount += cleanedCount;
    return cleanedCount;
  }

  /**
   * ⚙️ 配置预测性缓存
   */
  configurePredictive(config: Partial<PredictiveConfig>): void {
    this.predictiveConfig = { ...this.predictiveConfig, ...config };
  }

  // ===== 私有方法 =====

  private async setMemoryCache(entry: CacheEntry): Promise<void> {
    // 检查内存限制
    if (this.currentMemorySize + entry.size > this.maxMemorySize) {
      await this.evictLRU(entry.size);
    }

    this.memoryCache.set(entry.key, entry);
    this.currentMemorySize += entry.size;
  }

  private async setStorageCache(entry: CacheEntry): Promise<void> {
    try {
      await AsyncStorage.setItem(`cache_${entry.key}`, JSON.stringify(entry));
    } catch (error) {
      console.warn('存储缓存设置失败:', error);
    }
  }

  private async setPredictiveCache(entry: CacheEntry): Promise<void> {
    // 预测性缓存逻辑
    entry.tags.push('predictive');
    await this.setMemoryCache(entry);
  }

  private async getFromLayer(key: string, layer: CacheLayer): Promise<CacheEntry | null> {
    switch (layer) {
      case CacheLayer.MEMORY:
        return this.memoryCache.get(key) || null;
      
      case CacheLayer.STORAGE:
        try {
          const data = await AsyncStorage.getItem(`cache_${key}`);
          return data ? JSON.parse(data) : null;
        } catch (error) {
          return null;
        }
      
      case CacheLayer.PREDICTIVE:
        // 检查预测性缓存
        const entry = this.memoryCache.get(key);
        return (entry && entry.tags.includes('predictive')) ? entry : null;
      
      default:
        return null;
    }
  }

  private async getEntryFromAnyLayer(key: string): Promise<CacheEntry | null> {
    const layers = [CacheLayer.MEMORY, CacheLayer.STORAGE, CacheLayer.PREDICTIVE];
    
    for (const layer of layers) {
      const entry = await this.getFromLayer(key, layer);
      if (entry) return entry;
    }
    
    return null;
  }

  private shouldPromoteToMemory(entry: CacheEntry): boolean {
    return entry.accessCount >= 3 && entry.priority >= CachePriority.NORMAL;
  }

  private async evictLRU(requiredSize: number): Promise<void> {
    const entries = Array.from(this.memoryCache.values())
      .sort((a, b) => {
        // 按优先级和最后访问时间排序
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        return a.lastAccessed - b.lastAccessed;
      });

    let freedSize = 0;
    for (const entry of entries) {
      if (freedSize >= requiredSize) break;
      
      await this.delete(entry.key);
      freedSize += entry.size;
    }
  }

  private updateAccessPattern(key: string): void {
    const now = Date.now();
    const pattern = this.accessPatterns.get(key) || {
      key,
      accessTimes: [],
      frequency: 0,
      lastAccess: 0,
      avgInterval: 0
    };

    pattern.accessTimes.push(now);
    pattern.frequency++;
    pattern.lastAccess = now;

    // 保持最近100次访问记录
    if (pattern.accessTimes.length > 100) {
      pattern.accessTimes = pattern.accessTimes.slice(-100);
    }

    // 计算平均访问间隔
    if (pattern.accessTimes.length > 1) {
      const intervals = [];
      for (let i = 1; i < pattern.accessTimes.length; i++) {
        intervals.push(pattern.accessTimes[i] - pattern.accessTimes[i - 1]);
      }
      pattern.avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    }

    this.accessPatterns.set(key, pattern);
  }

  private predictNextAccess(pattern: AccessPattern): Array<{ key: string; confidence: number }> {
    const predictions: Array<{ key: string; confidence: number }> = [];
    
    // 基于访问间隔预测
    if (pattern.avgInterval > 0) {
      const timeSinceLastAccess = Date.now() - pattern.lastAccess;
      const expectedNextAccess = pattern.avgInterval - timeSinceLastAccess;
      
      if (expectedNextAccess <= this.predictiveConfig.lookAheadTime) {
        const confidence = Math.max(0, 1 - (expectedNextAccess / this.predictiveConfig.lookAheadTime));
        predictions.push({ key: pattern.key, confidence });
      }
    }

    return predictions.filter(p => p.confidence >= this.predictiveConfig.confidenceThreshold);
  }

  private async preloadData(key: string): Promise<void> {
    // 预加载数据的逻辑
    // 这里应该调用实际的数据获取方法
    console.log(`预加载数据: ${key}`);
  }

  private updateStats(operation: 'set' | 'hit' | 'miss' | 'delete', entry?: CacheEntry): void {
    switch (operation) {
      case 'hit':
        this.stats.hitRate = (this.stats.hitRate * 0.9) + (1 * 0.1);
        break;
      case 'miss':
        this.stats.missRate = (this.stats.missRate * 0.9) + (1 * 0.1);
        break;
    }
  }

  private isExpired(entry: CacheEntry, now: number): boolean {
    return (now - entry.timestamp) > entry.ttl;
  }

  private calculateSize(data: any): number {
    return JSON.stringify(data).length * 2; // 粗略估算
  }

  private compress(data: any): any {
    // 简化的压缩实现
    return data;
  }

  private decompress(data: any): any {
    // 简化的解压实现
    return data;
  }

  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup().catch(error => {
        console.warn('定时清理失败:', error);
      });
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }

  /**
   * 🔧 销毁实例
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.memoryCache.clear();
    this.accessPatterns.clear();
  }
}

// ===== 辅助接口 =====

interface AccessPattern {
  key: string;
  accessTimes: number[];
  frequency: number;
  lastAccess: number;
  avgInterval: number;
}

export default SmartCachingSystem;
