/**
 * 🎨 统一DayCard组件
 * 实现用户期望的简洁展开格式：
 * - 未展开：天数 + 天气 + 总消费 + 简洁时间线
 * - 展开后：详细信息完整显示
 */

import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import type { DayCard as DayCardType, TimelineActivity, DayActivity } from '../../types/SixAgentTypes';
import { DataTransformUtils, type UltraThinkActivity, type LegacyDayData } from '../../utils/DataTransformUtils';
import { TimeFormatter } from '../../utils/TimeFormatter';
import { DayCardDataAdapter } from '../adapters/UltraThinkDataAdapter';

const { width } = Dimensions.get('window');

interface DayCardProps {
  dayData?: DayCardType;
  // 🔄 Ultra Think数据支持
  ultraThinkActivities?: UltraThinkActivity[];
  day?: number;
  totalBudget?: number;
  currency?: string;
  isExpanded?: boolean;
  onToggle?: () => void;
  onActivityPress?: (activity: DayActivity) => void;
}

export default function DayCard({
  dayData,
  ultraThinkActivities,
  day,
  totalBudget,
  currency = 'MYR',
  isExpanded = false,
  onToggle,
  onActivityPress,
}: DayCardProps) {
  // 🔄 如果有Ultra Think数据，使用适配器处理
  if (ultraThinkActivities && day !== undefined) {
    return (
      <DayCardDataAdapter
        ultraThinkData={ultraThinkActivities}
        day={day}
        currency={currency}
      >
        {(adaptedDayData) => (
          <DayCardCore
            dayData={adaptedDayData}
            isExpanded={isExpanded}
            onToggle={onToggle}
            onActivityPress={onActivityPress}
            currency={currency}
          />
        )}
      </DayCardDataAdapter>
    );
  }

  // 使用传统数据或默认数据
  const processedDayData: LegacyDayData = dayData as LegacyDayData || {
    day: day || 1,
    date: '待定',
    timeline: [],
    activitiesDetailed: [],
    budget: { total: `${currency} 0`, breakdown: '暂无数据' },
    transportation: { totalTime: '0分钟', totalCost: `${currency} 0` }
  };

  return (
    <DayCardCore
      dayData={processedDayData}
      isExpanded={isExpanded}
      onToggle={onToggle}
      onActivityPress={onActivityPress}
      currency={currency}
    />
  );
}

/**
 * 🎨 DayCard核心渲染组件
 */
function DayCardCore({
  dayData,
  isExpanded,
  onToggle,
  onActivityPress,
  currency = 'MYR'
}: {
  dayData: LegacyDayData;
  isExpanded: boolean;
  onToggle?: () => void;
  onActivityPress?: (activity: DayActivity) => void;
  currency?: string;
}) {
  const [animatedHeight] = useState(new Animated.Value(isExpanded ? 1 : 0));

  React.useEffect(() => {
    Animated.timing(animatedHeight, {
      toValue: isExpanded ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isExpanded]);

  /**
   * 🎨 渲染未展开状态（简洁版）
   */
  const renderCompactView = () => (
    <TouchableOpacity
      style={styles.compactContainer}
      onPress={onToggle}
      activeOpacity={0.7}
    >
      {/* 第一行：天数、日期、天气、预算 */}
      <View style={styles.headerRow}>
        <View style={styles.dayInfo}>
          <Text style={styles.dayNumber}>Day {dayData.day}</Text>
          <Text style={styles.dayDate}>{dayData.date}</Text>
          <Text style={styles.dayLocation}>目的地</Text>
        </View>

        <View style={styles.weatherBudgetRow}>
          {/* 天气信息 */}
          <View style={styles.weatherCompact}>
            <Text style={styles.weatherIcon}>☀️</Text>
            <Text style={styles.tempRange}>25-30°C</Text>
          </View>

          {/* 预算信息 */}
          <View style={styles.budgetCompact}>
            <Text style={styles.budgetTotal}>{dayData.budget.total}</Text>
          </View>

          {/* 展开箭头 */}
          <Ionicons 
            name={isExpanded ? "chevron-up" : "chevron-down"} 
            size={20} 
            color="#666"
          />
        </View>
      </View>

      {/* 第二行：预算分解 */}
      <Text style={styles.budgetBreakdown} numberOfLines={1}>
        {dayData.budget.breakdown}
      </Text>

      {/* 第三行：简洁时间线 */}
      <View style={styles.timelineCompact}>
        {dayData.timeline.slice(0, 4).map((activity, index) => (
          <View key={index} style={styles.timelineItem}>
            <Text style={styles.timelineTime}>{activity.time}</Text>
            <Text style={styles.timelineIcon}>{activity.icon}</Text>
            <Text style={styles.timelineName} numberOfLines={1}>
              {activity.name}
            </Text>
            <Text style={styles.timelineDuration}>({activity.duration})</Text>
          </View>
        ))}
        {dayData.timeline.length > 4 && (
          <Text style={styles.moreActivities}>
            +{dayData.timeline.length - 4}个活动
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  /**
   * 🎨 渲染展开状态（详细版）
   */
  const renderExpandedView = () => (
    <Animated.View
      style={[
        styles.expandedContainer,
        {
          opacity: animatedHeight,
          maxHeight: animatedHeight.interpolate({
            inputRange: [0, 1],
            outputRange: [0, 1000],
          }),
        },
      ]}
    >
      {/* 详细预算信息 - 简化版 */}
      <View style={styles.budgetDetailed}>
        <Text style={styles.sectionTitle}>💰 预算明细</Text>
        <Text style={styles.budgetBreakdown}>
          {dayData.budget.breakdown}
        </Text>
      </View>

      {/* 详细活动列表 */}
      {dayData.activitiesDetailed && (
        <View style={styles.activitiesDetailed}>
          <Text style={styles.sectionTitle}>📅 详细行程</Text>
          {dayData.activitiesDetailed.map((activity, index) => (
            <TouchableOpacity
              key={activity.id}
              style={styles.activityCard}
              onPress={() => onActivityPress?.(activity)}
            >
              <View style={styles.activityHeader}>
                <View style={styles.activityTime}>
                  <Text style={styles.activityTimeText}>
                    {activity.timeSlot.start} - {activity.timeSlot.end}
                  </Text>
                  <Text style={styles.activityDuration}>
                    {activity.timeSlot.duration}
                  </Text>
                </View>
                <View style={styles.activityInfo}>
                  <Text style={styles.activityName}>{activity.name}</Text>
                  <Text style={styles.activityCategory}>{activity.category}</Text>
                  <Text style={styles.activityLocation}>
                    📍 {activity.location.name}
                  </Text>
                </View>
                <View style={styles.activityCost}>
                  <Text style={styles.activityCostText}>{activity.cost.total}</Text>
                </View>
              </View>
              
              <Text style={styles.activityDescription} numberOfLines={2}>
                {activity.details.description}
              </Text>
              
              {/* 交通信息 */}
              <View style={styles.transportInfo}>
                <Ionicons name="car-outline" size={14} color="#666" />
                <Text style={styles.transportText}>
                  {activity.transportation.method} · {activity.transportation.duration} · {activity.transportation.cost}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* 交通概览 */}
      {dayData.transportation && (
        <View style={styles.transportationOverview}>
          <Text style={styles.sectionTitle}>🚗 交通概览</Text>
          <View style={styles.transportGrid}>
            <View style={styles.transportItem}>
              <Text style={styles.transportLabel}>总时间</Text>
              <Text style={styles.transportValue}>{dayData.transportation.totalTime}</Text>
            </View>
            <View style={styles.transportItem}>
              <Text style={styles.transportLabel}>总费用</Text>
              <Text style={styles.transportValue}>{dayData.transportation.totalCost}</Text>
            </View>
          </View>
        </View>
      )}
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      {renderCompactView()}
      {isExpanded && renderExpandedView()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  // 未展开状态样式
  compactContainer: {
    padding: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dayInfo: {
    flex: 1,
  },
  dayNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  dayDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  dayLocation: {
    fontSize: 12,
    color: '#999',
    marginTop: 1,
  },
  weatherBudgetRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  weatherCompact: {
    alignItems: 'center',
  },
  weatherIcon: {
    fontSize: 24,
  },
  tempRange: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  budgetCompact: {
    alignItems: 'center',
  },
  budgetTotal: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E7D32',
  },
  budgetBreakdown: {
    fontSize: 12,
    color: '#666',
    marginBottom: 12,
  },
  
  // 时间线样式
  timelineCompact: {
    gap: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    gap: 8,
  },
  timelineTime: {
    fontSize: 12,
    color: '#666',
    width: 40,
  },
  timelineIcon: {
    fontSize: 16,
    width: 20,
  },
  timelineName: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  timelineDuration: {
    fontSize: 12,
    color: '#999',
  },
  moreActivities: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 4,
  },
  
  // 展开状态样式
  expandedContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  
  // 详细天气样式
  weatherDetailed: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  weatherGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  weatherItem: {
    alignItems: 'center',
  },
  weatherLabel: {
    fontSize: 12,
    color: '#666',
  },
  weatherValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginTop: 2,
  },
  weatherRecommendation: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  
  // 详细预算样式
  budgetDetailed: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  budgetGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  budgetItem: {
    width: '48%',
    marginBottom: 8,
  },
  budgetLabel: {
    fontSize: 12,
    color: '#666',
  },
  budgetValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2E7D32',
    marginTop: 2,
  },
  
  // 详细活动样式
  activitiesDetailed: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  activityCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  activityHeader: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  activityTime: {
    width: 80,
  },
  activityTimeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  activityDuration: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  activityInfo: {
    flex: 1,
    marginLeft: 12,
  },
  activityName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  activityCategory: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  activityLocation: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  activityCost: {
    alignItems: 'flex-end',
  },
  activityCostText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2E7D32',
  },
  activityDescription: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
    marginBottom: 8,
  },
  transportInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  transportText: {
    fontSize: 12,
    color: '#666',
  },
  
  // 交通概览样式
  transportationOverview: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transportGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  transportItem: {
    alignItems: 'center',
  },
  transportLabel: {
    fontSize: 12,
    color: '#666',
  },
  transportValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginTop: 2,
  },
  recommendedPasses: {
    marginTop: 8,
  },
  passesTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  passItem: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  
  // 提醒样式
  tipsSection: {
    padding: 16,
  },
  tipCategory: {
    marginBottom: 12,
  },
  tipCategoryTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  tipItem: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    marginBottom: 2,
  },
});
