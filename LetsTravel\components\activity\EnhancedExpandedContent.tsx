/**
 * 🌟 增强的活动展开内容组件
 * 
 * 集成真实API数据，显示优化后的活动详细信息
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// ===== 接口定义 =====

export interface ActivityData {
  id: string;
  name: string;
  type: string;
  location?: {
    name?: string;
    address?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
    district?: string;
  };
  cost?: number;
  currency?: string;
  duration?: number;
  rating?: number;
  reviewCount?: number;
  description?: string;
  tags?: string[];
  features?: string[];
  tips?: string[];
  realLocation?: boolean;
  source?: string;
  confidence?: number;
  attributes?: {
    isRealLocation?: boolean;
    apiSource?: string;
    qualityScore?: number;
  };
}

export interface APIData {
  nominatimData?: {
    displayName: string;
    address: any;
    confidence: number;
  };
  osrmData?: {
    duration: number;
    distance: number;
    route: string;
  };
  overpassData?: {
    amenities: string[];
    nearbyPlaces: string[];
  };
  budgetBreakdown?: {
    base: number;
    extras: number;
    total: number;
  };
  weatherData?: {
    condition: string;
    temperature: string;
    recommendation: string;
  };
  realTimeInfo?: {
    openingHours: string;
    crowdLevel: string;
    bestVisitTime: string;
  };
}

interface EnhancedExpandedContentProps {
  activity: ActivityData;
  apiData?: APIData;
  cardFormat: 'classic' | 'modern';
  onClose?: () => void;
}

// ===== 增强展开内容组件 =====

const EnhancedExpandedContent: React.FC<EnhancedExpandedContentProps> = ({
  activity,
  apiData,
  cardFormat,
  onClose
}) => {

  /**
   * 🎨 渲染位置信息
   */
  const renderLocationInfo = () => (
    <View style={styles.infoSection}>
      <View style={styles.sectionHeader}>
        <Ionicons name="location" size={18} color="#4CAF50" />
        <Text style={styles.sectionTitle}>位置信息</Text>
        {activity.realLocation && (
          <View style={styles.realLocationBadge}>
            <Text style={styles.realLocationText}>真实位置</Text>
          </View>
        )}
      </View>
      
      <View style={styles.sectionContent}>
        {apiData?.nominatimData ? (
          <>
            <Text style={styles.primaryText}>📍 {apiData.nominatimData.displayName}</Text>
            <Text style={styles.secondaryText}>
              精确度: {(apiData.nominatimData.confidence * 100).toFixed(0)}%
            </Text>
            {activity.location?.district && (
              <Text style={styles.secondaryText}>🏘️ 区域: {activity.location.district}</Text>
            )}
          </>
        ) : (
          <Text style={styles.primaryText}>📍 {activity.location?.address || activity.location?.name || '位置信息待更新'}</Text>
        )}
        
        {activity.location?.coordinates && (
          <Text style={styles.coordinatesText}>
            坐标: {activity.location.coordinates.lat.toFixed(4)}, {activity.location.coordinates.lng.toFixed(4)}
          </Text>
        )}
      </View>
    </View>
  );

  /**
   * 🛣️ 渲染路线信息
   */
  const renderRouteInfo = () => (
    apiData?.osrmData && (
      <View style={styles.infoSection}>
        <View style={styles.sectionHeader}>
          <Ionicons name="navigate" size={18} color="#2196F3" />
          <Text style={styles.sectionTitle}>路线信息</Text>
        </View>
        
        <View style={styles.sectionContent}>
          <Text style={styles.primaryText}>🚶 步行时间: {apiData.osrmData.duration}分钟</Text>
          <Text style={styles.secondaryText}>📏 距离: {(apiData.osrmData.distance / 1000).toFixed(1)}公里</Text>
          {apiData.osrmData.route && (
            <Text style={styles.secondaryText}>🛣️ 路线: {apiData.osrmData.route}</Text>
          )}
        </View>
      </View>
    )
  );

  /**
   * 💰 渲染预算明细
   */
  const renderBudgetBreakdown = () => (
    <View style={styles.infoSection}>
      <View style={styles.sectionHeader}>
        <Ionicons name="wallet" size={18} color="#FF9800" />
        <Text style={styles.sectionTitle}>预算明细</Text>
      </View>
      
      <View style={styles.sectionContent}>
        {apiData?.budgetBreakdown ? (
          <>
            <View style={styles.budgetRow}>
              <Text style={styles.budgetLabel}>基础费用:</Text>
              <Text style={styles.budgetValue}>{activity.currency || 'MYR'} {apiData.budgetBreakdown.base}</Text>
            </View>
            <View style={styles.budgetRow}>
              <Text style={styles.budgetLabel}>额外费用:</Text>
              <Text style={styles.budgetValue}>{activity.currency || 'MYR'} {apiData.budgetBreakdown.extras}</Text>
            </View>
            <View style={[styles.budgetRow, styles.budgetTotal]}>
              <Text style={styles.budgetTotalLabel}>总计:</Text>
              <Text style={styles.budgetTotalValue}>{activity.currency || 'MYR'} {apiData.budgetBreakdown.total}</Text>
            </View>
          </>
        ) : (
          <Text style={styles.primaryText}>💰 预估费用: {activity.currency || 'MYR'} {activity.cost || 0}</Text>
        )}
      </View>
    </View>
  );

  /**
   * 🌤️ 渲染天气信息
   */
  const renderWeatherInfo = () => (
    apiData?.weatherData && (
      <View style={styles.infoSection}>
        <View style={styles.sectionHeader}>
          <Ionicons name="partly-sunny" size={18} color="#FFC107" />
          <Text style={styles.sectionTitle}>天气建议</Text>
        </View>
        
        <View style={styles.sectionContent}>
          <Text style={styles.primaryText}>🌤️ {apiData.weatherData.condition}</Text>
          <Text style={styles.secondaryText}>🌡️ 温度: {apiData.weatherData.temperature}</Text>
          <Text style={styles.recommendationText}>💡 {apiData.weatherData.recommendation}</Text>
        </View>
      </View>
    )
  );

  /**
   * ⏰ 渲染实时信息
   */
  const renderRealTimeInfo = () => (
    apiData?.realTimeInfo && (
      <View style={styles.infoSection}>
        <View style={styles.sectionHeader}>
          <Ionicons name="time" size={18} color="#9C27B0" />
          <Text style={styles.sectionTitle}>实时信息</Text>
        </View>
        
        <View style={styles.sectionContent}>
          <Text style={styles.primaryText}>🕐 营业时间: {apiData.realTimeInfo.openingHours}</Text>
          <Text style={styles.secondaryText}>👥 人流量: {apiData.realTimeInfo.crowdLevel}</Text>
          <Text style={styles.recommendationText}>⭐ 最佳时间: {apiData.realTimeInfo.bestVisitTime}</Text>
        </View>
      </View>
    )
  );

  /**
   * 🏷️ 渲染标签和特色
   */
  const renderTagsAndFeatures = () => (
    <View style={styles.infoSection}>
      <View style={styles.sectionHeader}>
        <Ionicons name="pricetag" size={18} color="#E91E63" />
        <Text style={styles.sectionTitle}>特色标签</Text>
      </View>
      
      <View style={styles.sectionContent}>
        {activity.tags && activity.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {activity.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        )}
        
        {activity.features && activity.features.length > 0 && (
          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>✨ 特色亮点:</Text>
            {activity.features.map((feature, index) => (
              <Text key={index} style={styles.featureItem}>• {feature}</Text>
            ))}
          </View>
        )}
      </View>
    </View>
  );

  /**
   * 💡 渲染贴士建议
   */
  const renderTips = () => (
    activity.tips && activity.tips.length > 0 && (
      <View style={styles.infoSection}>
        <View style={styles.sectionHeader}>
          <Ionicons name="bulb" size={18} color="#FF5722" />
          <Text style={styles.sectionTitle}>实用贴士</Text>
        </View>
        
        <View style={styles.sectionContent}>
          {activity.tips.map((tip, index) => (
            <Text key={index} style={styles.tipItem}>💡 {tip}</Text>
          ))}
        </View>
      </View>
    )
  );

  /**
   * 📊 渲染数据质量信息
   */
  const renderDataQuality = () => (
    activity.attributes && (
      <View style={styles.infoSection}>
        <View style={styles.sectionHeader}>
          <Ionicons name="analytics" size={18} color="#607D8B" />
          <Text style={styles.sectionTitle}>数据质量</Text>
        </View>
        
        <View style={styles.sectionContent}>
          <Text style={styles.secondaryText}>
            📊 质量评分: {activity.attributes.qualityScore?.toFixed(0) || 'N/A'}/100
          </Text>
          <Text style={styles.secondaryText}>
            🔗 数据源: {activity.attributes.apiSource || activity.source || '模板'}
          </Text>
          <Text style={styles.secondaryText}>
            🎯 置信度: {activity.confidence ? (activity.confidence * 100).toFixed(0) + '%' : 'N/A'}
          </Text>
        </View>
      </View>
    )
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.content}>
        {/* 头部信息 */}
        <View style={styles.header}>
          <Text style={styles.activityName}>{activity.name}</Text>
          {activity.rating && (
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={styles.ratingText}>{activity.rating.toFixed(1)}</Text>
              {activity.reviewCount && (
                <Text style={styles.reviewCount}>({activity.reviewCount})</Text>
              )}
            </View>
          )}
        </View>

        {/* 描述 */}
        {activity.description && (
          <Text style={styles.description}>{activity.description}</Text>
        )}

        {/* 各个信息区块 */}
        {renderLocationInfo()}
        {renderRouteInfo()}
        {renderBudgetBreakdown()}
        {renderWeatherInfo()}
        {renderRealTimeInfo()}
        {renderTagsAndFeatures()}
        {renderTips()}
        {renderDataQuality()}
      </View>
    </ScrollView>
  );
};

// ===== 样式定义 =====

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  activityName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  reviewCount: {
    fontSize: 12,
    color: '#666',
  },
  description: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 16,
  },
  infoSection: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  realLocationBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  realLocationText: {
    fontSize: 10,
    color: '#ffffff',
    fontWeight: '600',
  },
  sectionContent: {
    gap: 6,
  },
  primaryText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  secondaryText: {
    fontSize: 13,
    color: '#666',
  },
  coordinatesText: {
    fontSize: 11,
    color: '#999',
    fontFamily: 'monospace',
  },
  budgetRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 2,
  },
  budgetLabel: {
    fontSize: 13,
    color: '#666',
  },
  budgetValue: {
    fontSize: 13,
    color: '#333',
    fontWeight: '500',
  },
  budgetTotal: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    marginTop: 8,
    paddingTop: 8,
  },
  budgetTotalLabel: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  budgetTotalValue: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  recommendationText: {
    fontSize: 13,
    color: '#4CAF50',
    fontStyle: 'italic',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 8,
  },
  tag: {
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 11,
    color: '#1976d2',
    fontWeight: '500',
  },
  featuresContainer: {
    marginTop: 8,
  },
  featuresTitle: {
    fontSize: 13,
    color: '#333',
    fontWeight: '600',
    marginBottom: 4,
  },
  featureItem: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
  },
  tipItem: {
    fontSize: 13,
    color: '#555',
    marginBottom: 4,
    lineHeight: 18,
  },
});

export default EnhancedExpandedContent;
