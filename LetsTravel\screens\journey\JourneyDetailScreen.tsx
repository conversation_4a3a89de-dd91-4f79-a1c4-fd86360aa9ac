/**
 * Trekmate 4.0 - 行程详情界面
 * 显示行程的详细信息和活动列表
 */

import React, { useEffect, useState, useMemo, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  FlatList,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { JOURNEY, COMMON } from '../../constants/Strings';
import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { useCurrentJourney, useJourneyActivities, useLoading } from '../../hooks/useStore';
import type { JourneyStackScreenProps } from '../../types/Navigation';
import type { Activity } from '../../types/CoreServices';
import { SupabaseJourneyService } from '../../services/journey/SupabaseJourneyService';
import SimpleDayNavigation from '../../components/journey/SimpleDayNavigation';
import { DayHeaderWithWeather, WeatherForecast } from '../../components/journey/WeatherIntegration';
import TransportationCard from '../../components/journey/TransportationCard';
import DayCard from '../../components/journey/DayCard';
import { TotalBudgetCard, DayBudgetCard, BudgetAnalyzer } from '../../components/journey/BudgetBreakdown';
import EnhancedDayCard from '../../components/journey/EnhancedDayCard';
// import TimelineDayCard from '../../components/journey/TimelineDayCard'; // 🔧 已移除，使用FixedDayCard
import { EnhancedBudgetDisplay } from '../../components/journey/EnhancedBudgetDisplay';
// import JSONDataAdapter from '../../components/adapters/JSONDataAdapter'; // 🔧 已移除，使用FixedJourneyAdapter
import { JSONDataConverter } from '../../services/converters/JSONDataConverter';
// import TimelineAdapter from '../../components/adapters/TimelineAdapter'; // 🔧 已移除，使用FixedJourneyAdapter
import { SimpleBudgetFixer } from '../../services/budget/SimpleBudgetFixer';
import FixedJourneyAdapter from '../../components/adapters/FixedJourneyAdapter';
import UltraThinkJourneyAdapter from '../../components/adapters/UltraThinkJourneyAdapter';
import { TransportationParser } from '../../utils/TransportationParser';
import { calculateTotalDays, groupActivitiesByDay, generateDayDates, formatActivityTime, calculateDayStatistics } from '../../utils/journeyUtils';
import { EnhancedBudgetAnalyzer } from '../../utils/BudgetAnalyzer';
import { CurrencyConverter } from '../../utils/CurrencyConverter';
import { safeDateHandler } from '../../services/datetime/SafeDateHandler';
import { getCurrencyFromLocation, getCurrencyFromCountryCode, formatCurrency, CurrencyInfo, DEFAULT_CURRENCY, SUPPORTED_CURRENCIES } from '../../config/CurrencyConfig';
import { getUserCountry } from '../../services/locationService';

interface JourneyDetailScreenProps extends JourneyStackScreenProps<'JourneyDetail'> {}

export default function JourneyDetailScreen({ navigation, route }: JourneyDetailScreenProps) {
  const { journeyId, journeyData, isTemporary, source } = route.params;

  const { journey: hookJourney, isLoading, fetchJourney } = useCurrentJourney();
  const { activities: dbActivities, fetchActivities } = useJourneyActivities(journeyId);
  const { setGlobalLoading } = useLoading();

  const [refreshing, setRefreshing] = useState(false);
  const [localJourney, setLocalJourney] = useState(journeyData || null);
  const [currentDay, setCurrentDay] = useState(1);
  const [weatherData, setWeatherData] = useState([]);
  const [expandedActivities, setExpandedActivities] = useState(new Set());
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showDetailedInfo, setShowDetailedInfo] = useState(false); // 🆕 显示详细信息
  const [userCurrency, setUserCurrency] = useState<CurrencyInfo>(SUPPORTED_CURRENCIES[DEFAULT_CURRENCY]);
  const [isLoadingCurrency, setIsLoadingCurrency] = useState(true);
  const [useNewJSONStructure, setUseNewJSONStructure] = useState(true); // 🆕 使用新JSON结构
  const [jsonComponentProps, setJsonComponentProps] = useState(null); // 🆕 JSON组件Props
  const [useTimelineView, setUseTimelineView] = useState(true); // 🆕 使用时间线视图
  const [useFixedView, setUseFixedView] = useState(true); // 🔧 使用修复版视图
  const [isUltraThinkData, setIsUltraThinkData] = useState(false); // 🎯 Ultra Think数据标识

  // 🔄 临时数据状态管理
  const [isTemporaryData, setIsTemporaryData] = useState(isTemporary || false);
  const [showSavePrompt, setShowSavePrompt] = useState(isTemporary || false);

  // 合并Journey数据 - 移到顶部避免变量声明顺序问题
  const currentJourney = localJourney || hookJourney;

  // 🔄 检查是否有新的JSON数据结构 + 预算修复
  useEffect(() => {
    const checkForJSONData = () => {
      if (currentJourney?.metadata?.jsonData) {
        console.log('🔄 检测到新的JSON数据结构');

        // 🎯 检测Ultra Think数据
        const jsonData = currentJourney.metadata.jsonData;
        console.log('🔍 检测Ultra Think数据:', {
          hasSource: !!jsonData.source,
          source: jsonData.source,
          hasMetadata: !!jsonData.metadata,
          hasPayload: !!jsonData.payload,
          hasDayPlans: !!jsonData.payload?.dayPlans,
          generationSource: jsonData.metadata?.generationSource
        });

        const isUltraThink = jsonData.source === 'master_solver_v2' ||
                           jsonData.metadata?.generationSource === 'ultra_think_master_solver_v2' ||
                           jsonData.payload?.dayPlans ||
                           jsonData.source === 'ultra-think';

        if (isUltraThink) {
          console.log('🎯 检测到Ultra Think数据，使用专用适配器');
          setIsUltraThinkData(true);
          return; // 直接返回，不进行传统数据转换
        }

        try {
          // 🔧 使用增强版数据转换，确保day信息完整性
          const originalBudget = currentJourney.budget || 8000;

          // 先尝试使用增强版转换
          try {
            const componentProps = JSONDataConverter.convertToComponentPropsEnhanced(currentJourney.metadata.jsonData);
            setJsonComponentProps(componentProps);
            setUseNewJSONStructure(true);
            console.log('✅ 增强版JSON数据转换成功，day信息已修复');
          } catch (enhancedError) {
            console.warn('⚠️ 增强版转换失败，使用预算修复方案:', enhancedError);

            // 降级到预算修复方案
            const fixedJourneyData = SimpleBudgetFixer.fixJourneyBudget(
              currentJourney.metadata.jsonData,
              originalBudget,
              'MYR'
            );

            const componentProps = JSONDataConverter.convertToComponentProps(fixedJourneyData);
            setJsonComponentProps(componentProps);
            setUseNewJSONStructure(true);
            console.log('✅ 降级JSON数据转换成功，预算已修复');
          }
        } catch (error) {
          console.error('❌ JSON数据转换失败:', error);
          setUseNewJSONStructure(false);
        }
      } else {
        setUseNewJSONStructure(false);
      }
    };

    checkForJSONData();
  }, [currentJourney]);

  // 预算调整Banner
  const renderBudgetBanner = () => {
    if (!currentJourney?.adjustmentNote) return null;
    const isWarning = currentJourney.budgetStatus === 'still_insufficient';
    const bg = isWarning ? '#ff4d4f' : '#ffc107';
    return (
      <View style={[styles.bannerContainer, { backgroundColor: bg }]}>
        <Text style={styles.bannerText}>{currentJourney.adjustmentNote}</Text>
      </View>
    );
  };

  // 🌍 检测用户位置并设置货币
  useEffect(() => {
    const detectUserCurrency = async () => {
      try {
        setIsLoadingCurrency(true);

        // 首先尝试从Journey数据中获取货币
        const journeyCurrency = currentJourney?.budget?.currency;
        if (journeyCurrency && SUPPORTED_CURRENCIES[journeyCurrency]) {
          setUserCurrency(SUPPORTED_CURRENCIES[journeyCurrency]);
          setIsLoadingCurrency(false);
          return;
        }

        // 尝试从位置检测货币
        try {
          const detectedCurrency = await getCurrencyFromLocation();
          setUserCurrency(detectedCurrency);
          console.log('🌍 检测到用户货币:', detectedCurrency.code);
        } catch (locationError) {
          console.warn('⚠️ 位置检测失败，尝试获取国家信息:', locationError);

          // 降级：尝试获取用户国家
          try {
            const userCountry = await getUserCountry();
            const countryCode = userCountry === 'Malaysia' ? 'MY' :
                              userCountry === 'Singapore' ? 'SG' :
                              userCountry === 'Thailand' ? 'TH' :
                              userCountry === 'Japan' ? 'JP' : 'MY';

            const currencyFromCountry = getCurrencyFromCountryCode(countryCode);
            setUserCurrency(currencyFromCountry);
            console.log('🏳️ 根据国家设置货币:', currencyFromCountry.code);
          } catch (countryError) {
            console.warn('⚠️ 国家检测也失败，使用默认货币:', countryError);
            setUserCurrency(SUPPORTED_CURRENCIES[DEFAULT_CURRENCY]);
          }
        }
      } catch (error) {
        console.error('❌ 货币检测失败:', error);
        setUserCurrency(SUPPORTED_CURRENCIES[DEFAULT_CURRENCY]);
      } finally {
        setIsLoadingCurrency(false);
      }
    };

    detectUserCurrency();
  }, [currentJourney?.budget?.currency]);

  // 🔧 安全的日期格式化函数 - 使用SafeDateHandler
  const formatDateSafely = (dateInput: any): string => {
    try {
      const dateResult = safeDateHandler.createSafeDate(dateInput);

      if (!dateResult.isValid) {
        console.warn('⚠️ 日期无效:', dateResult.errorMessage);
        return '日期待定';
      }

      return safeDateHandler.formatSafely(dateResult.date, 'short');
    } catch (error) {
      console.warn('❌ 日期格式化失败:', error);
      return '日期待定';
    }
  };

  // 🔧 安全获取Journey数据的函数
  const getSafeJourneyData = () => {
    const journey = currentJourney || localJourney;
    if (!journey) {
      return {
        title: '未命名行程',
        destination: { name: '未知目的地' },
        startDate: null,
        endDate: null,
        tags: [],
        isValid: false
      };
    }

    return {
      title: journey.title || '未命名行程',
      destination: journey.destination || { name: '未知目的地' },
      startDate: journey.startDate,
      endDate: journey.endDate,
      tags: journey.tags || [],
      isValid: true,
      originalJourney: journey
    };
  };

  // 🆕 动态生成详细信息
  const renderDynamicDetailInfo = () => {
    const detailItems = [];

    // 提取航班信息
    const flightActivities = activities.filter(activity =>
      activity.category === 'flight' || activity.type === 'flight' ||
      activity.title?.includes('航班') || activity.title?.includes('Flight')
    );

    flightActivities.forEach((flight: Activity, index: number) => {
      detailItems.push(
        <View key={`flight-${index}`} style={styles.detailRow}>
          <Ionicons name="airplane-outline" size={16} color="rgba(255,255,255,0.8)" />
          <Text style={styles.detailText}>{flight.title || flight.name}</Text>
        </View>
      );
    });

    // 提取酒店信息
    const hotelActivities = activities.filter(activity =>
      activity.category === 'accommodation' || activity.type === 'accommodation' ||
      activity.title?.includes('酒店') || activity.title?.includes('Hotel') ||
      activity.title?.includes('入住') || activity.title?.includes('退房')
    );

    // 按入住、住宿、退房分组
    const checkinActivities = hotelActivities.filter((h: Activity) => h.title?.includes('入住'));
    const stayActivities = hotelActivities.filter((h: Activity) => h.title?.includes('夜宿') || h.title?.includes('住宿'));
    const checkoutActivities = hotelActivities.filter((h: Activity) => h.title?.includes('退房'));

    // 显示酒店信息
    [...checkinActivities, ...stayActivities, ...checkoutActivities].forEach((hotel: Activity, index: number) => {
      detailItems.push(
        <View key={`hotel-${index}`} style={styles.detailRow}>
          <Ionicons name="bed-outline" size={16} color="rgba(255,255,255,0.8)" />
          <Text style={styles.detailText}>{hotel.title || hotel.name}</Text>
        </View>
      );
    });

    // 提取交通信息
    const transportActivities = activities.filter(activity =>
      activity.category === 'transport' || activity.type === 'transport' ||
      activity.title?.includes('交通') || activity.title?.includes('地铁') ||
      activity.title?.includes('JR') || activity.title?.includes('Pass')
    );

    transportActivities.forEach((transport: Activity, index: number) => {
      detailItems.push(
        <View key={`transport-${index}`} style={styles.detailRow}>
          <Ionicons name="card-outline" size={16} color="rgba(255,255,255,0.8)" />
          <Text style={styles.detailText}>{transport.title || transport.name}</Text>
        </View>
      );
    });

    // 如果没有找到任何详细信息，显示默认信息
    if (detailItems.length === 0) {
      detailItems.push(
        <View key="no-details" style={styles.detailRow}>
          <Ionicons name="information-circle-outline" size={16} color="rgba(255,255,255,0.8)" />
          <Text style={styles.detailText}>详细信息正在加载中...</Text>
        </View>
      );
    }

    return detailItems;
  };

  // 🆕 动态生成路线描述
  const generateDynamicRoute = () => {
    // 从活动中提取主要地点
    const locations = new Set<string>();

    activities.forEach((activity: Activity) => {
      if (activity.location?.address) {
        // 提取城市或地区名称
        const location = activity.location.address;
        if (location.includes('→')) {
          // 航班路线，提取目的地
          const parts = location.split('→');
          if (parts.length > 1) {
            locations.add(parts[1].trim());
          }
        } else {
          // 普通地点，提取主要区域
          const mainLocation = location.split(/[,，]/)[0].trim();
          if (mainLocation && !mainLocation.includes('机场') && !mainLocation.includes('Airport')) {
            locations.add(mainLocation);
          }
        }
      }
    });

    // 如果没有找到地点，使用Journey的目的地信息
    if (locations.size === 0 && currentJourney?.destination?.name) {
      locations.add(currentJourney.destination.name);
    }

    // 生成路线描述
    const locationArray = Array.from(locations).slice(0, 4); // 最多显示4个地点
    if (locationArray.length > 0) {
      return locationArray.join(' → ');
    }

    return '精彩旅程路线';
  };

  // 获取天气数据
  useEffect(() => {
    const fetchWeatherData = async () => {
      if (currentJourney?.destination) {
        try {
          // 模拟天气数据 - 实际应该调用天气API
          // 🔧 使用SafeDateHandler安全处理日期
          const startDateResult = safeDateHandler.createSafeDate(currentJourney.startDate);
          const endDateResult = safeDateHandler.createSafeDate(currentJourney.endDate);

          if (!startDateResult.isValid || !endDateResult.isValid) {
            console.warn('⚠️ Journey日期无效，使用默认值');
            setWeatherData([]);
            return;
          }

          const totalDays = calculateTotalDays(startDateResult.date, endDateResult.date);
          const mockWeatherData = Array.from({ length: totalDays }, (_, index) => ({
            date: new Date(Date.now() + index * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            temperature: {
              min: 24 + Math.random() * 4,
              max: 30 + Math.random() * 6,
            },
            condition: {
              icon: ['01d', '02d', '03d', '10d'][Math.floor(Math.random() * 4)],
              description: ['晴天', '多云', '阴天', '小雨'][Math.floor(Math.random() * 4)],
            },
            // 🔧 添加emoji图标用于显示
            icon: ['☀️', '⛅', '🌤️', '🌧️'][Math.floor(Math.random() * 4)],
            description: ['晴朗舒适', '多云转晴', '晴间多云', '小雨清新'][Math.floor(Math.random() * 4)],
            precipitation: {
              probability: Math.random() * 0.5,
            },
            wind: {
              speed: 5 + Math.random() * 10,
            },
          }));

          setWeatherData(mockWeatherData);
          console.log('天气数据已设置:', mockWeatherData.length, '天');
        } catch (error) {
          console.error('获取天气数据失败:', error);
        }
      }
    };

    fetchWeatherData();
  }, [currentJourney?.destination, currentJourney?.startDate, currentJourney?.endDate]);

  // 滚动引用和位置跟踪
  const scrollViewRef = useRef(null);
  const dayPositions = useRef({});
  const dayHeights = useRef({} as Record<number, number>);
  const isScrollingToDay = useRef(false);

  // 合并Journey中的activities和数据库中的activities
  const activities = useMemo(() => {
    const journeyActivities = localJourney?.activities || currentJourney?.activities || [];
    const databaseActivities = dbActivities || [];

    // 如果Journey中有activities（AI生成的），优先使用这些
    if (journeyActivities.length > 0) {
      console.log(`📊 使用Journey中的活动: ${journeyActivities.length}个`);
      return journeyActivities;
    }

    // 否则使用数据库中的activities
    console.log(`📊 使用数据库中的活动: ${databaseActivities.length}个`);
    return databaseActivities;
  }, [localJourney?.activities, currentJourney?.activities, dbActivities]);

  // 计算总天数和分组活动
  const totalDays = useMemo(() => {
    const journey = localJourney || currentJourney;
    if (!journey) return 0;
    return calculateTotalDays(new Date(journey.startDate), new Date(journey.endDate));
  }, [localJourney, currentJourney]);

  // 计算总活动数
  const totalActivities = useMemo(() => {
    return activities.length;
  }, [activities]);

  const [groupedActivities, setGroupedActivities] = useState<Activity[][]>([]);

  // 异步分组活动
  useEffect(() => {
    const groupActivities = async () => {
      const journey = localJourney || currentJourney;
      if (!journey || !activities.length) {
        setGroupedActivities([]);
        return;
      }

      // 🔧 使用SafeDateHandler安全处理日期
      const startDateResult = safeDateHandler.createSafeDate(journey.startDate);
      if (!startDateResult.isValid) {
        console.warn('⚠️ Journey开始日期无效，无法分组活动');
        setGroupedActivities([]);
        return;
      }

      try {
        const grouped = await groupActivitiesByDay(activities, startDateResult.date, totalDays);
        setGroupedActivities(grouped);
      } catch (error) {
        console.error('❌ 活动分组失败:', error);
        setGroupedActivities([]);
      }
    };

    groupActivities();
  }, [activities, localJourney, currentJourney, totalDays]);

  const dayDates = useMemo(() => {
    const journey = localJourney || currentJourney;
    if (!journey) return [];

    // 🔧 使用SafeDateHandler安全处理日期
    const startDateResult = safeDateHandler.createSafeDate(journey.startDate);
    if (!startDateResult.isValid) {
      console.warn('⚠️ Journey开始日期无效，使用默认日期');
      return generateDayDates(new Date(), totalDays);
    }

    return generateDayDates(startDateResult.date, totalDays);
  }, [localJourney, currentJourney, totalDays]);

  // 预算分析 - 使用增强版预算分析器
  const budgetAnalysis = useMemo(() => {
    const journey = localJourney || currentJourney;
    if (!journey || !activities.length) {
      return {
        breakdown: {},
        totalBudget: 0,
        isValid: false,
        report: null,
      };
    }

    // 验证和修复预算一致性
    const validation = EnhancedBudgetAnalyzer.validateAndFixJourneyBudget(journey);

    // 生成预算报告
    const report = EnhancedBudgetAnalyzer.generateBudgetReport(
      validation.fixedJourney || journey
    );

    const totalBudget = typeof journey.budget === 'number'
      ? journey.budget
      : journey.budget?.total || journey.budget?.amount || 0;

    if (!validation.isValid) {
      console.warn('🚨 预算验证问题:', validation.issues);
      console.log('💡 建议:', validation.recommendations);
    }

    return {
      breakdown: report.categoryTotals,
      totalBudget,
      isValid: validation.isValid,
      report,
      validation,
    };
  }, [activities, localJourney, currentJourney]);

  // 防抖引用
  const scrollDebounceRef = useRef(null);

  // 滚动处理 - 修复版本，确保天数导航正确更新
  const handleScroll = (event: any) => {
    if (isScrollingToDay.current) {
      return; // 如果正在程序化滚动，忽略滚动事件
    }

    const scrollY = event.nativeEvent.contentOffset.y;

    // 清除之前的防抖定时器
    if (scrollDebounceRef.current) {
      clearTimeout(scrollDebounceRef.current);
    }

    // 防抖处理，避免频繁更新
    scrollDebounceRef.current = setTimeout(() => {
      // 使用改进的算法计算当前天数
    let newCurrentDay = 1;
      let minDistance = Infinity;

      // 遍历所有天数位置，找到最接近视口中心的天数
      const viewportCenter = scrollY + 200; // 视口中心偏移

      Object.entries(dayPositions.current).forEach(([dayStr, position]) => {
      const day = parseInt(dayStr);
        if (position === undefined || position === null || isNaN(day)) return;

        // 计算距离视口中心的距离
        const distance = Math.abs((position as number) - viewportCenter);
        
        // 如果这个天数更接近视口中心，则更新
        if (distance < minDistance) {
          minDistance = distance;
        newCurrentDay = day;
      }
    });

      // 边界检查
      newCurrentDay = Math.max(1, Math.min(newCurrentDay, totalDays));

      // 只在天数真正改变时更新
    if (newCurrentDay !== currentDay) {
      setCurrentDay(newCurrentDay);
    }
    }, 50); // 减少防抖延迟，提高响应速度
  };

  // 天数导航处理 - 修复版本，避免scrollToIndex out of range错误
  const handleDaySelect = (day: number) => {
    if (!scrollViewRef.current) return;

    // 边界检查
    if (day < 1 || day > totalDays) {
      console.warn(`🚫 无效的天数选择: ${day}, 总天数: ${totalDays}`);
      return;
    }

    // 准备当前的FlatList数据
    const currentFlatListData = prepareFlatListData();

    // 检查FlatList数据是否准备好
    if (!currentFlatListData || currentFlatListData.length === 0) {
      console.warn(`🚫 FlatList数据未准备好，无法滚动到第${day}天`);
      return;
    }

    // 计算正确的index：header是index 0，day 1是index 1，day 2是index 2...
    const targetIndex = day; // day对应的index就是day本身（因为有header）

    // 确保index在有效范围内
    if (targetIndex >= currentFlatListData.length) {
      console.warn(`🚫 目标index ${targetIndex} 超出范围 (数据长度: ${currentFlatListData.length})`);
      return;
    }

    try {
      isScrollingToDay.current = true;

      // 使用安全的scrollToIndex
      scrollViewRef.current.scrollToIndex({
        index: targetIndex,
        animated: true,
        viewPosition: 0.1, // 滚动到顶部附近，留出一些空间
      });

      // 立即更新当前天数状态
      setCurrentDay(day);
      console.log(`🧭 选择了第${day}天 (index: ${targetIndex})`);

      // 延迟重置标志，确保滚动动画完成
      setTimeout(() => {
        isScrollingToDay.current = false;
      }, 800);

    } catch (error) {
      console.error('🚫 滚动到指定天数失败:', error);

      // 降级方案1：使用scrollToOffset
      const position = dayPositions.current[day];
      if (position !== undefined && scrollViewRef.current.scrollToOffset) {
        try {
          scrollViewRef.current.scrollToOffset({
            offset: position,
            animated: true,
          });
          console.log(`🔄 使用scrollToOffset降级方案成功`);
        } catch (offsetError) {
          console.error('🚫 scrollToOffset也失败:', offsetError);
        }
      }

      isScrollingToDay.current = false;
    }
  };

  // 天数布局处理 - 修复版本
  const layoutLogRef = useRef(new Set<number>());

  const handleDayLayout = (day: number, event: any) => {
    const { y, height } = event.nativeEvent.layout;

    // 使用FlatList的onLayout提供的y值，这是相对于列表开始的绝对位置
    dayPositions.current[day] = y;

    // 存储高度信息，用于后续计算
    if (!dayHeights.current) {
      dayHeights.current = {};
    }
    dayHeights.current[day] = height;

    // 只在首次布局时记录位置
    if (!layoutLogRef.current.has(day)) {
      console.log(`📐 第${day}天位置: ${y}px (高度: ${height}px)`);
      layoutLogRef.current.add(day);

      // 如果所有天数都布局完成，重新计算位置
      if (layoutLogRef.current.size === totalDays) {
        setTimeout(() => {
          console.log('🔄 重新计算所有天数位置...');
          Object.keys(dayPositions.current).forEach(dayStr => {
            const day = parseInt(dayStr);
            const position = dayPositions.current[day];
            console.log(`📍 第${day}天最终位置: ${position}px`);
          });
        }, 100);
      }
    }
  };

  // 获取分类显示名称
  const getCategoryDisplayName = (category: string): string => {
    const categoryNames = {
      accommodation: '住宿',
      transportation: '交通',
      food: '餐饮',
      activities: '景点',
      shopping: '购物',
      miscellaneous: '其他'
    };
    return categoryNames[category as keyof typeof categoryNames] || '其他';
  };

  // 准备FlatList数据结构
  const prepareFlatListData = () => {
    const data = [];

    // 🔧 修复：确保journey数据完整且安全
    const safeJourney = currentJourney || localJourney;
    if (!safeJourney) {
      console.warn('⚠️ prepareFlatListData: 没有可用的journey数据');
      return [];
    }

    // 添加头部信息
    data.push({
      type: 'header',
      id: 'header',
      journey: safeJourney,
      // 🔧 添加额外的安全数据
      title: safeJourney.title || '未命名行程',
      destination: safeJourney.destination || { name: '未知目的地' },
      startDate: safeJourney.startDate,
      endDate: safeJourney.endDate
    });

    // 添加每日数据
    groupedActivities.forEach((dayActivities: Activity[], dayIndex: number) => {
      const day = dayIndex + 1;
      const date = dayDates[dayIndex];
      const weather = weatherData[dayIndex];
      const destination = currentJourney?.destination?.name || localJourney?.destination?.name || '马来西亚';
      const dayCurrency = userCurrency?.code || 'MYR'; // 使用状态中的货币，带安全检查
      const dayStats = calculateDayStatistics(dayActivities, destination, dayCurrency) || {
        totalCost: 0,
        transportCost: 0,
        accommodationCost: 0,
        foodCost: 0,
        attractionsCost: 0,
        shoppingCost: 0
      };

      data.push({
        type: 'day',
        id: `day-${day}`,
        day,
        date,
        weather,
        activities: dayActivities,
        stats: dayStats
      });
    });

    return data;
  };

  const flatListData = prepareFlatListData();

  // 🎨 获取活动图标
  const getActivityIcon = (type: string | undefined): string => {
    const iconMap: Record<string, string> = {
      transport: '🚗',
      accommodation: '🏨',
      food: '🍽️',
      attraction: '🎯',
      shopping: '🛍️',
      leisure: '😊',
      cultural: '🏛️',
      nature: '🌿',
      entertainment: '🎭',
      default: '📍'
    };
    return iconMap[type] || iconMap.default;
  };

  // FlatList渲染函数
  const renderFlatListItem = ({ item }: { item: any }) => {
    if (item.type === 'header') {
      // 🔧 修复：多层安全获取journey数据
      const journey = item.journey || currentJourney || localJourney;
      if (!journey) {
        console.warn('⚠️ Header item缺少journey数据，跳过渲染');
        return (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>行程数据加载中...</Text>
          </View>
        );
      }

      // 🔧 添加数据验证
      const safeTitle = journey.title || item.title || '未命名行程';
      const safeDestination = journey.destination || item.destination || { name: '未知目的地' };
      const safeStartDate = journey.startDate || item.startDate;
      const safeEndDate = journey.endDate || item.endDate;

      return (
        <View>
          {/* 行程概览卡片 */}
          <LinearGradient
            colors={[colors.primary[500], colors.primary[600]]}
            style={styles.overviewCard}
          >
            <View style={styles.titleRow}>
              <View style={styles.titleWithIcon}>
                <Text style={styles.journeyIcon}>🗾</Text>
                <Text style={styles.journeyTitle}>{safeTitle}</Text>
              </View>
              <TouchableOpacity
                style={styles.viewToggleButton}
                onPress={() => setUseTimelineView(!useTimelineView)}
              >
                <Ionicons
                  name={useTimelineView ? "list-outline" : "time-outline"}
                  size={20}
                  color="rgba(255,255,255,0.9)"
                />
                <Text style={styles.viewToggleText}>
                  {useTimelineView ? '卡片视图' : '时间线'}
                </Text>
              </TouchableOpacity>
            </View>
            <Text style={styles.journeyDescription}>
              精心安排的{safeDestination.name}旅行计划，包含{totalActivities}个精彩活动
            </Text>

            {/* 🆕 基础信息 */}
            <View style={styles.basicInfoContainer}>
              <View style={styles.infoRow}>
                <Ionicons name="calendar-outline" size={16} color="rgba(255,255,255,0.8)" />
                <Text style={styles.infoText}>
                  {formatDateSafely(safeStartDate)} - {formatDateSafely(safeEndDate)} ({totalDays}天{totalDays-1}夜)
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Ionicons name="location-outline" size={16} color="rgba(255,255,255,0.8)" />
                <Text style={styles.infoText}>{generateDynamicRoute()}</Text>
              </View>
              <View style={styles.infoRow}>
                <Ionicons name="wallet-outline" size={16} color="rgba(255,255,255,0.8)" />
                <Text style={styles.infoText}>
                  {isLoadingCurrency ? (
                    '总预算: 计算中...'
                  ) : (
                    `总预算: ${formatCurrency(budgetAnalysis?.totalBudget || 0, userCurrency)} (约 ${formatCurrency(Math.round((budgetAnalysis?.totalBudget || 0)/totalDays), userCurrency)}/天)`
                  )}
                </Text>
              </View>
            </View>

            {/* 🆕 详细信息展开按钮 */}
            <TouchableOpacity
              style={styles.detailToggleButton}
              onPress={() => setShowDetailedInfo(!showDetailedInfo)}
            >
              <Text style={styles.detailToggleText}>
                {showDetailedInfo ? '收起详情' : '展开详情'}
              </Text>
              <Ionicons
                name={showDetailedInfo ? "chevron-up" : "chevron-down"}
                size={16}
                color="rgba(255,255,255,0.8)"
              />
            </TouchableOpacity>

            {/* 🆕 详细信息内容 */}
            {showDetailedInfo && (
              <View style={styles.detailedInfoContainer}>
                <View style={styles.detailSection}>
                  {renderDynamicDetailInfo()}
                </View>
              </View>
            )}

            <View style={styles.journeyMeta}>
              <View style={styles.metaItem}>
                <Ionicons name="calendar-outline" size={16} color="rgba(255,255,255,0.8)" />
                <Text style={styles.metaText}>
                  {formatDate(safeStartDate)} - {formatDate(safeEndDate)}
                </Text>
              </View>

              <View style={styles.metaItem}>
                <Ionicons name="time-outline" size={16} color="rgba(255,255,255,0.8)" />
                <Text style={styles.metaText}>{getDuration()}</Text>
              </View>

              {safeDestination && (
                <View style={styles.metaItem}>
                  <Ionicons name="location-outline" size={16} color="rgba(255,255,255,0.8)" />
                  <Text style={styles.metaText}>{safeDestination.name || safeDestination.address || '未知目的地'}</Text>
                </View>
              )}
            </View>

            {journey.tags && journey.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {journey.tags.map((tag: any, index: number) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}
          </LinearGradient>
        </View>
      );
    }

    if (item.type === 'day') {
      // 🎨 使用增强版DayCard组件
      return (
        <View
          style={styles.daySection}
          onLayout={(event: any) => handleDayLayout(item.day, event)}
        >
          <DayCard
            dayData={{
              dayNumber: item.day,
              date: formatDateSafely(item.date),
              dayOfWeek: new Date(item.date).toLocaleDateString('zh-CN', { weekday: 'short' }),
              location: currentJourney?.destination?.name || currentJourney?.destination?.address || localJourney?.destination?.name || localJourney?.destination?.address || '未知地点',
              weather: {
                icon: item.weather?.icon || '☀️',
                tempRange: item.weather?.temperature ?
                  `${Math.round(item.weather.temperature.min || 20)}-${Math.round(item.weather.temperature.max || 25)}°C` :
                  '20-25°C',
                condition: item.weather?.description || '晴朗',
                humidity: item.weather?.humidity,
                precipitation: item.weather?.precipitation,
                recommendation: item.weather?.recommendation
              },
              budget: {
                total: `${userCurrency?.symbol || 'RM'} ${Math.round(item.stats?.totalCost || 0)}`,
                breakdown: `住宿 ${userCurrency?.symbol || 'RM'}${Math.round(item.stats?.accommodationCost || 0)} · 餐饮 ${userCurrency?.symbol || 'RM'}${Math.round(item.stats?.foodCost || 0)} · 活动 ${userCurrency?.symbol || 'RM'}${Math.round(item.stats?.attractionsCost || 0)} · 交通 ${userCurrency?.symbol || 'RM'}${Math.round(item.stats?.transportCost || 0)}`,
                detailed: {
                  accommodation: Math.round(item.stats?.accommodationCost || 0),
                  meals: Math.round(item.stats?.foodCost || 0),
                  activities: Math.round(item.stats?.attractionsCost || 0),
                  transport: Math.round(item.stats?.transportCost || 0),
                  currency: userCurrency?.symbol || 'RM'
                }
              },
              timeline: item.activities.slice(0, 6).map((activity: any) => ({
                time: activity.startTime || '09:00',
                name: activity.title || activity.name || '活动',
                type: activity.type || 'attraction',
                icon: getActivityIcon(activity.type || 'attraction'),
                duration: activity.duration ? `${Math.round(activity.duration / 60)}h` : '2h',
                cost: activity.cost ? `${userCurrency?.symbol || 'RM'}${Math.round(activity.cost)}` : '免费'
              }))
            }}
            isExpanded={expandedActivities.has(item.day)}
            onToggle={() => {
              const newExpanded = new Set(expandedActivities);
              if (newExpanded.has(item.day)) {
                newExpanded.delete(item.day);
              } else {
                newExpanded.add(item.day);
              }
              setExpandedActivities(newExpanded);
            }}
            onActivityPress={(activity) => {
              console.log('活动点击:', activity.name);
              // 这里可以导航到活动详情页面
            }}
          />
        </View>
      );
    }

    return null;
  };



  // 分享行程
  const handleShare = async () => {
    try {
      const journeyToShare = localJourney || currentJourney;
      if (!journeyToShare) {
        Alert.alert('错误', '没有可分享的行程数据');
        return;
      }

      const shareContent = `🌟 ${journeyToShare.title}\n📅 ${totalDays}天行程\n🎯 ${activities.length}个活动\n📱 使用 Trekmate 4.0 制作`;

      // 临时处理：显示分享内容
      Alert.alert('分享内容', shareContent, [
        { text: '复制', onPress: () => console.log('复制功能待实现') },
        { text: '关闭', style: 'cancel' }
      ]);

        console.log('✅ 行程分享成功');
    } catch (error) {
      console.error('分享行程失败:', error);
      Alert.alert('分享失败', '分享行程时发生错误');
    }
  };

  // 生成分享内容
  const generateShareContent = (journey: any): string => {
    const startDate = new Date(journey.startDate).toLocaleDateString('zh-CN');
    const endDate = new Date(journey.endDate).toLocaleDateString('zh-CN');
    const duration = totalDays;
    const activitiesCount = activities.length;

    let content = `🌟 ${journey.title}\n\n`;
    content += `📅 时间: ${startDate} - ${endDate} (${duration}天)\n`;
    content += `📍 目的地: ${journey.destination?.name || '未知'}\n`;
    content += `🎯 活动数量: ${activitiesCount}个\n\n`;

    if (budgetAnalysis?.totalBudget && budgetAnalysis.totalBudget > 0) {
      content += `💰 预算: ${formatCurrency(budgetAnalysis.totalBudget, userCurrency)}\n\n`;
    }

    content += `📱 使用 Trekmate 4.0 制作`;

    return content;
  };

  // 活动展开/收起处理
  const toggleActivityExpansion = (activityId: string) => {
    const newExpanded = new Set(expandedActivities);
    if (newExpanded.has(activityId)) {
      newExpanded.delete(activityId);
    } else {
      newExpanded.add(activityId);
    }
    setExpandedActivities(newExpanded);
  };

  useEffect(() => {
    if (journeyData) {
      // 如果直接传递了数据，反序列化日期字段
      const deserializedJourney = {
        ...journeyData,
        startDate: new Date(journeyData.startDate),
        endDate: new Date(journeyData.endDate),
        createdAt: new Date(journeyData.createdAt),
        updatedAt: new Date(journeyData.updatedAt),
        activities: journeyData.activities?.map((activity: any) => ({
          ...activity,
          createdAt: new Date(activity.createdAt),
          updatedAt: new Date(activity.updatedAt),
          // 序列化时间字段，避免Date对象导致的序列化警告
          startTime: activity.startTime instanceof Date ? activity.startTime :
                     (typeof activity.startTime === 'string' ? new Date(activity.startTime) : undefined),
          endTime: activity.endTime instanceof Date ? activity.endTime :
                   (typeof activity.endTime === 'string' ? new Date(activity.endTime) : undefined),
        })) || []
      };
      setLocalJourney(deserializedJourney);
    } else {
      // 否则从数据库加载
      loadJourneyData();
    }
  }, [journeyId, journeyData]);

  const loadJourneyData = async () => {
    try {
      if (journeyData) {
        setLocalJourney(journeyData);
        return;
      }

      await Promise.all([
        fetchJourney(journeyId),
        fetchActivities(),
      ]);
    } catch (error) {
      console.error('加载行程数据失败:', error);
      // 尝试从本地存储加载
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const localData = await AsyncStorage.getItem(`journey_${journeyId}`);
        if (localData) {
          const parsedJourney = JSON.parse(localData);
          setLocalJourney(parsedJourney);
          console.log('✅ 从本地存储加载Journey成功');
          return;
        }
      } catch (localError) {
        console.error('本地存储加载失败:', localError);
      }

      Alert.alert('错误', '加载行程数据失败');
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadJourneyData();
    setRefreshing(false);
  };

  const handleEdit = () => {
    navigation.navigate('JourneyEdit', { journeyId });
  };

  const handleAddActivity = () => {
    navigation.navigate('ActivityEdit', { journeyId, activityId: undefined });
  };

  const handleActivityPress = (activity: Activity) => {
    navigation.navigate('ActivityDetail', { activityId: activity.id });
  };

  // 🔄 修改保存函数，处理临时数据
  const handleSaveJourney = async () => {
    if (!currentJourney) {
      Alert.alert('保存失败', '没有可保存的行程数据');
      return;
    }

    setIsSaving(true);
    try {
      const alertTitle = isTemporaryData ? '保存AI生成的行程' : '保存行程';
      const alertMessage = isTemporaryData ?
        '这是AI为您生成的行程，确定要保存到云端吗？保存后可以在"我的行程"中查看和编辑。' :
        '确定要保存这个行程到云端吗？';

      Alert.alert(
        alertTitle,
        alertMessage,
        [
          { text: '取消', style: 'cancel', onPress: () => setIsSaving(false) },
          {
            text: '保存',
            onPress: async () => {
              try {
                const journeyService = new SupabaseJourneyService();

                // 🔧 为临时数据生成新的正式ID
                const journeyToSave = isTemporaryData ? {
                  ...currentJourney,
                  id: `journey_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                  isTemporary: false,
                  savedAt: new Date().toISOString(),
                  source: source || 'ai_generated'
                } : currentJourney;

                const result = await journeyService.createJourney(journeyToSave);

                if (result.success) {
                  setHasUnsavedChanges(false);
                  setIsTemporaryData(false);
                  setShowSavePrompt(false);

                  // 🔧 清理临时缓存
                  if (isTemporaryData && journeyId.startsWith('temp_')) {
                    try {
                      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
                      await AsyncStorage.removeItem(`temp_journey_${journeyId}`);
                      console.log('✅ 临时缓存已清理');
                    } catch (cleanupError) {
                      console.warn('⚠️ 清理临时缓存失败:', cleanupError);
                    }
                  }

                  Alert.alert(
                    '保存成功',
                    '行程已保存到云端，可以在"我的行程"中查看',
                    [
                      {
                        text: '查看我的行程',
                        onPress: () => navigation.navigate('JourneyList')
                      },
                      { text: '继续查看', style: 'cancel' }
                    ]
                  );
                } else {
                  Alert.alert('保存失败', result.error || '数据库保存失败，请检查网络连接');
                }
              } catch (error) {
                console.error('保存行程错误:', error);
                Alert.alert('保存失败', '网络错误，请稍后重试');
              } finally {
                setIsSaving(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('保存行程错误:', error);
      Alert.alert('保存失败', '系统错误，请稍后重试');
      setIsSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getDuration = () => {
    if (!currentJourney) return '';
    const start = new Date(currentJourney.startDate);
    const end = new Date(currentJourney.endDate);
    const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    return `${days} 天`;
  };

  const renderActivityCard = (activity: Activity) => {
    const isExpanded = expandedActivities.has(activity.id);
    const timeStr = formatActivityTime(activity);

    // 获取预算信息
    const destination = currentJourney?.destination?.name || localJourney?.destination?.name || '马来西亚';
    const activityCurrency = userCurrency?.code || 'MYR'; // 使用状态中的货币，带安全检查
    const category = EnhancedBudgetAnalyzer.categorizeActivity(activity);
    const categoryIcon = EnhancedBudgetAnalyzer.getCategoryIcon(category);
    const costDisplay = EnhancedBudgetAnalyzer.getActivityCostDisplay(activity, destination, activityCurrency);

    // 解析交通信息
    const transportationInfo = TransportationParser.parseTransportationFromDescription(
      activity.description || ''
    );

    return (
      <View key={activity.id} style={styles.activityCard}>
        <TouchableOpacity
          style={styles.activityHeader}
          onPress={() => toggleActivityExpansion(activity.id)}
        >
          <View style={styles.activityTime}>
            <Text style={styles.activityTimeText}>
              {timeStr || '时间待定'}
            </Text>
          </View>

          <View style={styles.activityContent}>
            <Text style={styles.activityTitle}>{activity.title}</Text>
            {activity.location && (
              <View style={styles.activityLocation}>
                <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
                <Text style={styles.activityLocationText}>
                  {typeof activity.location === 'string' ? activity.location : activity.location?.address || '未知位置'}
                </Text>
              </View>
            )}
            {/* 智能预算显示 */}
            <View style={styles.activityBudgetInfo}>
              <Text style={styles.activityCost}>
                {categoryIcon} {costDisplay}
              </Text>
              <Text style={styles.activityCategory}>
                {getCategoryDisplayName(category)}
              </Text>
            </View>
          </View>

          <View style={styles.activityActions}>
            <Ionicons
              name={isExpanded ? "chevron-up" : "chevron-down"}
              size={20}
              color={colors.textSecondary}
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.expandedContent}>
            {activity.description && (
              <Text style={styles.activityDescription}>
                {activity.description}
              </Text>
            )}

            {transportationInfo && (
              <TransportationCard
                transportation={transportationInfo}
                isExpanded={false}
              />
            )}

            {activity.notes && (
              <Text style={styles.activityNotes}>
                📝 备注: {activity.notes}
              </Text>
            )}
          </View>
        )}
      </View>
    );
  };

  if (isLoading && !currentJourney) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!currentJourney) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingErrorContainer}>
          <Text>行程不存在</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.backButton}>返回</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // 🎯 Ultra Think专用视图渲染 (最高优先级)
  if (isUltraThinkData && currentJourney?.metadata?.jsonData) {
    console.log('🎯 渲染Ultra Think专用视图');

    return (
      <SafeAreaView style={styles.container}>
        {renderBudgetBanner()}

        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>

          <Text style={styles.headerTitle} numberOfLines={1}>
            {currentJourney?.title || '精彩之旅'}
          </Text>

          <View style={styles.headerActions}>
            {/* 🔄 切换到传统视图 */}
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => setIsUltraThinkData(false)}
            >
              <Ionicons name="list" size={24} color={colors.primary[500]} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
              <Ionicons name="share-outline" size={24} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
              <Ionicons name="create-outline" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* 🎯 Ultra Think专用适配器 */}
        <UltraThinkJourneyAdapter
          journeyJSON={currentJourney.metadata.jsonData}
          onActivityPress={(activity) => {
            console.log('Ultra Think Activity pressed:', activity.name || activity.title);
          }}
        />
      </SafeAreaView>
    );
  }

  // 🔧 修复版视图渲染 (优先级最高)
  if (useFixedView && currentJourney && activities && activities.length > 0) {
    // 🔧 应用预算修复
    let fixedJourney = currentJourney;
    let fixedActivities = activities;

    if (currentJourney?.metadata?.jsonData) {
      try {
        const originalBudget = currentJourney.budget || 8000;
        const fixedJourneyData = SimpleBudgetFixer.fixJourneyBudget(
          currentJourney.metadata.jsonData,
          originalBudget,
          'MYR'
        );
        fixedJourney = { ...currentJourney, metadata: { ...currentJourney.metadata, jsonData: fixedJourneyData } };

        // 🔧 使用修复后的活动数据
        if (fixedJourneyData.activities) {
          fixedActivities = fixedJourneyData.activities;
        }

        console.log('🔧 修复版视图应用预算修复');
      } catch (error) {
        console.warn('⚠️ 预算修复失败，使用原始数据:', error);
      }
    }

    return (
      <SafeAreaView style={styles.container}>
        {renderBudgetBanner()}

        {/* 头部 */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>

          <Text style={styles.headerTitle} numberOfLines={1}>
            {fixedJourney?.title || localJourney?.title || '未命名行程'}
          </Text>

          <View style={styles.headerActions}>
            {/* 🔧 切换到其他视图 */}
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => setUseFixedView(false)}
            >
              <Ionicons name="options" size={24} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
              <Ionicons name="share-outline" size={24} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
              <Ionicons name="create-outline" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* 🔧 修复版适配器 */}
        <FixedJourneyAdapter
          journey={fixedJourney}
          activities={fixedActivities}
          onActivityPress={(activity) => {
            console.log('Activity pressed:', activity.name || activity.title);
          }}
        />
      </SafeAreaView>
    );
  }

  // 🕐 时间线视图渲染
  if (useNewJSONStructure && currentJourney?.metadata?.jsonData) {
    // 检查是否有时间线数据
    const hasTimelineData = currentJourney.metadata.jsonData.metadata?.dayTimelines;

    if (hasTimelineData && useTimelineView) {
      return (
        <SafeAreaView style={styles.container}>
          {renderBudgetBanner()}

          {/* 头部 */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>

            <Text style={styles.headerTitle} numberOfLines={1}>
              {currentJourney?.title || localJourney?.title || '未命名行程'}
            </Text>

            <View style={styles.headerActions}>
              {/* 🕐 切换到传统视图 */}
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => setUseTimelineView(false)}
              >
                <Ionicons name="list" size={24} color={colors.primary[500]} />
              </TouchableOpacity>
              {/* 🔄 切换到旧版本 */}
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => setUseNewJSONStructure(false)}
              >
                <Ionicons name="swap-horizontal" size={24} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
                <Ionicons name="share-outline" size={24} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
                <Ionicons name="create-outline" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
          </View>

          {/* 🕐 时间线适配器 (已禁用，使用修复版视图) */}
          {/* <TimelineAdapter
            journeyData={currentJourney.metadata.jsonData}
            onDayPress={(dayNumber) => {
              console.log('Day pressed:', dayNumber);
              setCurrentDay(dayNumber);
            }}
            onItemPress={(item) => {
              console.log('Timeline item pressed:', item.name);
              // 可以显示详细信息或导航
            }}
          /> */}
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text style={{ fontSize: 16, color: '#666' }}>
              时间线视图已禁用，请使用修复版视图 (绿色✅按钮)
            </Text>
          </View>
        </SafeAreaView>
      );
    }

    // 降级到JSON适配器
    if (jsonComponentProps) {
      return (
        <SafeAreaView style={styles.container}>
          {renderBudgetBanner()}

          {/* 头部 */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>

            <Text style={styles.headerTitle} numberOfLines={1}>
              {currentJourney?.title || localJourney?.title || '未命名行程'}
            </Text>

            <View style={styles.headerActions}>
              {/* 🕐 切换到时间线视图 */}
              {hasTimelineData && (
                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={() => setUseTimelineView(true)}
                >
                  <Ionicons name="time" size={24} color={colors.primary[500]} />
                </TouchableOpacity>
              )}
              {/* 🔄 切换到旧版本 */}
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => setUseNewJSONStructure(false)}
              >
                <Ionicons name="swap-horizontal" size={24} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
                <Ionicons name="share-outline" size={24} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
                <Ionicons name="create-outline" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
          </View>

          {/* 🎯 JSON数据适配器 */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <JSONDataAdapter
              journeyData={currentJourney.metadata.jsonData}
              componentProps={jsonComponentProps}
              onActivityPress={(activityId) => {
                console.log('Activity pressed:', activityId);
              }}
              onDayPress={(dayNumber) => {
                console.log('Day pressed:', dayNumber);
                setCurrentDay(dayNumber);
              }}
            />
          </ScrollView>
        </SafeAreaView>
      );
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderBudgetBanner()}
      {/* 头部 */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>

        <Text style={styles.headerTitle} numberOfLines={1}>
          {currentJourney?.title || localJourney?.title || '未命名行程'}
        </Text>

        <View style={styles.headerActions}>
          {/* 🔧 修复版视图按钮 (优先推荐) */}
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setUseFixedView(true)}
          >
            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
          </TouchableOpacity>

          {/* 🔄 切换到新视图按钮（如果有JSON数据） */}
          {currentJourney?.metadata?.jsonData && (
            <>
              {/* 🕐 时间线视图按钮 */}
              {currentJourney.metadata.jsonData.metadata?.dayTimelines && (
                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={() => {
                    setUseNewJSONStructure(true);
                    setUseTimelineView(true);
                    setUseFixedView(false);
                  }}
                >
                  <Ionicons name="time" size={24} color={colors.primary[500]} />
                </TouchableOpacity>
              )}
              {/* 🎯 JSON视图按钮 */}
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => {
                  setUseNewJSONStructure(true);
                  setUseTimelineView(false);
                  setUseFixedView(false);
                }}
              >
                <Ionicons name="sparkles" size={24} color={colors.primary[500]} />
              </TouchableOpacity>
            </>
          )}
          <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
            <Ionicons name="share-outline" size={24} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
            <Ionicons name="create-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* 天数导航栏 */}
      {totalDays > 1 && (
        <SimpleDayNavigation
          totalDays={totalDays}
          currentDay={currentDay}
          onDaySelect={handleDaySelect}
        />
      )}

      <FlatList
        ref={scrollViewRef}
        style={styles.content}
        data={flatListData}
        renderItem={renderFlatListItem}
        keyExtractor={(item: any) => item.id}

        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        removeClippedSubviews={true}
        initialNumToRender={3}
        maxToRenderPerBatch={5}
        windowSize={10}
        ListFooterComponent={() => <View style={{ height: 100 }} />}
      />

      {/* 底部操作栏 */}
      <View style={styles.actionBar}>
        <TouchableOpacity
          style={[styles.actionButton, styles.saveButton]}
          onPress={handleSaveJourney}
          disabled={isSaving}
        >
          <Ionicons name="save-outline" size={20} color="#fff" />
          <Text style={styles.saveButtonText}>
            {isSaving ? '保存中...' : '保存'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleShare}
        >
          <Ionicons name="share-outline" size={20} color={colors.primary[500]} />
          <Text style={styles.actionButtonText}>分享</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  // 🔧 错误容器样式
  errorContainer: {
    padding: 20,
    backgroundColor: '#fff3cd',
    margin: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#856404',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.surface,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginHorizontal: spacing[3],
  },
  headerActions: {
    flexDirection: 'row',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButton: {
    color: colors.primary[500],
    marginTop: spacing[2],
  },
  overviewCard: {
    margin: spacing[4],
    padding: spacing[5],
    borderRadius: borderRadius.lg,
    ...shadows.md,
  },
  journeyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.surface,
    marginBottom: spacing[2],
  },
  journeyDescription: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    marginBottom: spacing[4],
    lineHeight: 24,
  },
  journeyMeta: {
    marginBottom: spacing[4],
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  metaText: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    marginLeft: spacing[2],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[2],
  },
  tag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: borderRadius.full,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[1],
  },
  tagText: {
    fontSize: 12,
    color: colors.surface,
  },
  section: {
    padding: spacing[4],
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    gap: spacing[1],
  },
  addButtonText: {
    fontSize: 14,
    color: colors.primary[700],
    fontWeight: '500',
  },
  activitiesList: {
    gap: spacing[3],
  },
  activityCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing[4],
    ...shadows.sm,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  activityTime: {
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    marginRight: spacing[3],
  },
  activityTimeText: {
    fontSize: 12,
    color: colors.primary[700],
    fontWeight: '600',
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[1],
  },
  activityLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[1],
  },
  activityLocationText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: spacing[1],
  },
  activityNotes: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  activityActions: {
    marginLeft: spacing[2],
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing[3],
    marginBottom: spacing[1],
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing[4],
  },
  emptyButton: {
    backgroundColor: colors.primary[500],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[6],
    paddingVertical: spacing[3],
  },
  emptyButtonText: {
    fontSize: 16,
    color: colors.surface,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing[4],
    ...shadows.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing[1],
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  statDivider: {
    width: 1,
    backgroundColor: colors.border,
    marginHorizontal: spacing[3],
  },
  // 新增样式
  daysList: {
    marginTop: spacing[2],
  },
  daySection: {
    marginBottom: spacing[4],
  },
  emptyDay: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing[4],
    alignItems: 'center',
    marginVertical: spacing[2],
  },
  emptyDayText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: spacing[2],
  },
  addDayActivityButton: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: borderRadius.sm,
  },
  addDayActivityText: {
    color: colors.surface,
    fontSize: 14,
    fontWeight: '500',
  },
  // 活动卡片展开相关样式
  activityBudgetInfo: {
    marginTop: spacing[1],
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  activityCost: {
    fontSize: 12,
    color: colors.primary[600],
    fontWeight: '600',
    flex: 1,
  },
  activityCategory: {
    fontSize: 10,
    color: colors.textSecondary,
    backgroundColor: colors.background,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  expandedContent: {
    padding: spacing[3],
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.background,
  },
  activityDescription: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
    marginBottom: spacing[2],
  },
  // 底部操作栏样式
  actionBar: {
    flexDirection: 'row',
    padding: spacing[4],
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingBottom: spacing[6], // 额外的底部间距
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderRadius: borderRadius.md,
    marginRight: spacing[3],
    borderWidth: 1,
    borderColor: colors.primary[500],
  },
  saveButton: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
    flex: 1,
    justifyContent: 'center',
  },
  saveButtonText: {
    color: colors.surface,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: spacing[2],
  },
  actionButtonText: {
    color: colors.primary[500],
    fontSize: 14,
    fontWeight: '500',
    marginLeft: spacing[2],
  },
  // 🆕 视图切换相关样式
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  journeyIcon: {
    fontSize: 24,
    marginRight: 8,
  },
  viewToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  viewToggleText: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  // 🆕 基础信息样式
  basicInfoContainer: {
    marginTop: 12,
    marginBottom: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  infoText: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  detailToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    paddingVertical: 8,
  },
  detailToggleText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 13,
    marginRight: 4,
  },
  detailedInfoContainer: {
    marginTop: 12,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 16,
  },
  detailSection: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 13,
    marginLeft: 8,
    flex: 1,
  },
  // 新增样式
  bannerContainer: {
    paddingVertical: spacing[3],
    paddingHorizontal: spacing[4],
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'transparent', // 初始透明，根据内容调整
  },
  bannerText: {
    color: colors.surface,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});
