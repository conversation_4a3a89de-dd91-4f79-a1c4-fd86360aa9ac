/**
 * 🔍 Debug Master Solver - 检查是否正确调用
 */

import { ultraThinkBridge } from '../services/UltraThinkBridge';

async function debugMasterSolver() {
  console.log('🔍 开始调试 Master Solver 调用链路');
  console.log('='.repeat(60));

  try {
    // 1. 测试桥接器初始化
    console.log('1️⃣ 测试桥接器初始化...');
    await ultraThinkBridge.initialize();
    console.log('✅ 桥接器初始化成功');

    // 2. 测试活动生成调用
    console.log('\n2️⃣ 测试活动生成调用...');
    const testRequest = {
      destination: '吉隆坡',
      duration: 3,
      budget: 6000,
      currency: 'MYR',
      travelers: 2,
      startDate: new Date(),
      preferences: {
        travelStyle: ['文化探索'],
        accommodation: ['酒店'],
        transport: ['公共交通']
      }
    };

    console.log('📤 发送请求:', testRequest);

    const result = await ultraThinkBridge.routeRequest('activity-generation', testRequest);

    console.log('\n3️⃣ 检查响应结果...');
    console.log('✅ 成功:', result.success);
    console.log('📊 数据源:', result.source);
    console.log('⏱️ 执行时间:', result.executionTime, 'ms');
    console.log('🔄 降级使用:', result.fallbackUsed);

    if (result.data) {
      console.log('📋 活动数量:', result.data.activities?.length || 0);
      console.log('💰 总预算:', result.data.summary?.totalCost || 'N/A');
      
      if (result.data.activities && result.data.activities.length > 0) {
        console.log('\n4️⃣ 检查活动质量...');
        const firstActivity = result.data.activities[0];
        console.log('🎯 第一个活动:', {
          name: firstActivity.name,
          type: firstActivity.type,
          cost: firstActivity.cost,
          timing: firstActivity.timing,
          hasExpandedContent: !!firstActivity.expandedContent
        });

        // 检查是否有Master Solver的标识
        const masterSolverActivities = result.data.activities.filter(
          (a: any) => a.metadata?.source === 'ultra_think_master_solver'
        );
        
        console.log('🎯 Master Solver活动数量:', masterSolverActivities.length);
        
        if (masterSolverActivities.length > 0) {
          console.log('🎉 Master Solver正在工作！');
        } else {
          console.log('⚠️ Master Solver可能没有被调用');
        }

        // 检查时间格式
        const timeFormatIssues = result.data.activities.filter((a: any) => {
          const startTime = a.timing?.startTime;
          return !startTime || !/^\d{2}:\d{2}$/.test(startTime);
        });
        console.log('⏰ 时间格式问题:', timeFormatIssues.length);

        // 检查预算
        const totalCost = result.data.activities.reduce(
          (sum: number, a: any) => sum + (a.cost?.amount || 0), 0
        );
        console.log('💰 总费用:', totalCost, testRequest.currency);
        console.log('💰 预算合规:', totalCost <= testRequest.budget ? '✅' : '❌');

        // 检查交通和美食
        const transportCount = result.data.activities.filter((a: any) => a.type === 'transport').length;
        const foodCount = result.data.activities.filter((a: any) => a.type === 'restaurant').length;
        console.log('🚗 交通活动:', transportCount);
        console.log('🍽️ 美食活动:', foodCount);
      }
    }

    console.log('\n5️⃣ 总结...');
    if (result.success && result.data?.activities?.length > 0) {
      console.log('🎉 调用成功！Master Solver应该正在工作');
    } else {
      console.log('❌ 调用失败或返回空数据');
    }

  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
    console.error('错误详情:', error.stack);
  }
}

// 运行调试
if (require.main === module) {
  debugMasterSolver()
    .then(() => {
      console.log('\n✅ 调试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 调试失败:', error);
      process.exit(1);
    });
}

export { debugMasterSolver };
