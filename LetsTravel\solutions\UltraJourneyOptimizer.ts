﻿/**
 * 🚀 Ultra Journey Optimizer - Ultra Think核心模块
 * 集成类型安全、智能LLM管理、精确预算计算、智能时间格式化、活动图标管理
 * 这是整个Ultra Think解决方案的核心整合模块
 */

import { z } from 'zod';

// ===== 类型安全 (Zod Schema) =====

/**
 * 🛡️ 用户偏好验证Schema
 */
export const UserPreferenceSchema = z.object({
  // 基础偏好
  travelStyle: z.array(z.enum(['cultural', 'adventure', 'relaxation', 'food', 'nature', 'luxury', 'budget', 'family'])),
  accommodation: z.array(z.enum(['budget', 'mid_range', 'luxury', 'boutique', 'traditional'])),
  transport: z.array(z.enum(['walking', 'public', 'taxi', 'rental_car', 'bicycle'])),
  pace: z.enum(['slow', 'moderate', 'active', 'intensive']),

  // 扩展偏好
  interests: z.array(z.string()).optional(),
  budgetLevel: z.enum(['budget', 'mid_range', 'luxury']).optional(),
  groupType: z.enum(['solo', 'couple', 'family', 'friends', 'business']).optional(),

  // 特殊需求
  accessibility: z.object({
    mobility: z.boolean().optional(),
    dietary: z.array(z.string()).optional(),
    language: z.string().optional()
  }).optional(),

  // 时间偏好
  timePreferences: z.object({
    earlyBird: z.boolean().optional(),
    nightOwl: z.boolean().optional(),
    flexibleSchedule: z.boolean().optional()
  }).optional()
});

/**
 * 🎯 旅行请求验证Schema
 */
export const TravelRequestSchema = z.object({
  destination: z.string().min(1, "目的地不能为空"),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "日期格式必须为YYYY-MM-DD"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "日期格式必须为YYYY-MM-DD"),
  duration: z.number().min(1).max(30, "行程天数必须在1-30天之间"),
  travelers: z.number().min(1).max(20, "旅行人数必须在1-20人之间"),
  budget: z.number().min(0, "预算必须为正数"),
  currency: z.string().length(3, "货币代码必须为3位字母"),
  preferences: UserPreferenceSchema
});

/**
 * 🎨 活动验证Schema
 */
export const ActivitySchema = z.object({
  id: z.string(),
  name: z.string(),
  nameZh: z.string(),
  type: z.enum(['attraction', 'transport', 'accommodation', 'flight', 'meal', 'shopping']),
  startTime: z.string().regex(/^\d{2}:\d{2}$/, "时间格式必须为HH:MM"),
  endTime: z.string().regex(/^\d{2}:\d{2}$/, "时间格式必须为HH:MM"),
  duration: z.number().min(0),
  cost: z.number().min(0),
  currency: z.string().length(3),
  location: z.object({
    address: z.string(),
    coordinates: z.object({
      lat: z.number(),
      lng: z.number()
    }).optional()
  }).optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  rating: z.number().min(0).max(5).optional(),
  bookingRequired: z.boolean().optional(),
  weatherSensitive: z.boolean().optional()
});

/**
 * 📅 日程计划Schema
 */
export const DayPlanSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  dayNumber: z.number().min(1),
  activities: z.array(ActivitySchema).min(3).max(8),
  totalCost: z.number().min(0),
  totalDuration: z.number().min(180).max(720),
  qualityScore: z.number().min(0).max(100),
  weather: z.object({
    condition: z.string(),
    temperature: z.number(),
    humidity: z.number(),
    precipitation: z.number()
  }).optional()
});

/**
 * 🗺️ 完整行程Schema
 */
export const JourneySchema = z.object({
  id: z.string(),
  destination: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  duration: z.number(),
  travelers: z.number(),
  totalBudget: z.number(),
  currency: z.string(),
  days: z.array(DayPlanSchema),
  overallQualityScore: z.number().min(0).max(100),
  budgetBreakdown: z.object({
    accommodation: z.number(),
    food: z.number(),
    activities: z.number(),
    transport: z.number(),
    shopping: z.number(),
    emergency: z.number().optional()
  }),
  preferences: UserPreferenceSchema,
  generatedAt: z.string(),
  lastModified: z.string()
});

// ===== 类型定义 =====

export type UserPreference = z.infer<typeof UserPreferenceSchema>;
export type TravelRequest = z.infer<typeof TravelRequestSchema>;
export type Activity = z.infer<typeof ActivitySchema>;
export type DayPlan = z.infer<typeof DayPlanSchema>;
export type Journey = z.infer<typeof JourneySchema>;

// ===== 智能LLM管理 =====

/**
 * 🤖 智能LLM管理器
 */
export class IntelligentLLMManager {
  private static instance: IntelligentLLMManager;
  private modelPerformance: Map<string, { successRate: number; avgResponseTime: number; lastUsed: Date }>;
  private fallbackData: Map<string, any>;

  private constructor() {
    this.modelPerformance = new Map();
    this.fallbackData = new Map();
    this.initializeFallbackData();
  }

  static getInstance(): IntelligentLLMManager {
    if (!IntelligentLLMManager.instance) {
      IntelligentLLMManager.instance = new IntelligentLLMManager();
    }
    return IntelligentLLMManager.instance;
  }

  /**
   * 🎯 智能模型选择
   */
  selectOptimalModel(taskType: 'planner' | 'optimizer' | 'analyzer' | 'generator'): string {
    const modelOptions = {
      planner: ['meta-llama/llama-3.3-70b-instruct:free', 'google/gemma-2-9b-it:free'],
      optimizer: ['google/gemma-2-9b-it:free', 'meta-llama/llama-3.2-3b-instruct:free'],
      analyzer: ['google/gemma-2-9b-it:free', 'microsoft/phi-3-mini-128k-instruct:free'],
      generator: ['meta-llama/llama-3.2-3b-instruct:free', 'google/gemma-2-9b-it:free']
    };

    const candidates = modelOptions[taskType];

    // 选择性能最好的模型
    let bestModel = candidates[0];
    let bestScore = 0;

    for (const model of candidates) {
      const performance = this.modelPerformance.get(model);
      if (performance) {
        const score = performance.successRate * 0.7 + (1 / performance.avgResponseTime) * 0.3;
        if (score > bestScore) {
          bestScore = score;
          bestModel = model;
        }
      }
    }

    return bestModel;
  }

  /**
   * 🔄 智能降级响应
   */
  async callWithFallback(
    prompt: string,
    taskType: 'planner' | 'optimizer' | 'analyzer' | 'generator',
    context: any = {}
  ): Promise<{ success: boolean; data: any; source: 'llm' | 'fallback' }> {
    const startTime = Date.now();
    const selectedModel = this.selectOptimalModel(taskType);

    try {
      // 模拟LLM调用 (实际应该调用真实的LLM API)
      const response = await this.simulateLLMCall(selectedModel, prompt, context);

      // 更新性能统计
      const responseTime = Date.now() - startTime;
      this.updateModelPerformance(selectedModel, true, responseTime);

      return {
        success: true,
        data: response,
        source: 'llm'
      };

    } catch (error) {
      console.warn(`LLM调用失败，使用降级数据: ${error.message}`);

      // 更新性能统计
      const responseTime = Date.now() - startTime;
      this.updateModelPerformance(selectedModel, false, responseTime);

      // 使用智能降级数据
      const fallbackResponse = this.generateIntelligentFallback(taskType, context);

      return {
        success: true,
        data: fallbackResponse,
        source: 'fallback'
      };
    }
  }

  /**
   * 📊 更新模型性能统计
   */
  private updateModelPerformance(model: string, success: boolean, responseTime: number): void {
    const current = this.modelPerformance.get(model) || {
      successRate: 0.5,
      avgResponseTime: 3000,
      lastUsed: new Date()
    };

    // 使用指数移动平均更新统计
    const alpha = 0.1;
    current.successRate = current.successRate * (1 - alpha) + (success ? 1 : 0) * alpha;
    current.avgResponseTime = current.avgResponseTime * (1 - alpha) + responseTime * alpha;
    current.lastUsed = new Date();

    this.modelPerformance.set(model, current);
  }

  /**
   * 🎭 模拟LLM调用
   */
  private async simulateLLMCall(model: string, prompt: string, context: any): Promise<any> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));

    // 模拟30%的失败率来测试降级机制
    if (Math.random() < 0.3) {
      throw new Error('模拟LLM调用失败');
    }

    // 根据任务类型返回模拟响应
    if (prompt.includes('生成活动')) {
      return this.generateMockActivities(context);
    } else if (prompt.includes('预算分析')) {
      return this.generateMockBudgetAnalysis(context);
    } else {
      return { message: '模拟LLM响应', model, timestamp: new Date().toISOString() };
    }
  }

  /**
   * 🛡️ 生成智能降级数据
   */
  private generateIntelligentFallback(taskType: string, context: any): any {
    const fallbackKey = `${taskType}_${JSON.stringify(context).slice(0, 50)}`;

    if (this.fallbackData.has(fallbackKey)) {
      return this.fallbackData.get(fallbackKey);
    }

    let fallbackData;
    switch (taskType) {
      case 'planner':
        fallbackData = this.generateFallbackItinerary(context);
        break;
      case 'generator':
        fallbackData = this.generateFallbackActivities(context);
        break;
      case 'analyzer':
        fallbackData = this.generateFallbackAnalysis(context);
        break;
      default:
        fallbackData = { message: '降级响应', source: 'fallback', timestamp: new Date().toISOString() };
    }

    this.fallbackData.set(fallbackKey, fallbackData);
    return fallbackData;
  }

  /**
   * 🗂️ 初始化降级数据
   */
  private initializeFallbackData(): void {
    // 预设一些高质量的降级数据
    this.fallbackData.set('tokyo_cultural', {
      activities: [
        {
          id: 'fallback_1',
          name: 'Senso-ji Temple',
          nameZh: '浅草寺',
          type: 'attraction',
          duration: 120,
          cost: 0,
          description: '东京最古老的寺庙，充满传统文化气息'
        },
        {
          id: 'fallback_2',
          name: 'Tokyo National Museum',
          nameZh: '东京国立博物馆',
          type: 'attraction',
          duration: 180,
          cost: 1000,
          description: '日本最大的博物馆，收藏丰富的文化艺术品'
        }
      ]
    });
  }

  private generateMockActivities(context: any): any {
    return {
      activities: [
        {
          id: 'mock_1',
          name: 'Cultural Experience',
          nameZh: '文化体验',
          type: 'attraction',
          duration: 120,
          cost: 2000
        }
      ]
    };
  }

  private generateMockBudgetAnalysis(context: any): any {
    return {
      breakdown: {
        accommodation: context.budget * 0.35,
        food: context.budget * 0.25,
        activities: context.budget * 0.25,
        transport: context.budget * 0.15
      },
      confidence: 0.85
    };
  }

  private generateFallbackItinerary(context: any): any {
    return {
      days: Array(context.duration || 3).fill(0).map((_, index) => ({
        date: new Date(Date.now() + index * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        dayNumber: index + 1,
        activities: [
          {
            id: `fallback_day${index + 1}_1`,
            name: 'Morning Activity',
            nameZh: '上午活动',
            type: 'attraction',
            startTime: '09:00',
            endTime: '11:00',
            duration: 120,
            cost: 1500,
            currency: context.currency || 'USD'
          }
        ]
      }))
    };
  }

  private generateFallbackActivities(context: any): any {
    return {
      activities: [
        {
          id: 'fallback_activity_1',
          name: 'Local Attraction',
          nameZh: '当地景点',
          type: 'attraction',
          duration: 120,
          cost: 1000,
          currency: context.currency || 'USD'
        }
      ]
    };
  }

  private generateFallbackAnalysis(context: any): any {
    return {
      feasibility: 0.8,
      recommendations: ['建议增加缓冲时间', '考虑天气因素'],
      qualityScore: 75
    };
  }
}

// ===== 精确预算计算 =====

/**
 * 💰 精确预算计算器
 * @deprecated 请使用 TrulyUnifiedBudgetEngine 替代此类
 * @see TrulyUnifiedBudgetEngine
 */
export class PreciseBudgetCalculator {
  private static instance: PreciseBudgetCalculator;
  private seasonalFactors: Map<string, any>;
  private destinationMultipliers: Map<string, any>;

  private constructor() {
    this.seasonalFactors = new Map();
    this.destinationMultipliers = new Map();
    this.initializePricingData();
  }

  static getInstance(): PreciseBudgetCalculator {
    if (!PreciseBudgetCalculator.instance) {
      PreciseBudgetCalculator.instance = new PreciseBudgetCalculator();
    }
    return PreciseBudgetCalculator.instance;
  }

  /**
   * 🎯 计算最优预算分配
   */
  calculateOptimalBudget(
    totalBudget: number,
    currency: string,
    duration: number,
    travelers: number,
    destination: string,
    travelStyle: string[],
    startDate?: string
  ): {
    breakdown: Record<string, number>;
    confidence: number;
    recommendations: string[];
    seasonalFactors: Record<string, number>;
  } {
    // 基础分配比例
    let baseAllocation = {
      accommodation: 0.35,
      food: 0.25,
      activities: 0.20,
      transport: 0.15,
      shopping: 0.05
    };

    // 根据旅行风格调整
    baseAllocation = this.adjustForTravelStyle(baseAllocation, travelStyle);

    // 根据目的地调整
    const destinationMultipliers = this.getDestinationMultipliers(destination);

    // 季节性调整
    const seasonalMultipliers = this.getSeasonalMultipliers(destination, startDate);

    // 计算最终分配
    const finalAllocation: Record<string, number> = {};
    Object.keys(baseAllocation).forEach(category => {
      const baseAmount = totalBudget * baseAllocation[category];
      const destMultiplier = destinationMultipliers[category] || 1.0;
      const seasonMultiplier = seasonalMultipliers[category] || 1.0;

      finalAllocation[category] = Math.round(baseAmount * destMultiplier * seasonMultiplier);
    });

    // 归一化确保总和等于预算
    const totalAllocated = Object.values(finalAllocation).reduce((sum, amount) => sum + amount, 0);
    const ratio = totalBudget / totalAllocated;

    Object.keys(finalAllocation).forEach(category => {
      finalAllocation[category] = Math.round(finalAllocation[category] * ratio);
    });

    // 计算信心度
    const confidence = this.calculateBudgetConfidence(totalBudget, duration, travelers, destination);

    // 生成建议
    const recommendations = this.generateBudgetRecommendations(finalAllocation, travelStyle, destination);

    return {
      breakdown: finalAllocation,
      confidence,
      recommendations,
      seasonalFactors: seasonalMultipliers
    };
  }

  /**
   * 🎨 根据旅行风格调整预算
   */
  private adjustForTravelStyle(allocation: Record<string, number>, travelStyle: string[]): Record<string, number> {
    const adjusted = { ...allocation };

    if (travelStyle.includes('luxury')) {
      adjusted.accommodation += 0.1;
      adjusted.food += 0.05;
      adjusted.shopping += 0.05;
      adjusted.activities -= 0.1;
      adjusted.transport -= 0.1;
    }

    if (travelStyle.includes('budget')) {
      adjusted.accommodation -= 0.1;
      adjusted.food -= 0.05;
      adjusted.activities += 0.1;
      adjusted.transport += 0.05;
    }

    if (travelStyle.includes('food')) {
      adjusted.food += 0.1;
      adjusted.activities -= 0.05;
      adjusted.shopping -= 0.05;
    }

    if (travelStyle.includes('cultural')) {
      adjusted.activities += 0.1;
      adjusted.shopping -= 0.05;
      adjusted.transport -= 0.05;
    }

    return adjusted;
  }

  /**
   * 🌍 获取目的地价格倍数
   */
  private getDestinationMultipliers(destination: string): Record<string, number> {
    const multipliers = this.destinationMultipliers.get(destination) || {
      accommodation: 1.0,
      food: 1.0,
      activities: 1.0,
      transport: 1.0,
      shopping: 1.0
    };
    return multipliers;
  }

  /**
   * 🗓️ 获取季节性价格倍数
   */
  private getSeasonalMultipliers(destination: string, startDate?: string): Record<string, number> {
    if (!startDate) {
      return { accommodation: 1.0, food: 1.0, activities: 1.0, transport: 1.0, shopping: 1.0 };
    }

    const month = new Date(startDate).getMonth() + 1;
    const seasonalKey = `${destination}_${month}`;

    return this.seasonalFactors.get(seasonalKey) || {
      accommodation: 1.0,
      food: 1.0,
      activities: 1.0,
      transport: 1.0,
      shopping: 1.0
    };
  }

  /**
   * 📊 计算预算信心度
   */
  private calculateBudgetConfidence(budget: number, duration: number, travelers: number, destination: string): number {
    let confidence = 0.8;

    const dailyBudgetPerPerson = budget / (duration * travelers);

    if (dailyBudgetPerPerson < 50) {
      confidence -= 0.3;
    } else if (dailyBudgetPerPerson > 300) {
      confidence += 0.1;
    }

    if (['东京', '京都', 'Tokyo', 'Kyoto'].includes(destination) && dailyBudgetPerPerson < 100) {
      confidence -= 0.2;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * 💡 生成预算建议
   */
  private generateBudgetRecommendations(
    allocation: Record<string, number>,
    travelStyle: string[],
    destination: string
  ): string[] {
    const recommendations: string[] = [];

    if (allocation.accommodation > allocation.food + allocation.activities) {
      recommendations.push('住宿预算占比较高，考虑选择性价比更好的住宿选项');
    }

    if (allocation.activities < allocation.food * 0.5) {
      recommendations.push('活动预算较低，可能影响旅行体验的丰富度');
    }

    if (travelStyle.includes('food') && allocation.food < allocation.accommodation * 0.5) {
      recommendations.push('作为美食爱好者，建议增加餐饮预算');
    }

    return recommendations;
  }

  /**
   * 🗂️ 初始化价格数据
   */
  private initializePricingData(): void {
    // 目的地价格倍数
    this.destinationMultipliers.set('东京', {
      accommodation: 1.3,
      food: 1.1,
      activities: 1.2,
      transport: 0.9,
      shopping: 1.4
    });

    this.destinationMultipliers.set('京都', {
      accommodation: 1.2,
      food: 1.0,
      activities: 1.1,
      transport: 0.9,
      shopping: 1.2
    });

    // 季节性价格倍数 (东京樱花季)
    this.seasonalFactors.set('东京_4', {
      accommodation: 1.3,
      food: 1.1,
      activities: 1.2,
      transport: 1.0,
      shopping: 1.0
    });
  }
}

// ===== 智能时间格式化 =====

/**
 * ⏰ 智能时间格式化器
 */
export class SmartTimeFormatter {
  /**
   * 📅 智能分配活动时间
   */
  static distributeActivitiesInDay(
    activities: Activity[],
    startTime: string = '09:00',
    endTime: string = '21:00',
    breakDuration: number = 15
  ): Activity[] {
    const start = this.parseTime(startTime);
    const end = this.parseTime(endTime);
    const totalAvailableTime = end - start;

    const totalActivityTime = activities.reduce((sum, activity) => sum + activity.duration, 0);
    const totalBreakTime = (activities.length - 1) * breakDuration;
    const requiredTime = totalActivityTime + totalBreakTime;

    if (requiredTime > totalAvailableTime) {
      console.warn('活动时间超出可用时间，将进行压缩');
    }

    const scheduledActivities: Activity[] = [];
    let currentTime = start;

    activities.forEach((activity, index) => {
      const startTimeStr = this.formatMinutesToTime(currentTime);
      const endTimeStr = this.formatMinutesToTime(currentTime + activity.duration);

      scheduledActivities.push({
        ...activity,
        startTime: startTimeStr,
        endTime: endTimeStr
      });

      currentTime += activity.duration;

      if (index < activities.length - 1) {
        currentTime += breakDuration;
      }
    });

    return scheduledActivities;
  }

  /**
   * ⚠️ 检测时间冲突
   */
  static detectTimeConflicts(activities: Activity[]): Array<{
    type: 'overlap' | 'tight_schedule';
    activities: string[];
    description: string;
    severity: 'low' | 'medium' | 'high';
  }> {
    const conflicts = [];

    for (let i = 0; i < activities.length - 1; i++) {
      const current = activities[i];
      const next = activities[i + 1];

      const currentEnd = this.parseTime(current.endTime);
      const nextStart = this.parseTime(next.startTime);

      if (currentEnd > nextStart) {
        conflicts.push({
          type: 'overlap' as const,
          activities: [current.id, next.id],
          description: `${current.name} 和 ${next.name} 时间重叠`,
          severity: 'high' as const
        });
      } else if (nextStart - currentEnd < 10) {
        conflicts.push({
          type: 'tight_schedule' as const,
          activities: [current.id, next.id],
          description: `${current.name} 和 ${next.name} 之间时间过紧`,
          severity: 'medium' as const
        });
      }
    }

    return conflicts;
  }

  /**
   * 🔧 解析时间字符串为分钟数
   */
  private static parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + (minutes || 0);
  }

  /**
   * 🔧 将分钟数格式化为时间字符串
   */
  private static formatMinutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}

// ===== 活动图标管理 =====

/**
 * 🎨 活动图标管理器
 */
export class ActivityIconManager {
  private static iconMap = new Map([
    // 景点类
    ['attraction', { icon: '🏛️', color: '#FF6B6B', category: 'sightseeing' }],
    ['temple', { icon: '⛩️', color: '#FF8E53', category: 'cultural' }],
    ['museum', { icon: '🏛️', color: '#4ECDC4', category: 'cultural' }],
    ['park', { icon: '🌳', color: '#45B7D1', category: 'nature' }],

    // 交通类
    ['transport', { icon: '🚇', color: '#FFEAA7', category: 'transport' }],
    ['flight', { icon: '✈️', color: '#74B9FF', category: 'transport' }],
    ['train', { icon: '🚅', color: '#0984E3', category: 'transport' }],

    // 住宿类
    ['accommodation', { icon: '🏨', color: '#A29BFE', category: 'accommodation' }],
    ['hotel', { icon: '🏨', color: '#6C5CE7', category: 'accommodation' }],

    // 餐饮类
    ['meal', { icon: '🍽️', color: '#E17055', category: 'dining' }],
    ['restaurant', { icon: '🍽️', color: '#E17055', category: 'dining' }],
    ['sushi', { icon: '🍣', color: '#FF7675', category: 'dining' }],

    // 购物类
    ['shopping', { icon: '🛍️', color: '#FD79A8', category: 'shopping' }],
    ['market', { icon: '🏪', color: '#FDCB6E', category: 'shopping' }]
  ]);

  private static colorSchemes = new Map([
    ['sightseeing', { primary: '#FF6B6B', secondary: '#FFE0E0', accent: '#FF5252' }],
    ['cultural', { primary: '#4ECDC4', secondary: '#E0F7F5', accent: '#26A69A' }],
    ['nature', { primary: '#45B7D1', secondary: '#E3F2FD', accent: '#2196F3' }],
    ['transport', { primary: '#FFEAA7', secondary: '#FFF8E1', accent: '#FFC107' }],
    ['accommodation', { primary: '#A29BFE', secondary: '#F3E5F5', accent: '#9C27B0' }],
    ['dining', { primary: '#E17055', secondary: '#FFF3E0', accent: '#FF9800' }],
    ['shopping', { primary: '#FD79A8', secondary: '#FCE4EC', accent: '#E91E63' }],
    ['general', { primary: '#DDD', secondary: '#F5F5F5', accent: '#999' }]
  ]);

  /**
   * 🎯 获取活动图标
   */
  static getActivityIcon(activityType: string, activityName?: string): {
    icon: string;
    color: string;
    category: string;
  } {
    // 首先尝试精确匹配
    if (this.iconMap.has(activityType)) {
      return this.iconMap.get(activityType)!;
    }

    // 基于活动名称的智能匹配
    if (activityName) {
      const name = activityName.toLowerCase();

      if (name.includes('temple') || name.includes('寺')) {
        return this.iconMap.get('temple')!;
      }
      if (name.includes('museum') || name.includes('博物馆')) {
        return this.iconMap.get('museum')!;
      }
      if (name.includes('sushi') || name.includes('寿司')) {
        return this.iconMap.get('sushi')!;
      }
      if (name.includes('market') || name.includes('市场')) {
        return this.iconMap.get('market')!;
      }
    }

    // 基于类型的默认匹配
    const typeMapping: Record<string, string> = {
      'attraction': 'attraction',
      'transport': 'transport',
      'accommodation': 'accommodation',
      'flight': 'flight',
      'meal': 'meal',
      'shopping': 'shopping'
    };

    const mappedType = typeMapping[activityType];
    if (mappedType && this.iconMap.has(mappedType)) {
      return this.iconMap.get(mappedType)!;
    }

    // 默认图标
    return { icon: '📍', color: '#DDD', category: 'general' };
  }

  /**
   * 🎨 获取类别颜色方案
   */
  static getCategoryColorScheme(category: string): {
    primary: string;
    secondary: string;
    accent: string;
  } {
    return this.colorSchemes.get(category) || this.colorSchemes.get('general')!;
  }

  /**
   * 🖼️ 生成图标样式
   */
  static generateIconStyle(
    activityType: string,
    activityName?: string,
    size: 'small' | 'medium' | 'large' = 'medium'
  ): {
    icon: string;
    backgroundColor: string;
    borderColor: string;
    color: string;
    width: number;
    height: number;
    fontSize: number;
    borderRadius: number;
    borderWidth: number;
  } {
    const iconInfo = this.getActivityIcon(activityType, activityName);
    const colorScheme = this.getCategoryColorScheme(iconInfo.category);

    const sizes = {
      small: { width: 24, height: 24, fontSize: 12 },
      medium: { width: 32, height: 32, fontSize: 16 },
      large: { width: 48, height: 48, fontSize: 24 }
    };

    const sizeConfig = sizes[size];

    return {
      icon: iconInfo.icon,
      backgroundColor: colorScheme.secondary,
      borderColor: colorScheme.primary,
      color: colorScheme.accent,
      width: sizeConfig.width,
      height: sizeConfig.height,
      fontSize: sizeConfig.fontSize,
      borderRadius: sizeConfig.width / 2,
      borderWidth: 2
    };
  }
}

// ===== 导出所有核心功能 =====

export {
  // Schema验证
  UserPreferenceSchema,
  TravelRequestSchema,
  ActivitySchema,
  DayPlanSchema,
  JourneySchema,

  // 核心管理器
  IntelligentLLMManager,
  PreciseBudgetCalculator,
  SmartTimeFormatter,
  ActivityIconManager
};
