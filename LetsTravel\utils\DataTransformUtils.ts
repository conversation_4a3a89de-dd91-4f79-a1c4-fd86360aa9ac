/**
 * 🔄 数据转换工具 - Ultra Think系统性修复
 * 解决前端旧系统与Ultra Think新系统的数据格式冲突问题
 */

import { TimeFormatter } from './TimeFormatter';

// ===== 类型定义 =====

export interface LegacyActivity {
  id: string;
  name: string;
  timeSlot: {
    start: string;
    end: string;
    duration: string;
  };
  category?: string;
  location?: string;
  cost?: number;
  description?: string;
}

export interface UltraThinkActivity {
  id: string;
  name: string;
  name_zh?: string;
  startTime: string;
  endTime: string;
  duration: number;
  timing: {
    startTime: string;
    endTime: string;
    duration: number;
    date: string;
    day: number;
  };
  type: string;
  location: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  cost: {
    amount: number;
    currency: string;
    priceLevel: string;
  };
  description?: string;
  highlights?: string[];
  tips?: string[];
  rating?: number;
}

export interface LegacyDayData {
  day: number;
  date: string;
  timeline: Array<{
    time: string;
    name: string;
    icon: string;
    duration: string;
  }>;
  activitiesDetailed?: LegacyActivity[];
  budget: {
    total: string;
    breakdown: string;
  };
  transportation: {
    totalTime: string;
    totalCost: string;
  };
}

// ===== 核心转换类 =====

export class DataTransformUtils {
  
  /**
   * 🔄 将Ultra Think活动转换为前端兼容格式 - 修复版
   */
  static transformUltraThinkToLegacy(ultraActivity: UltraThinkActivity): LegacyActivity {
    // 使用TimeFormatter确保时间格式统一
    const startTime = ultraActivity.startTime || ultraActivity.timing.startTime;
    const endTime = ultraActivity.endTime || ultraActivity.timing.endTime;

    // 格式化开始和结束时间
    const formattedStart = TimeFormatter.formatSingleTime(startTime);
    const formattedEnd = TimeFormatter.formatSingleTime(endTime);

    return {
      id: ultraActivity.id,
      name: ultraActivity.name_zh || ultraActivity.name,
      timeSlot: {
        start: formattedStart,
        end: formattedEnd,
        duration: TimeFormatter.formatDuration(ultraActivity.duration || ultraActivity.timing.duration)
      },
      category: this.mapActivityTypeToCategory(ultraActivity.type),
      location: ultraActivity.location.name,
      cost: ultraActivity.cost.amount,
      description: ultraActivity.description || this.generateDescription(ultraActivity)
    };
  }

  /**
   * 🔄 将Ultra Think活动列表转换为DayCard格式
   */
  static transformActivitiesToDayData(
    activities: UltraThinkActivity[], 
    day: number,
    totalBudget?: number,
    currency?: string
  ): LegacyDayData {
    // 筛选当天活动
    const dayActivities = activities.filter(activity => 
      activity.timing.day === day
    );

    // 转换为timeline格式
    const timeline = dayActivities.map(activity => ({
      time: TimeFormatter.formatSingleTime(activity.startTime || activity.timing.startTime),
      name: this.truncateText(activity.name_zh || activity.name, 15),
      icon: this.getActivityIcon(activity.type),
      duration: TimeFormatter.formatDuration(activity.duration || activity.timing.duration)
    }));

    // 转换为详细活动格式
    const activitiesDetailed = dayActivities.map(activity => 
      this.transformUltraThinkToLegacy(activity)
    );

    // 计算预算信息
    const dayBudget = dayActivities.reduce((sum, activity) => 
      sum + (activity.cost.amount || 0), 0
    );

    // 计算交通信息
    const transportActivities = dayActivities.filter(activity => 
      activity.type === 'transport'
    );
    const totalTransportTime = transportActivities.reduce((sum, activity) => 
      sum + (activity.duration || activity.timing.duration), 0
    );
    const totalTransportCost = transportActivities.reduce((sum, activity) => 
      sum + (activity.cost.amount || 0), 0
    );

    // 获取日期
    const activityDate = dayActivities.length > 0 
      ? dayActivities[0].timing.date 
      : new Date().toISOString().split('T')[0];

    return {
      day,
      date: this.formatDate(activityDate),
      timeline,
      activitiesDetailed,
      budget: {
        total: `${currency || 'MYR'} ${dayBudget.toFixed(0)}`,
        breakdown: this.generateBudgetBreakdown(dayActivities, currency || 'MYR')
      },
      transportation: {
        totalTime: TimeFormatter.formatDuration(totalTransportTime),
        totalCost: `${currency || 'MYR'} ${totalTransportCost.toFixed(0)}`
      }
    };
  }

  /**
   * 🎯 活动类型映射
   */
  private static mapActivityTypeToCategory(type: string): string {
    const categoryMap: Record<string, string> = {
      'attraction': '景点',
      'meal': '餐饮',
      'transport': '交通',
      'accommodation': '住宿',
      'flight': '航班',
      'shopping': '购物',
      'entertainment': '娱乐',
      'culture': '文化',
      'nature': '自然',
      'adventure': '探险'
    };
    
    return categoryMap[type] || '其他';
  }

  /**
   * 🎨 获取活动图标
   */
  private static getActivityIcon(type: string): string {
    const iconMap: Record<string, string> = {
      'attraction': '🏛️',
      'meal': '🍽️',
      'transport': '🚗',
      'accommodation': '🏨',
      'flight': '✈️',
      'shopping': '🛍️',
      'entertainment': '🎭',
      'culture': '🎨',
      'nature': '🌿',
      'adventure': '🏔️'
    };
    
    return iconMap[type] || '📍';
  }

  /**
   * 📝 生成活动描述
   */
  private static generateDescription(activity: UltraThinkActivity): string {
    const parts = [];
    
    if (activity.highlights && activity.highlights.length > 0) {
      parts.push(`亮点: ${activity.highlights.slice(0, 2).join(', ')}`);
    }
    
    if (activity.tips && activity.tips.length > 0) {
      parts.push(`小贴士: ${activity.tips[0]}`);
    }
    
    if (activity.rating) {
      parts.push(`评分: ${activity.rating}/5`);
    }
    
    return parts.length > 0 
      ? parts.join(' | ')
      : `探索${activity.location.name}的精彩体验`;
  }

  /**
   * 💰 生成预算分解
   */
  private static generateBudgetBreakdown(activities: UltraThinkActivity[], currency: string): string {
    const breakdown: Record<string, number> = {};
    
    activities.forEach(activity => {
      const category = this.mapActivityTypeToCategory(activity.type);
      breakdown[category] = (breakdown[category] || 0) + (activity.cost.amount || 0);
    });
    
    const parts = Object.entries(breakdown)
      .filter(([_, amount]) => amount > 0)
      .map(([category, amount]) => `${category}${amount.toFixed(0)}`)
      .slice(0, 3); // 只显示前3个类别
    
    return parts.join(' | ') || `${currency} 0`;
  }

  /**
   * 📅 格式化日期
   */
  private static formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric',
        weekday: 'short'
      });
    } catch (error) {
      return dateString;
    }
  }

  /**
   * ✂️ 截断文本
   */
  private static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  /**
   * 🔄 将Ultra Think活动转换为Activity接口格式
   */
  static transformUltraThinkToActivity(ultraActivity: UltraThinkActivity): any {
    return {
      id: ultraActivity.id,
      title: ultraActivity.name_zh || ultraActivity.name,
      description: ultraActivity.description || this.generateDescription(ultraActivity),
      type: ultraActivity.type,
      location: {
        name: ultraActivity.location.name,
        address: ultraActivity.location.address,
        coordinates: ultraActivity.location.coordinates
      },
      startTime: ultraActivity.startTime || ultraActivity.timing.startTime,
      endTime: ultraActivity.endTime || ultraActivity.timing.endTime,
      duration: ultraActivity.duration || ultraActivity.timing.duration,
      cost: ultraActivity.cost.amount,
      currency: ultraActivity.cost.currency,
      notes: ultraActivity.tips ? ultraActivity.tips.join(' | ') : undefined,
      dayNumber: ultraActivity.timing.day,
      createdAt: new Date(),
      updatedAt: new Date(),
      isCompleted: false
    };
  }

  /**
   * 🔄 批量转换Ultra Think活动列表
   */
  static transformUltraThinkActivitiesToActivityList(ultraActivities: UltraThinkActivity[]): any[] {
    return ultraActivities.map(activity => this.transformUltraThinkToActivity(activity));
  }

  /**
   * 💰 将Ultra Think活动转换为预算分解格式
   */
  static transformActivitiesToBudgetBreakdown(activities: UltraThinkActivity[]): {
    total: number;
    breakdown: {
      transport: number;
      accommodation: number;
      food: number;
      attractions: number;
      shopping: number;
      other: number;
    };
  } {
    const breakdown = {
      transport: 0,
      accommodation: 0,
      food: 0,
      attractions: 0,
      shopping: 0,
      other: 0
    };

    activities.forEach(activity => {
      const amount = activity.cost.amount || 0;

      switch (activity.type) {
        case 'transport':
          breakdown.transport += amount;
          break;
        case 'accommodation':
          breakdown.accommodation += amount;
          break;
        case 'meal':
          breakdown.food += amount;
          break;
        case 'attraction':
          breakdown.attractions += amount;
          break;
        case 'shopping':
          breakdown.shopping += amount;
          break;
        default:
          breakdown.other += amount;
          break;
      }
    });

    const total = Object.values(breakdown).reduce((sum, amount) => sum + amount, 0);

    return { total, breakdown };
  }

  /**
   * 🔄 Ultra Think响应数据适配器 - 核心数据转换函数
   * 将Ultra Think系统返回的数据转换为前端组件期望的格式
   */
  static adaptUltraThinkResponse(ultraThinkResponse: any): {
    journeyData: any;
    dayCards: LegacyDayData[];
    activities: any[];
    budgetData: any;
  } {
    if (!ultraThinkResponse || !ultraThinkResponse.activities) {
      console.warn('⚠️ Ultra Think响应数据为空或格式不正确');
      return {
        journeyData: null,
        dayCards: [],
        activities: [],
        budgetData: null
      };
    }

    const activities = ultraThinkResponse.activities as UltraThinkActivity[];

    // 1. 转换为Journey数据格式
    const journeyData = {
      id: `journey_${Date.now()}`,
      title: ultraThinkResponse.summary?.title || '精彩之旅',
      destination: activities[0]?.location?.name || '未知目的地',
      duration: Math.max(...activities.map(a => a.timing.day)),
      activities: this.transformUltraThinkActivitiesToActivityList(activities),
      summary: ultraThinkResponse.summary,
      optimization: ultraThinkResponse.optimization,
      budget: ultraThinkResponse.summary?.budget,
      currency: activities[0]?.cost?.currency || 'MYR',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 2. 按天分组生成DayCard数据
    const maxDay = Math.max(...activities.map(a => a.timing.day));
    const dayCards: LegacyDayData[] = [];

    for (let day = 1; day <= maxDay; day++) {
      const dayCard = this.transformActivitiesToDayData(
        activities,
        day,
        ultraThinkResponse.summary?.budget?.total,
        activities[0]?.cost?.currency || 'MYR'
      );
      dayCards.push(dayCard);
    }

    // 3. 生成预算数据
    const budgetData = this.transformActivitiesToBudgetBreakdown(activities);

    console.log(`✅ Ultra Think数据适配完成: ${activities.length}个活动, ${dayCards.length}天行程`);

    return {
      journeyData,
      dayCards,
      activities: journeyData.activities,
      budgetData
    };
  }

  /**
   * 🎯 智能数据格式检测和转换
   * 自动检测数据格式并转换为目标格式
   */
  static smartDataTransform(inputData: any, targetFormat: 'dayCard' | 'activity' | 'budget'): any {
    // 检测输入数据格式
    if (this.isUltraThinkFormat(inputData)) {
      console.log('🔍 检测到Ultra Think格式数据');

      switch (targetFormat) {
        case 'dayCard':
          if (Array.isArray(inputData)) {
            // 输入是活动数组
            return inputData.map((activity, index) =>
              this.transformActivitiesToDayData([activity], activity.timing?.day || index + 1)
            );
          } else if (inputData.activities) {
            // 输入是完整响应
            return this.adaptUltraThinkResponse(inputData).dayCards;
          }
          break;

        case 'activity':
          if (Array.isArray(inputData)) {
            return this.transformUltraThinkActivitiesToActivityList(inputData);
          } else if (inputData.activities) {
            return this.transformUltraThinkActivitiesToActivityList(inputData.activities);
          }
          break;

        case 'budget':
          if (Array.isArray(inputData)) {
            return this.transformActivitiesToBudgetBreakdown(inputData);
          } else if (inputData.activities) {
            return this.transformActivitiesToBudgetBreakdown(inputData.activities);
          }
          break;
      }
    }

    // 如果不是Ultra Think格式，直接返回
    console.log('🔍 数据格式未知或已是目标格式，直接返回');
    return inputData;
  }

  /**
   * 🔍 检测是否为Ultra Think数据格式
   */
  private static isUltraThinkFormat(data: any): boolean {
    if (!data) return false;

    // 检测单个活动
    if (data.timing && data.location && data.cost && typeof data.cost === 'object') {
      return true;
    }

    // 检测活动数组
    if (Array.isArray(data) && data.length > 0 && data[0].timing) {
      return true;
    }

    // 检测完整响应
    if (data.activities && Array.isArray(data.activities) && data.activities.length > 0) {
      return data.activities[0].timing !== undefined;
    }

    return false;
  }
}
