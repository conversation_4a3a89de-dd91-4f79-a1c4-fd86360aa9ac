/**
 * ⏰ 智能时间处理器
 * 解决所有时间相关的显示问题，包括日期格式化和时间范围处理
 */

export class SmartTimeHandler {
  
  /**
   * 📅 智能日期格式化
   * 支持多种输入格式，确保输出一致性
   */
  static formatDate(date: Date | string | number): string {
    try {
      let safeDate: Date;
      
      if (date instanceof Date) {
        safeDate = date;
      } else if (typeof date === 'string') {
        safeDate = new Date(date);
      } else if (typeof date === 'number') {
        safeDate = new Date(date);
      } else {
        // 默认日期
        safeDate = new Date('2025-12-15');
      }
      
      // 验证日期有效性
      if (isNaN(safeDate.getTime())) {
        console.warn('⚠️ 无效日期，使用默认日期:', date);
        safeDate = new Date('2025-12-15');
      }
      
      return safeDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
    } catch (error) {
      console.error('❌ 日期格式化失败:', error);
      return '2025年12月15日'; // 安全默认值
    }
  }
  
  /**
   * 🕐 智能时间格式化
   * 确保时间格式为 HH:MM
   */
  static formatTime(time: any): string {
    try {
      // 🔧 确保time是字符串类型
      const safeTime = String(time || '09:00');
      
      // 如果已经是正确格式
      if (safeTime.match(/^\d{2}:\d{2}$/)) {
        return safeTime;
      }
      
      // 如果是 H:M 格式，补零
      if (safeTime.includes(':')) {
        const [hours, minutes] = safeTime.split(':');
        const paddedHours = hours.padStart(2, '0');
        const paddedMinutes = minutes.padStart(2, '0');
        return `${paddedHours}:${paddedMinutes}`;
      }
      
      // 如果是纯数字（如 9 表示 09:00）
      const numTime = parseInt(safeTime);
      if (!isNaN(numTime) && numTime >= 0 && numTime <= 23) {
        return `${numTime.toString().padStart(2, '0')}:00`;
      }
      
      // 默认返回
      return '09:00';
      
    } catch (error) {
      console.error('❌ 时间格式化失败:', error, 'input:', time);
      return '09:00';
    }
  }
  
  /**
   * ⏰ 智能时间范围格式化
   */
  static formatTimeRange(startTime: any, endTime: any): string {
    const formattedStart = this.formatTime(startTime);
    const formattedEnd = this.formatTime(endTime);
    return `${formattedStart} - ${formattedEnd}`;
  }
  
  /**
   * 📊 计算日期显示（基于天数）
   */
  static getDateDisplay(dayNumber: number, baseDate?: Date | string): string {
    try {
      const base = baseDate ? new Date(baseDate) : new Date('2025-12-15');
      
      if (isNaN(base.getTime())) {
        console.warn('⚠️ 基础日期无效，使用默认日期');
        return this.formatDate(new Date('2025-12-15'));
      }
      
      const targetDate = new Date(base);
      targetDate.setDate(base.getDate() + dayNumber - 1);
      
      return this.formatDate(targetDate);
      
    } catch (error) {
      console.error('❌ 日期计算失败:', error);
      return '2025年12月15日';
    }
  }
  
  /**
   * 🌤️ 获取天气信息
   */
  static getWeatherInfo(dayNumber: number): string {
    const weatherOptions = [
      '🌤️ 晴朗 8°C',
      '☁️ 多云 6°C',
      '🌧️ 小雨 5°C'
    ];
    
    const index = (dayNumber - 1) % weatherOptions.length;
    return weatherOptions[index];
  }
  
  /**
   * ⏱️ 计算活动持续时间
   */
  static calculateDuration(startTime: any, endTime: any): number {
    try {
      const start = this.timeToMinutes(startTime);
      const end = this.timeToMinutes(endTime);
      
      if (end > start) {
        return end - start;
      } else {
        // 跨天情况
        return (24 * 60 - start) + end;
      }
      
    } catch (error) {
      console.error('❌ 持续时间计算失败:', error);
      return 60; // 默认1小时
    }
  }
  
  /**
   * 🔢 时间转换为分钟数
   */
  private static timeToMinutes(time: any): number {
    const timeStr = this.formatTime(time);
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }
  
  /**
   * 📅 验证日期范围
   */
  static validateDateRange(startDate: any, endDate: any): { start: Date, end: Date, days: number } {
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new Error('Invalid date range');
      }
      
      const timeDiff = end.getTime() - start.getTime();
      const days = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
      
      return {
        start,
        end,
        days: Math.max(1, Math.min(days, 7)) // 限制在1-7天
      };
      
    } catch (error) {
      console.error('❌ 日期范围验证失败:', error);
      
      // 返回默认的3天行程
      const defaultStart = new Date('2025-12-15');
      const defaultEnd = new Date('2025-12-17');
      
      return {
        start: defaultStart,
        end: defaultEnd,
        days: 3
      };
    }
  }
  
  /**
   * 🕐 智能时间排序
   */
  static sortActivitiesByTime(activities: any[]): any[] {
    return [...activities].sort((a, b) => {
      const timeA = a.startTime || a.timing?.startTime || '09:00';
      const timeB = b.startTime || b.timing?.startTime || '09:00';
      
      const minutesA = this.timeToMinutes(timeA);
      const minutesB = this.timeToMinutes(timeB);
      
      return minutesA - minutesB;
    });
  }
  
  /**
   * 📊 生成时间统计
   */
  static generateTimeStats(activities: any[]): {
    totalDuration: number;
    averageDuration: number;
    earliestStart: string;
    latestEnd: string;
  } {
    if (!activities || activities.length === 0) {
      return {
        totalDuration: 0,
        averageDuration: 0,
        earliestStart: '09:00',
        latestEnd: '18:00'
      };
    }
    
    let totalDuration = 0;
    let earliestMinutes = 24 * 60;
    let latestMinutes = 0;
    
    activities.forEach(activity => {
      const startTime = activity.startTime || activity.timing?.startTime || '09:00';
      const endTime = activity.endTime || activity.timing?.endTime || '10:00';
      
      const duration = this.calculateDuration(startTime, endTime);
      totalDuration += duration;
      
      const startMinutes = this.timeToMinutes(startTime);
      const endMinutes = this.timeToMinutes(endTime);
      
      earliestMinutes = Math.min(earliestMinutes, startMinutes);
      latestMinutes = Math.max(latestMinutes, endMinutes);
    });
    
    return {
      totalDuration,
      averageDuration: Math.round(totalDuration / activities.length),
      earliestStart: this.minutesToTime(earliestMinutes),
      latestEnd: this.minutesToTime(latestMinutes)
    };
  }
  
  /**
   * 🔢 分钟数转换为时间
   */
  private static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}
