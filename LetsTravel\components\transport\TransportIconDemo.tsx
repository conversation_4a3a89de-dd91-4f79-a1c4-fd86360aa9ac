/**
 * 🚇 交通图标演示组件
 * 
 * 用于展示和测试统一的交通图标系统
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { 
  TRANSPORT_ICONS, 
  TRANSPORT_COLORS, 
  TRANSPORT_NAMES_ZH,
  getTransportIcon,
  getTransportColor,
  getTransportName,
  getTransportInfo,
  searchTransportTypes
} from '../../constants/TransportationIcons';

/**
 * 🎯 交通图标演示组件
 */
export const TransportIconDemo: React.FC = () => {
  // 主要交通类型
  const mainTransportTypes = [
    'walking', 'subway', 'bus', 'taxi', 'train', 'flight', 'ferry', 'bicycle', 'car'
  ];

  // 测试搜索功能
  const searchResults = searchTransportTypes('地铁', 'zh');

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🚇 统一交通图标系统演示</Text>
      
      {/* 主要交通类型展示 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>主要交通类型</Text>
        <View style={styles.iconGrid}>
          {mainTransportTypes.map(type => {
            const info = getTransportInfo(type, 'zh');
            return (
              <View key={type} style={styles.iconItem}>
                <View style={[styles.iconCircle, { backgroundColor: info.color }]}>
                  <Text style={styles.iconText}>{info.icon}</Text>
                </View>
                <Text style={styles.iconLabel}>{info.name}</Text>
                <Text style={styles.iconType}>{type}</Text>
              </View>
            );
          })}
        </View>
      </View>

      {/* 所有图标展示 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>所有交通图标</Text>
        <View style={styles.iconGrid}>
          {Object.entries(TRANSPORT_ICONS).map(([type, icon]) => {
            if (type === 'default' || type === 'unknown') return null;
            
            const color = TRANSPORT_COLORS[type as keyof typeof TRANSPORT_COLORS] || TRANSPORT_COLORS.default;
            const name = TRANSPORT_NAMES_ZH[type as keyof typeof TRANSPORT_NAMES_ZH] || type;
            
            return (
              <View key={type} style={styles.iconItem}>
                <View style={[styles.iconCircle, { backgroundColor: color }]}>
                  <Text style={styles.iconText}>{icon}</Text>
                </View>
                <Text style={styles.iconLabel}>{name}</Text>
                <Text style={styles.iconType}>{type}</Text>
              </View>
            );
          })}
        </View>
      </View>

      {/* 功能测试 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>功能测试</Text>
        
        {/* 图标获取测试 */}
        <View style={styles.testItem}>
          <Text style={styles.testLabel}>图标获取测试:</Text>
          <Text style={styles.testResult}>
            getTransportIcon('subway') = {getTransportIcon('subway')}
          </Text>
          <Text style={styles.testResult}>
            getTransportIcon('地铁') = {getTransportIcon('地铁')}
          </Text>
          <Text style={styles.testResult}>
            getTransportIcon('unknown') = {getTransportIcon('unknown')}
          </Text>
        </View>

        {/* 颜色获取测试 */}
        <View style={styles.testItem}>
          <Text style={styles.testLabel}>颜色获取测试:</Text>
          <View style={styles.colorTest}>
            <View style={[styles.colorSample, { backgroundColor: getTransportColor('subway') }]} />
            <Text style={styles.testResult}>subway = {getTransportColor('subway')}</Text>
          </View>
          <View style={styles.colorTest}>
            <View style={[styles.colorSample, { backgroundColor: getTransportColor('bus') }]} />
            <Text style={styles.testResult}>bus = {getTransportColor('bus')}</Text>
          </View>
        </View>

        {/* 名称获取测试 */}
        <View style={styles.testItem}>
          <Text style={styles.testLabel}>名称获取测试:</Text>
          <Text style={styles.testResult}>
            getTransportName('subway', 'zh') = {getTransportName('subway', 'zh')}
          </Text>
          <Text style={styles.testResult}>
            getTransportName('subway', 'en') = {getTransportName('subway', 'en')}
          </Text>
        </View>

        {/* 搜索功能测试 */}
        <View style={styles.testItem}>
          <Text style={styles.testLabel}>搜索功能测试 (搜索"地铁"):</Text>
          {searchResults.map((result, index) => (
            <View key={index} style={styles.searchResult}>
              <Text style={styles.iconText}>{result.icon}</Text>
              <Text style={styles.testResult}>{result.name} ({result.type})</Text>
            </View>
          ))}
        </View>
      </View>

      {/* 使用示例 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>使用示例</Text>
        <View style={styles.codeBlock}>
          <Text style={styles.codeText}>
{`// 基础使用
import { getTransportIcon } from '../../constants/TransportationIcons';

const icon = getTransportIcon('subway'); // 🚇

// 获取完整信息
import { getTransportInfo } from '../../constants/TransportationIcons';

const info = getTransportInfo('subway', 'zh');
// { icon: '🚇', color: '#2196F3', name: '地铁', type: 'subway' }

// 在组件中使用
<Text style={{ color: info.color }}>
  {info.icon} {info.name}
</Text>`}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

// ===== 样式定义 =====

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },

  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
    color: '#333',
  },

  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },

  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },

  iconItem: {
    alignItems: 'center',
    width: 80,
    marginBottom: 16,
  },

  iconCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },

  iconText: {
    fontSize: 24,
  },

  iconLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
  },

  iconType: {
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
  },

  testItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },

  testLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },

  testResult: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },

  colorTest: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },

  colorSample: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 8,
  },

  searchResult: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },

  codeBlock: {
    backgroundColor: '#2d3748',
    borderRadius: 8,
    padding: 12,
  },

  codeText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: '#e2e8f0',
    lineHeight: 18,
  },
});

export default TransportIconDemo;
