/**
 * ⏰ 时间格式验证器 - Ultra Think系统性修复
 * 验证和统一所有时间显示格式，确保符合 "09:00-11:15" 标准
 */

import { TimeFormatter } from './TimeFormatter';

export interface TimeFormatTestCase {
  input: {
    startTime: any;
    endTime?: any;
  };
  expected: string;
  description: string;
}

export interface TimeFormatValidationResult {
  isValid: boolean;
  actualFormat: string;
  expectedFormat: string;
  issues: string[];
  suggestions: string[];
}

export class TimeFormatValidator {
  
  /**
   * 🎯 标准时间格式规范
   */
  private static readonly STANDARD_FORMAT_REGEX = /^\d{2}:\d{2}(-\d{2}:\d{2})?$/;
  private static readonly EXPECTED_FORMAT = "HH:MM-HH:MM";

  /**
   * ✅ 验证时间格式是否符合标准
   */
  static validateTimeFormat(timeString: string): TimeFormatValidationResult {
    const issues: string[] = [];
    const suggestions: string[] = [];
    
    // 1. 检查基本格式
    if (!timeString || timeString === '时间待定') {
      issues.push('时间显示为"时间待定"，应显示具体时间');
      suggestions.push('确保时间数据有效且格式正确');
      
      return {
        isValid: false,
        actualFormat: timeString,
        expectedFormat: this.EXPECTED_FORMAT,
        issues,
        suggestions
      };
    }

    // 2. 检查是否符合标准格式
    const isStandardFormat = this.STANDARD_FORMAT_REGEX.test(timeString);
    
    if (!isStandardFormat) {
      issues.push(`时间格式不符合标准: "${timeString}"`);
      suggestions.push(`应使用格式: "${this.EXPECTED_FORMAT}"`);
    }

    // 3. 检查时间范围格式
    if (timeString.includes('-')) {
      const parts = timeString.split('-');
      if (parts.length !== 2) {
        issues.push('时间范围格式错误，应包含开始和结束时间');
        suggestions.push('使用格式: "09:00-11:15"');
      } else {
        // 验证每个时间部分
        parts.forEach((part, index) => {
          if (!/^\d{2}:\d{2}$/.test(part.trim())) {
            issues.push(`时间部分${index + 1}格式错误: "${part}"`);
            suggestions.push('时间应为两位数小时:两位数分钟格式');
          }
        });
      }
    } else {
      // 单个时间格式检查
      if (!/^\d{2}:\d{2}$/.test(timeString)) {
        issues.push(`单个时间格式错误: "${timeString}"`);
        suggestions.push('时间应为两位数小时:两位数分钟格式，如"09:00"');
      }
    }

    return {
      isValid: issues.length === 0,
      actualFormat: timeString,
      expectedFormat: this.EXPECTED_FORMAT,
      issues,
      suggestions
    };
  }

  /**
   * 🧪 测试时间格式化功能
   */
  static runTimeFormatTests(): {
    passed: number;
    failed: number;
    results: Array<{
      testCase: TimeFormatTestCase;
      result: string;
      passed: boolean;
      validation: TimeFormatValidationResult;
    }>;
  } {
    const testCases: TimeFormatTestCase[] = [
      {
        input: { startTime: '09:00', endTime: '11:15' },
        expected: '09:00-11:15',
        description: '标准时间范围格式'
      },
      {
        input: { startTime: '9:00', endTime: '11:15' },
        expected: '09:00-11:15',
        description: '单位数小时应补零'
      },
      {
        input: { startTime: '14:30' },
        expected: '14:30',
        description: '单个时间格式'
      },
      {
        input: { startTime: new Date('2024-01-01T09:00:00'), endTime: new Date('2024-01-01T11:15:00') },
        expected: '09:00-11:15',
        description: 'Date对象输入'
      },
      {
        input: { startTime: '09:00:00', endTime: '11:15:00' },
        expected: '09:00-11:15',
        description: '带秒的时间格式（应忽略秒）'
      },
      {
        input: { startTime: null, endTime: null },
        expected: '时间待定',
        description: '空时间输入'
      },
      {
        input: { startTime: 'invalid', endTime: 'invalid' },
        expected: '时间待定',
        description: '无效时间输入'
      }
    ];

    const results = testCases.map(testCase => {
      const result = TimeFormatter.formatActivityTimeRange(
        testCase.input.startTime,
        testCase.input.endTime
      );
      
      const passed = result === testCase.expected;
      const validation = this.validateTimeFormat(result);

      return {
        testCase,
        result,
        passed,
        validation
      };
    });

    const passed = results.filter(r => r.passed).length;
    const failed = results.length - passed;

    return { passed, failed, results };
  }

  /**
   * 🔧 修复时间格式
   */
  static fixTimeFormat(timeString: string): string {
    if (!timeString || timeString === '时间待定') {
      return '时间待定';
    }

    try {
      // 尝试解析和重新格式化
      if (timeString.includes('-')) {
        // 时间范围
        const parts = timeString.split('-');
        if (parts.length === 2) {
          const startFixed = this.fixSingleTimeFormat(parts[0].trim());
          const endFixed = this.fixSingleTimeFormat(parts[1].trim());
          
          if (startFixed !== '时间待定' && endFixed !== '时间待定') {
            return `${startFixed}-${endFixed}`;
          }
        }
      } else {
        // 单个时间
        return this.fixSingleTimeFormat(timeString);
      }
    } catch (error) {
      console.warn('时间格式修复失败:', error);
    }

    return '时间待定';
  }

  /**
   * 🔧 修复单个时间格式
   */
  private static fixSingleTimeFormat(timeString: string): string {
    if (!timeString) return '时间待定';

    try {
      // 匹配各种时间格式
      const patterns = [
        /^(\d{1,2}):(\d{2})(?::\d{2})?$/, // HH:MM 或 HH:MM:SS
        /^(\d{1,2})\.(\d{2})$/, // HH.MM
        /^(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)$/i // 12小时制
      ];

      for (const pattern of patterns) {
        const match = timeString.match(pattern);
        if (match) {
          let hours = parseInt(match[1], 10);
          const minutes = parseInt(match[2], 10);
          
          // 处理AM/PM
          if (match[3]) {
            const period = match[3].toLowerCase();
            if (period === 'pm' && hours !== 12) {
              hours += 12;
            } else if (period === 'am' && hours === 12) {
              hours = 0;
            }
          }

          // 验证时间有效性
          if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          }
        }
      }

      // 尝试解析为Date对象
      const date = new Date(timeString);
      if (!isNaN(date.getTime())) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
      }
    } catch (error) {
      console.warn('单个时间格式修复失败:', error);
    }

    return '时间待定';
  }

  /**
   * 📊 生成时间格式验证报告
   */
  static generateValidationReport(): string {
    const testResults = this.runTimeFormatTests();
    const lines: string[] = [];

    lines.push('⏰ 时间格式验证报告');
    lines.push('='.repeat(40));
    lines.push(`测试用例: ${testResults.passed + testResults.failed}`);
    lines.push(`通过: ${testResults.passed}`);
    lines.push(`失败: ${testResults.failed}`);
    lines.push(`成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    lines.push('');

    if (testResults.failed > 0) {
      lines.push('❌ 失败的测试用例:');
      testResults.results
        .filter(r => !r.passed)
        .forEach(r => {
          lines.push(`  • ${r.testCase.description}`);
          lines.push(`    输入: ${JSON.stringify(r.testCase.input)}`);
          lines.push(`    期望: ${r.testCase.expected}`);
          lines.push(`    实际: ${r.result}`);
          lines.push(`    问题: ${r.validation.issues.join(', ')}`);
          lines.push('');
        });
    }

    lines.push('✅ 格式标准:');
    lines.push('  • 单个时间: "09:00" (两位数小时:两位数分钟)');
    lines.push('  • 时间范围: "09:00-11:15" (开始时间-结束时间)');
    lines.push('  • 无效时间: "时间待定"');

    return lines.join('\n');
  }

  /**
   * 🎯 验证Ultra Think活动的时间格式
   */
  static validateUltraThinkActivityTimes(activities: any[]): {
    validCount: number;
    invalidCount: number;
    issues: Array<{
      activityId: string;
      activityName: string;
      timeIssues: string[];
    }>;
  } {
    let validCount = 0;
    let invalidCount = 0;
    const issues: Array<{
      activityId: string;
      activityName: string;
      timeIssues: string[];
    }> = [];

    activities.forEach(activity => {
      const timeString = TimeFormatter.formatActivityTimeRange(
        activity.startTime || activity.timing?.startTime,
        activity.endTime || activity.timing?.endTime
      );

      const validation = this.validateTimeFormat(timeString);
      
      if (validation.isValid) {
        validCount++;
      } else {
        invalidCount++;
        issues.push({
          activityId: activity.id,
          activityName: activity.name || activity.title,
          timeIssues: validation.issues
        });
      }
    });

    return { validCount, invalidCount, issues };
  }
}
