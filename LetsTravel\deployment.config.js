// LetsTravel 部署配置
module.exports = {
  // 项目基本信息
  project: {
    name: 'Trekmate',
    version: '1.0.0',
    description: '智能旅行助手应用',
    author: 'Trekmate Team'
  },

  // Expo/EAS 配置
  expo: {
    projectId: '9b03ac62-03f6-4a91-af81-750047aa5bf2',
    buildProfiles: {
      development: {
        developmentClient: true,
        distribution: 'internal'
      },
      preview: {
        distribution: 'internal',
        android: { buildType: 'apk' }
      },
      production: {
        autoIncrement: true
      }
    },
    platforms: ['android', 'ios'],
    defaultPlatform: 'android'
  },

  // Web部署配置
  web: {
    vercel: {
      projectPath: './my-vercel-project',
      domain: 'trekmate.vercel.app',
      buildCommand: 'npx expo export:web',
      outputDirectory: 'web-build'
    }
  },

  // 后端服务配置
  backend: {
    scraper: {
      path: '../backend_scraper',
      runtime: 'python',
      dependencies: 'requirements.txt'
    },
    scheduler: {
      path: '../scheduler', 
      runtime: 'python',
      dependencies: 'requirements.txt'
    },
    api: {
      path: '../api',
      runtime: 'node',
      dependencies: 'package.json'
    }
  },

  // 环境配置
  environments: {
    development: {
      apiUrl: 'http://localhost:3000',
      debug: true
    },
    staging: {
      apiUrl: 'https://api-staging.trekmate.app',
      debug: false
    },
    production: {
      apiUrl: 'https://api.trekmate.app',
      debug: false
    }
  },

  // 构建脚本
  scripts: {
    'build:mobile': 'eas build --profile preview --platform android',
    'build:web': 'npx expo export:web',
    'deploy:vercel': 'vercel --prod',
    'deploy:full': 'node deploy.js',
    'test:deployment': 'node test-deployment.js'
  }
}; 