/**
 * 🧪 ActivityAttributeManager 单元测试
 * 
 * 测试活动属性管理器的核心功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import { ActivityAttributeManager } from '../../services/activity/ActivityAttributeManager';
import {
  RealActivityAttributes,
  PriceLevel,
  AttributeType,
  AttributeDisplayConfig
} from '../../types/ActivityAttributes';

describe('ActivityAttributeManager', () => {
  let manager: ActivityAttributeManager;

  beforeEach(() => {
    manager = ActivityAttributeManager.getInstance();
  });

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = ActivityAttributeManager.getInstance();
      const instance2 = ActivityAttributeManager.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('generateRealAttributes', () => {
    it('应该为餐厅生成正确的属性', () => {
      const activity = {
        type: 'restaurant',
        name: '测试餐厅',
        cost: 50
      };

      const attributes = manager.generateRealAttributes(activity, 'template');

      expect(attributes).toBeDefined();
      expect(attributes.operatingHours).toBeDefined();
      expect(attributes.rating).toBeDefined();
      expect(attributes.priceRange).toBeDefined();
      expect(attributes.availability).toBeDefined();
      expect(attributes.bookingRequirement).toBeDefined();
      expect(attributes.seasonality).toBeDefined();
    });

    it('应该为景点生成正确的属性', () => {
      const activity = {
        type: 'attraction',
        name: '测试博物馆',
        cost: 20
      };

      const attributes = manager.generateRealAttributes(activity, 'template');

      expect(attributes.operatingHours.todayHours).toBe('09:00-18:00');
      expect(attributes.priceRange.level).toBe(PriceLevel.BUDGET);
      expect(attributes.bookingRequirement.required).toBe(false);
    });

    it('应该处理免费活动', () => {
      const activity = {
        type: 'attraction',
        name: '免费公园',
        cost: 0
      };

      const attributes = manager.generateRealAttributes(activity, 'template');

      expect(attributes.priceRange.level).toBe(PriceLevel.FREE);
      expect(attributes.priceRange.range.min).toBe(0);
      expect(attributes.priceRange.range.max).toBe(0);
    });

    it('应该处理API数据源', () => {
      const activity = {
        type: 'restaurant',
        name: '测试餐厅',
        rating: 4.5,
        user_ratings_total: 150,
        cost: { amount: 75, currency: 'MYR' }
      };

      const attributes = manager.generateRealAttributes(activity, 'api');

      expect(attributes.rating.overallRating).toBe(4.5);
      expect(attributes.rating.reviewCount).toBe(150);
      expect(attributes.priceRange.range.min).toBe(75);
      expect(attributes.priceRange.range.currency).toBe('MYR');
    });

    it('应该在出错时返回备用属性', () => {
      const invalidActivity = null;

      const attributes = manager.generateRealAttributes(invalidActivity, 'template');

      expect(attributes).toBeDefined();
      expect(attributes.operatingHours.todayHours).toBe('09:00-18:00');
      expect(attributes.rating.overallRating).toBe(4.0);
      expect(attributes.priceRange.level).toBe(PriceLevel.MODERATE);
    });
  });

  describe('formatAttributeDisplay', () => {
    let sampleAttributes: RealActivityAttributes;

    beforeEach(() => {
      sampleAttributes = {
        operatingHours: {
          isCurrentlyOpen: true,
          todayHours: '09:00-18:00',
          weeklyHours: {
            monday: '09:00-18:00',
            tuesday: '09:00-18:00',
            wednesday: '09:00-18:00',
            thursday: '09:00-18:00',
            friday: '09:00-18:00',
            saturday: '09:00-18:00',
            sunday: '09:00-18:00'
          }
        },
        rating: {
          overallRating: 4.3,
          reviewCount: 125,
          source: 'google',
          trend: 'stable'
        },
        priceRange: {
          level: PriceLevel.MODERATE,
          range: { min: 30, max: 50, currency: 'MYR' },
          type: 'per_person',
          description: '门票价格',
          includesExtras: false
        },
        availability: {
          status: 'available',
          description: '当前可参观',
          crowdLevel: 3,
          bestTimeToVisit: ['上午', '下午']
        },
        bookingRequirement: {
          required: false,
          type: 'walk_in'
        },
        seasonality: {
          bestSeasons: ['全年'],
          currentSeasonRating: 4,
          seasonalNotes: '全年适宜',
          weatherDependent: false
        }
      };
    });

    it('应该格式化紧凑模式显示', () => {
      const config: AttributeDisplayConfig = {
        mode: 'compact',
        theme: 'light',
        showAttributes: [AttributeType.OPERATING_HOURS, AttributeType.RATING],
        locale: 'zh-CN'
      };

      const formatted = manager.formatAttributeDisplay(sampleAttributes, config);

      expect(formatted[AttributeType.OPERATING_HOURS]).toContain('营业中');
      expect(formatted[AttributeType.RATING]).toContain('⭐ 4.3');
    });

    it('应该格式化详细模式显示', () => {
      const config: AttributeDisplayConfig = {
        mode: 'detailed',
        theme: 'light',
        showAttributes: [AttributeType.PRICE_RANGE],
        locale: 'zh-CN'
      };

      const formatted = manager.formatAttributeDisplay(sampleAttributes, config);

      expect(formatted[AttributeType.PRICE_RANGE]).toContain('门票价格');
      expect(formatted[AttributeType.PRICE_RANGE]).toContain('MYR30-50');
    });

    it('应该处理免费价格', () => {
      sampleAttributes.priceRange.level = PriceLevel.FREE;
      sampleAttributes.priceRange.range.min = 0;
      sampleAttributes.priceRange.range.max = 0;

      const config: AttributeDisplayConfig = {
        mode: 'compact',
        theme: 'light',
        showAttributes: [AttributeType.PRICE_RANGE],
        locale: 'zh-CN'
      };

      const formatted = manager.formatAttributeDisplay(sampleAttributes, config);

      expect(formatted[AttributeType.PRICE_RANGE]).toBe('免费');
    });

    it('应该处理闭店状态', () => {
      sampleAttributes.operatingHours.isCurrentlyOpen = false;

      const config: AttributeDisplayConfig = {
        mode: 'compact',
        theme: 'light',
        showAttributes: [AttributeType.OPERATING_HOURS],
        locale: 'zh-CN'
      };

      const formatted = manager.formatAttributeDisplay(sampleAttributes, config);

      expect(formatted[AttributeType.OPERATING_HOURS]).toContain('已闭店');
    });
  });

  describe('价格等级判断', () => {
    it('应该正确判断价格等级', () => {
      const testCases = [
        { amount: 0, expected: PriceLevel.FREE },
        { amount: 15, expected: PriceLevel.BUDGET },
        { amount: 40, expected: PriceLevel.MODERATE },
        { amount: 80, expected: PriceLevel.EXPENSIVE },
        { amount: 150, expected: PriceLevel.LUXURY }
      ];

      testCases.forEach(({ amount, expected }) => {
        const activity = { type: 'attraction', cost: amount };
        const attributes = manager.generateRealAttributes(activity, 'template');
        
        expect(attributes.priceRange.level).toBe(expected);
      });
    });
  });

  describe('营业时间判断', () => {
    it('应该正确判断营业状态', () => {
      // 模拟当前时间为上午10点
      const mockDate = new Date();
      mockDate.setHours(10, 0, 0, 0);
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

      const activity = { type: 'attraction', name: '测试景点' };
      const attributes = manager.generateRealAttributes(activity, 'template');

      expect(attributes.operatingHours.isCurrentlyOpen).toBe(true);

      // 恢复原始Date
      jest.restoreAllMocks();
    });

    it('应该正确判断闭店状态', () => {
      // 模拟当前时间为晚上8点
      const mockDate = new Date();
      mockDate.setHours(20, 0, 0, 0);
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

      const activity = { type: 'attraction', name: '测试景点' };
      const attributes = manager.generateRealAttributes(activity, 'template');

      expect(attributes.operatingHours.isCurrentlyOpen).toBe(false);

      // 恢复原始Date
      jest.restoreAllMocks();
    });
  });

  describe('错误处理', () => {
    it('应该处理无效的活动数据', () => {
      const invalidActivities = [
        null,
        undefined,
        {},
        { invalidField: 'test' }
      ];

      invalidActivities.forEach(activity => {
        const attributes = manager.generateRealAttributes(activity, 'template');
        
        expect(attributes).toBeDefined();
        expect(attributes.operatingHours).toBeDefined();
        expect(attributes.rating).toBeDefined();
        expect(attributes.priceRange).toBeDefined();
      });
    });

    it('应该处理无效的数据源', () => {
      const activity = { type: 'attraction', name: '测试' };
      
      const attributes = manager.generateRealAttributes(activity, 'invalid_source' as any);
      
      expect(attributes).toBeDefined();
    });
  });
});
