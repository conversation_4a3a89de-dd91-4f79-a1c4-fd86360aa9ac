/**
 * 🔧 简单预算修复器
 * 直接修复预算计算问题，确保预算准确
 */

export class SimpleBudgetFixer {
  
  /**
   * 🔧 修复Journey预算计算
   */
  static fixJourneyBudget(journey: any, originalBudget: number, currency: string = 'MYR'): any {
    console.log('🔧 开始修复Journey预算计算');
    
    try {
      const fixedJourney = { ...journey };
      
      // 1. 计算真实活动费用
      const realActivityCosts = this.calculateRealActivityCosts(journey.activities || []);
      
      // 2. 确保预算合理
      const reasonableBudget = this.ensureReasonableBudget(realActivityCosts, originalBudget, currency);
      
      // 3. 更新Journey预算
      fixedJourney.budget = reasonableBudget;
      
      // 4. 修复活动费用
      fixedJourney.activities = this.fixActivityCosts(journey.activities || [], reasonableBudget, currency);
      
      console.log(`✅ 预算修复完成: 原始${originalBudget}${currency} -> 修复后${reasonableBudget}${currency}`);
      
      return fixedJourney;
      
    } catch (error) {
      console.error('❌ 预算修复失败:', error);
      return journey;
    }
  }
  
  /**
   * 💰 计算真实活动费用
   */
  private static calculateRealActivityCosts(activities: any[]): number {
    if (!activities || activities.length === 0) {
      return 0;
    }
    
    return activities.reduce((total, activity) => {
      // 只计算有实际费用的活动
      const cost = this.getActivityRealCost(activity);
      return total + cost;
    }, 0);
  }
  
  /**
   * 🎯 获取活动真实费用
   */
  private static getActivityRealCost(activity: any): number {
    // 优先使用已设定的费用
    if (activity.cost && typeof activity.cost === 'number' && activity.cost > 0) {
      return activity.cost;
    }
    
    // 根据活动类型估算合理费用
    const activityType = this.getActivityType(activity);
    const baseCosts = {
      'attraction': 50,      // 景点门票 RM50
      'meal': 80,           // 餐饮 RM80
      'transport': 20,      // 交通 RM20
      'accommodation': 200, // 住宿 RM200
      'shopping': 100,      // 购物 RM100
      'free': 0            // 免费活动
    };
    
    return baseCosts[activityType] || 30; // 默认RM30
  }
  
  /**
   * 🏷️ 获取活动类型
   */
  private static getActivityType(activity: any): string {
    const title = (activity.title || activity.name || '').toLowerCase();
    const description = (activity.description || '').toLowerCase();
    const text = `${title} ${description}`;
    
    // 免费活动
    if (text.includes('免费') || text.includes('free') || text.includes('公园') || text.includes('寺庙')) {
      return 'free';
    }
    
    // 餐饮
    if (text.includes('餐') || text.includes('食') || text.includes('lunch') || text.includes('dinner')) {
      return 'meal';
    }
    
    // 交通
    if (text.includes('地铁') || text.includes('巴士') || text.includes('taxi') || text.includes('transport')) {
      return 'transport';
    }
    
    // 住宿
    if (text.includes('酒店') || text.includes('hotel') || text.includes('住宿')) {
      return 'accommodation';
    }
    
    // 购物
    if (text.includes('购物') || text.includes('shopping') || text.includes('商店')) {
      return 'shopping';
    }
    
    // 默认为景点
    return 'attraction';
  }
  
  /**
   * ✅ 确保预算合理
   */
  private static ensureReasonableBudget(calculatedCost: number, originalBudget: number, currency: string): number {
    // 如果计算费用远超原始预算，使用原始预算
    if (calculatedCost > originalBudget * 3) {
      console.log(`⚠️ 计算费用过高(${calculatedCost}), 使用原始预算(${originalBudget})`);
      return originalBudget;
    }
    
    // 如果计算费用合理，使用计算费用
    if (calculatedCost > 0 && calculatedCost <= originalBudget * 1.5) {
      return Math.round(calculatedCost);
    }
    
    // 否则使用原始预算
    return originalBudget;
  }
  
  /**
   * 🔧 修复活动费用
   */
  private static fixActivityCosts(activities: any[], totalBudget: number, currency: string): any[] {
    if (!activities || activities.length === 0) {
      return activities;
    }
    
    // 计算当前总费用
    const currentTotal = activities.reduce((sum, activity) => sum + this.getActivityRealCost(activity), 0);
    
    // 如果当前总费用合理，不需要调整
    if (currentTotal <= totalBudget * 1.2) {
      return activities.map(activity => ({
        ...activity,
        cost: this.getActivityRealCost(activity)
      }));
    }
    
    // 需要按比例调整费用
    const adjustmentRatio = totalBudget / currentTotal;
    
    return activities.map(activity => {
      const originalCost = this.getActivityRealCost(activity);
      const adjustedCost = Math.round(originalCost * adjustmentRatio);
      
      return {
        ...activity,
        cost: Math.max(0, adjustedCost) // 确保费用不为负数
      };
    });
  }
  
  /**
   * 📊 生成预算报告
   */
  static generateBudgetReport(originalJourney: any, fixedJourney: any, originalBudget: number): string {
    const originalTotal = this.calculateRealActivityCosts(originalJourney.activities || []);
    const fixedTotal = this.calculateRealActivityCosts(fixedJourney.activities || []);
    
    const report = [
      '🔧 预算修复报告',
      '='.repeat(30),
      `💰 原始预算: ${originalBudget} MYR`,
      `📊 原始计算总计: ${originalTotal} MYR`,
      `✅ 修复后总计: ${fixedTotal} MYR`,
      `📈 调整幅度: ${((fixedTotal - originalTotal) / originalTotal * 100).toFixed(1)}%`,
      '',
      '📋 费用分解:',
      ...this.generateCostBreakdown(fixedJourney.activities || []),
      ''
    ];
    
    return report.join('\n');
  }
  
  /**
   * 📋 生成费用分解
   */
  private static generateCostBreakdown(activities: any[]): string[] {
    const breakdown: { [key: string]: number } = {};
    
    activities.forEach(activity => {
      const type = this.getActivityType(activity);
      const cost = this.getActivityRealCost(activity);
      breakdown[type] = (breakdown[type] || 0) + cost;
    });
    
    return Object.entries(breakdown).map(([type, cost]) => 
      `  ${this.getTypeDisplayName(type)}: ${cost} MYR`
    );
  }
  
  /**
   * 🏷️ 获取类型显示名称
   */
  private static getTypeDisplayName(type: string): string {
    const names: { [key: string]: string } = {
      'attraction': '景点',
      'meal': '餐饮',
      'transport': '交通',
      'accommodation': '住宿',
      'shopping': '购物',
      'free': '免费活动'
    };
    return names[type] || type;
  }
}
