/**
 * 🎨 简化版SmartDayCard组件
 * 基于历史工作版本，专注于稳定的活动显示
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { getTransportIcon } from '../../constants/TransportationIcons';

interface SmartDayCardProps {
  dayNumber: number;
  activities: any[];
  totalBudget?: number;
  currency?: string;
  weather?: string;
  date?: string;
  onActivityPress?: (activity: any) => void;
}

export default function SmartDayCard({
  dayNumber,
  activities,
  totalBudget = 0,
  currency = 'RM',
  weather = '🌤️ 晴朗 8°C',
  date,
  onActivityPress
}: SmartDayCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  console.log(`🎨 SmartDayCard Day${dayNumber} 渲染，活动数量:`, activities?.length || 0);

  // 🔧 简化的数据处理 - 直接使用传入的activities
  const processedActivities = activities || [];
  
  // 🔧 简化的辅助方法
  const getActivityName = (activity: any): string => {
    return activity.name || activity.title || activity.activityName || '未命名活动';
  };

  const getTimeRange = (activity: any, index: number): string => {
    // 🔧 安全的时间格式化函数
    const formatTime = (time: any): string => {
      if (!time) return '09:00';

      // 如果是Date对象，转换为时间字符串
      if (time instanceof Date) {
        if (isNaN(time.getTime())) return '09:00'; // 无效Date对象
        const hours = time.getHours().toString().padStart(2, '0');
        const minutes = time.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
      }

      // 如果是字符串
      if (typeof time === 'string') {
        if (!time.includes(':')) return '09:00';
        const [hours, minutes] = time.split(':');
        return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
      }

      return '09:00'; // 默认值
    };

    // 🎯 优先使用活动自带的时间数据
    if (activity.startTime && activity.endTime) {
      return `${formatTime(activity.startTime)} - ${formatTime(activity.endTime)}`;
    }

    // 🕐 检查timing对象中的时间
    if (activity.timing) {
      if (activity.timing.startTime && activity.timing.endTime) {
        return `${formatTime(activity.timing.startTime)} - ${formatTime(activity.timing.endTime)}`;
      }
      if (activity.timing.timeRange) {
        return activity.timing.timeRange;
      }
    }

    // 🍽️ 餐饮活动的特殊时间处理
    if (activity.type === 'meal' || activity.type === 'restaurant') {
      if (activity.time) {
        const mealTime = formatTime(activity.time);
        const duration = activity.duration || 90;
        const endTime = addMinutesToTime(mealTime, duration);
        return `${mealTime} - ${endTime}`;
      }
    }

    // 🚗 交通活动的时间处理
    if (activity.type === 'transport') {
      if (activity.departureTime) {
        const depTime = formatTime(activity.departureTime);
        const duration = activity.duration || 30;
        const arrTime = addMinutesToTime(depTime, duration);
        return `${depTime} - ${arrTime}`;
      }
    }

    // 🎯 智能时间分配 - 根据活动类型和索引生成多样化时间
    const activityType = activity.type || 'attraction';
    const name = getActivityName(activity).toLowerCase();

    let baseHour = 9; // 默认开始时间
    let duration = 90; // 默认持续时间

    // 根据活动类型调整时间
    if (activityType === 'meal' || activityType === 'restaurant') {
      if (name.includes('早餐') || name.includes('breakfast')) {
        baseHour = 8;
        duration = 60;
      } else if (name.includes('午餐') || name.includes('lunch')) {
        baseHour = 12;
        duration = 90;
      } else if (name.includes('晚餐') || name.includes('dinner')) {
        baseHour = 19;
        duration = 120;
      }
    } else if (activityType === 'transport') {
      duration = 30; // 交通时间较短
      baseHour = 9 + (index * 1.5); // 交通活动间隔较短
    } else {
      // 景点活动的多样化时间分配
      const timeSlots = [
        { start: 9, duration: 120 },   // 09:00-11:00
        { start: 10, duration: 90 },   // 10:00-11:30
        { start: 11, duration: 120 },  // 11:00-13:00
        { start: 13, duration: 90 },   // 13:00-14:30
        { start: 14, duration: 120 },  // 14:00-16:00
        { start: 15, duration: 90 },   // 15:00-16:30
        { start: 16, duration: 120 },  // 16:00-18:00
        { start: 17, duration: 90 }    // 17:00-18:30
      ];

      const slot = timeSlots[index % timeSlots.length];
      baseHour = slot.start;
      duration = slot.duration;
    }

    const startHour = Math.min(baseHour, 20); // 最晚不超过20点
    const startTime = `${Math.floor(startHour).toString().padStart(2, '0')}:${((startHour % 1) * 60).toString().padStart(2, '0')}`;
    const endTime = addMinutesToTime(startTime, duration);

    return `${startTime} - ${endTime}`;
  };

  // 🕐 辅助函数：给时间添加分钟
  const addMinutesToTime = (timeStr: string, minutes: number): string => {
    const [hours, mins] = timeStr.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMins = totalMinutes % 60;
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  };

  const getActivityCost = (activity: any): number => {
    // 🎯 优先使用活动自带的费用数据
    if (activity.cost !== undefined && activity.cost !== null) {
      // 如果是对象格式 {amount: number, currency: string}
      if (typeof activity.cost === 'object' && activity.cost.amount !== undefined) {
        return activity.cost.amount;
      }
      // 如果是数字格式
      if (typeof activity.cost === 'number') {
        return activity.cost;
      }
    }

    const type = activity.type || 'attraction';
    const name = getActivityName(activity).toLowerCase();

    // 🚶‍♂️ 交通活动费用处理
    if (type === 'transport' || name.includes('前往') || name.includes('步行') || name.includes('walking')) {
      return 0; // 步行免费
    }

    // 🚇 其他交通方式的合理费用
    if (name.includes('地铁') || name.includes('subway')) return 5;
    if (name.includes('出租车') || name.includes('taxi')) return 15;
    if (name.includes('巴士') || name.includes('bus')) return 3;

    // 🍽️ 餐饮活动费用处理
    if (type === 'restaurant' || type === 'meal' || type === 'food_tour') {
      // 根据餐厅名称估算费用
      if (name.includes('银座') || name.includes('米其林')) return 200;
      if (name.includes('寿司') || name.includes('和牛')) return 150;
      if (name.includes('拉面') || name.includes('快餐')) return 35;
      return 80; // 默认餐饮费用
    }

    // 📍 其他活动类型的费用
    const baseCosts = {
      cultural: 30,
      cultural_experience: 80,
      cultural_site: 25,
      attraction: 25,
      shopping: 50,
      entertainment: 40
    };

    return baseCosts[type] || 30;
  };

  const getBudgetText = (activity: any): string => {
    const cost = getActivityCost(activity);
    return cost === 0 ? '免费' : `${currency}${cost}`;
  };

  const getActivityIcon = (activity: any): string => {
    const name = getActivityName(activity).toLowerCase();
    const type = activity.type || '';

    if (name.includes('酒店') || name.includes('hotel') || type === 'accommodation') {
      return '🏨';
    }
    if (name.includes('餐') || name.includes('食') || name.includes('寿司') || type === 'meal' || type === 'restaurant') {
      return '🍽️';
    }
    if (name.includes('地铁') || name.includes('前往') || name.includes('taxi') || type === 'transport') {
      return getTransportIcon('default');
    }
    if (name.includes('购物') || name.includes('商店') || type === 'shopping') {
      return '🛍️';
    }

    return '🏛️'; // 默认景点图标
  };

  const getTotalDayBudget = (): number => {
    return processedActivities.reduce((sum, activity) => sum + getActivityCost(activity), 0);
  };

  const getTimelineDotColor = (activity: any): string => {
    const type = activity.type || '';
    const name = getActivityName(activity).toLowerCase();

    // 🚶‍♂️ 交通活动使用灰色 - 扩展识别规则
    if (type === 'transport' || type === 'transportation' ||
        name.includes('前往') || name.includes('步行') || name.includes('walking') ||
        name.includes('地铁') || name.includes('subway') || name.includes('metro') ||
        name.includes('出租车') || name.includes('taxi') || name.includes('cab') ||
        name.includes('巴士') || name.includes('bus') || name.includes('公交') ||
        name.includes('电车') || name.includes('train') ||
        name.includes('交通') || name.includes('transport')) {
      return '#999999'; // 灰色
    }

    // 🍽️ 餐饮活动使用橙色 - 扩展识别规则
    if (type === 'restaurant' || type === 'meal' || type === 'food_tour' || type === 'dining' ||
        name.includes('餐') || name.includes('食') || name.includes('寿司') || name.includes('sushi') ||
        name.includes('拉面') || name.includes('ramen') || name.includes('料理') ||
        name.includes('和牛') || name.includes('天妇罗') || name.includes('居酒屋') ||
        name.includes('早餐') || name.includes('午餐') || name.includes('晚餐') ||
        name.includes('breakfast') || name.includes('lunch') || name.includes('dinner') ||
        name.includes('市场') || name.includes('market') && name.includes('食')) {
      return '#FF9500'; // 橙色
    }

    // 🛍️ 购物活动使用紫色
    if (type === 'shopping' || name.includes('购物') || name.includes('商店') ||
        name.includes('shopping') || name.includes('mall')) {
      return '#AF52DE'; // 紫色
    }

    // 🎭 娱乐活动使用绿色
    if (type === 'entertainment' || name.includes('娱乐') || name.includes('表演') ||
        name.includes('show') || name.includes('theater')) {
      return '#34C759'; // 绿色
    }

    // 🏛️ 文化和景点活动使用蓝色（默认）
    return '#007AFF'; // 蓝色
  };

  return (
    <View style={styles.container}>
      {/* 📅 头部：Day 1 + 天气 + 总预算 */}
      <TouchableOpacity style={styles.header} onPress={() => setIsExpanded(!isExpanded)}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.dayTitle}>Day {dayNumber}</Text>
            {date && <Text style={styles.date}>{date}</Text>}
          </View>

          <View style={styles.headerCenter}>
            <Text style={styles.weather}>{weather}</Text>
          </View>

          <View style={styles.headerRight}>
            <Text style={styles.totalBudget}>
              {currency}{getTotalDayBudget()}
            </Text>
            <Text style={styles.expandIcon}>{isExpanded ? '▲' : '▼'}</Text>
          </View>
        </View>
      </TouchableOpacity>

      {/* 🕐 主要活动时间线 */}
      <View style={styles.activitiesContainer}>
        {processedActivities.map((activity, index) => (
          <TouchableOpacity
            key={activity.id || index}
            style={styles.activityItem}
            onPress={() => onActivityPress?.(activity)}
          >
            <View style={styles.timelineLayout}>
              {/* 时间线左侧 */}
              <View style={styles.timelineLeft}>
                <Text style={styles.timeRange}>
                  {getTimeRange(activity, index)}
                </Text>
                <View style={[
                  styles.timelineDot,
                  { backgroundColor: getTimelineDotColor(activity) }
                ]} />
                {index < processedActivities.length - 1 && (
                  <View style={styles.timelineConnector} />
                )}
              </View>

              {/* 活动内容右侧 */}
              <View style={styles.activityContent}>
                <View style={styles.activityHeader}>
                  <Text style={styles.activityName}>
                    {getActivityIcon(activity)} {getActivityName(activity)}
                  </Text>
                  <Text style={styles.activityBudget}>
                    {getBudgetText(activity)}
                  </Text>
                </View>

                {/* 展开内容 */}
                {isExpanded && (
                  <View style={styles.expandedContent}>
                    {/* 🎨 生动的活动描述 */}
                    <Text style={styles.activityDescription}>
                      {activity.description || `探索${getActivityName(activity)}，体验当地文化和历史`}
                    </Text>

                    {/* 🌟 活动亮点 */}
                    {activity.highlights && activity.highlights.length > 0 && (
                      <View style={styles.highlightsSection}>
                        <Text style={styles.sectionTitle}>✨ 活动亮点</Text>
                        {activity.highlights.slice(0, 3).map((highlight, idx) => (
                          <Text key={idx} style={styles.highlightItem}>• {highlight}</Text>
                        ))}
                      </View>
                    )}

                    {/* 💡 实用建议 */}
                    {activity.tips && activity.tips.length > 0 && (
                      <View style={styles.tipsSection}>
                        <Text style={styles.sectionTitle}>💡 实用建议</Text>
                        {activity.tips.slice(0, 2).map((tip, idx) => (
                          <Text key={idx} style={styles.tipItem}>• {tip}</Text>
                        ))}
                      </View>
                    )}

                    {/* 📍 位置信息 */}
                    {activity.location && (
                      <View style={styles.locationSection}>
                        <Text style={styles.locationInfo}>
                          📍 {activity.location.name || activity.location.district || '位置待定'}
                        </Text>
                        {activity.location.district && activity.location.district !== activity.location.name && (
                          <Text style={styles.districtInfo}>
                            🏢 {activity.location.district}
                          </Text>
                        )}
                      </View>
                    )}

                    {/* 🕐 时间和费用信息 */}
                    <View style={styles.practicalInfo}>
                      {activity.duration && (
                        <Text style={styles.detailItem}>
                          ⏱️ 建议时长: {Math.floor(activity.duration / 60)}小时{activity.duration % 60}分钟
                        </Text>
                      )}
                      <Text style={styles.detailItem}>
                        💰 费用: {getBudgetText(activity)}
                      </Text>
                      {activity.metadata?.contentEnhanced && (
                        <Text style={styles.detailItem}>
                          🎨 内容已优化
                        </Text>
                      )}
                    </View>
                  </View>
                )}
              </View>
            </View>
          </TouchableOpacity>
        ))}

        {processedActivities.length === 0 && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>暂无活动安排</Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // 📅 头部样式
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  dayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  weather: {
    fontSize: 14,
    color: '#FF9500',
    fontWeight: '500',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  totalBudget: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginRight: 8,
  },
  expandIcon: {
    fontSize: 12,
    color: '#666',
  },

  // 🕐 时间线布局
  activitiesContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  activityItem: {
    marginTop: 12,
  },
  timelineLayout: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },

  // 🕐 时间线左侧
  timelineLeft: {
    width: 90,
    alignItems: 'center',
    paddingRight: 12,
    position: 'relative',
  },
  timeRange: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 4,
  },
  timelineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007AFF',
    marginBottom: 4,
  },
  timelineConnector: {
    width: 2,
    height: 20,
    backgroundColor: '#E0E0E0',
    position: 'absolute',
    bottom: -16,
    left: '50%',
    marginLeft: -1,
  },

  // 📍 活动内容区域
  activityContent: {
    flex: 1,
    paddingLeft: 8,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  activityName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    flex: 1,
    lineHeight: 22,
  },
  activityBudget: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },

  // 🔽 展开内容样式
  expandedContent: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F5F5F5',
  },
  activityDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 12,
    fontWeight: '400',
  },
  highlightsSection: {
    marginBottom: 10,
  },
  tipsSection: {
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 13,
    color: '#333',
    fontWeight: '600',
    marginBottom: 6,
  },
  highlightItem: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    marginBottom: 3,
  },
  tipItem: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    marginBottom: 3,
  },
  locationSection: {
    marginBottom: 8,
  },
  locationInfo: {
    fontSize: 13,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 2,
  },
  districtInfo: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
  },
  practicalInfo: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 8,
    marginTop: 8,
  },
  detailItem: {
    fontSize: 12,
    color: '#777',
    marginBottom: 4,
    lineHeight: 16,
  },

  // 空状态
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
});