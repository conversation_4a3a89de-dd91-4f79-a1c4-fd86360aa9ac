/**
 * 💰 预算约束验证器 - Ultra Think系统性修复
 * 确保生成的活动严格遵守预算限制
 */

export interface BudgetConstraint {
  totalBudget: number;
  currency: string;
  maxDailySpend?: number;
  categoryLimits?: Record<string, number>;
  emergencyReserve?: number;
}

export interface BudgetValidationResult {
  isValid: boolean;
  totalCost: number;
  overBudget: number;
  utilizationRate: number;
  dailyBreakdown: Array<{
    day: number;
    cost: number;
    activities: number;
    isOverLimit: boolean;
  }>;
  categoryBreakdown: Record<string, {
    allocated: number;
    used: number;
    remaining: number;
    utilizationRate: number;
  }>;
  warnings: string[];
  suggestions: string[];
}

export interface ActivityWithCost {
  id: string;
  name: string;
  type: string;
  cost: {
    amount: number;
    currency: string;
  };
  timing: {
    day: number;
  };
}

export class BudgetConstraintValidator {
  
  /**
   * 💰 验证活动预算约束
   */
  static validateBudgetConstraints(
    activities: ActivityWithCost[],
    constraints: BudgetConstraint
  ): BudgetValidationResult {
    console.log(`🔍 开始预算约束验证: 目标预算=${constraints.totalBudget} ${constraints.currency}`);
    
    // 1. 计算总费用
    const totalCost = activities.reduce((sum, activity) => sum + activity.cost.amount, 0);
    const overBudget = Math.max(0, totalCost - constraints.totalBudget);
    const utilizationRate = totalCost / constraints.totalBudget;
    
    // 2. 按天分解预算
    const dailyBreakdown = this.calculateDailyBreakdown(activities, constraints);
    
    // 3. 按类别分解预算
    const categoryBreakdown = this.calculateCategoryBreakdown(activities, constraints);
    
    // 4. 生成警告和建议
    const warnings: string[] = [];
    const suggestions: string[] = [];
    
    this.generateWarningsAndSuggestions(
      totalCost,
      constraints,
      dailyBreakdown,
      categoryBreakdown,
      warnings,
      suggestions
    );
    
    const result: BudgetValidationResult = {
      isValid: overBudget === 0,
      totalCost,
      overBudget,
      utilizationRate,
      dailyBreakdown,
      categoryBreakdown,
      warnings,
      suggestions
    };
    
    console.log(`✅ 预算验证完成: ${result.isValid ? '通过' : '失败'}, 利用率=${(utilizationRate * 100).toFixed(1)}%`);
    
    return result;
  }

  /**
   * 📅 计算每日预算分解
   */
  private static calculateDailyBreakdown(
    activities: ActivityWithCost[],
    constraints: BudgetConstraint
  ) {
    const dailyMap = new Map<number, { cost: number; activities: number }>();
    
    // 按天统计
    activities.forEach(activity => {
      const day = activity.timing.day;
      const current = dailyMap.get(day) || { cost: 0, activities: 0 };
      current.cost += activity.cost.amount;
      current.activities += 1;
      dailyMap.set(day, current);
    });
    
    // 转换为数组格式
    const dailyBreakdown = Array.from(dailyMap.entries()).map(([day, data]) => ({
      day,
      cost: data.cost,
      activities: data.activities,
      isOverLimit: constraints.maxDailySpend ? data.cost > constraints.maxDailySpend : false
    }));
    
    return dailyBreakdown.sort((a, b) => a.day - b.day);
  }

  /**
   * 🏷️ 计算类别预算分解
   */
  private static calculateCategoryBreakdown(
    activities: ActivityWithCost[],
    constraints: BudgetConstraint
  ) {
    const categoryBreakdown: Record<string, {
      allocated: number;
      used: number;
      remaining: number;
      utilizationRate: number;
    }> = {};
    
    // 初始化类别
    const categories = ['flight', 'accommodation', 'transport', 'meal', 'attraction', 'shopping', 'entertainment'];
    categories.forEach(category => {
      const allocated = constraints.categoryLimits?.[category] || 0;
      categoryBreakdown[category] = {
        allocated,
        used: 0,
        remaining: allocated,
        utilizationRate: 0
      };
    });
    
    // 统计实际使用
    activities.forEach(activity => {
      const category = this.mapActivityTypeToCategory(activity.type);
      if (categoryBreakdown[category]) {
        categoryBreakdown[category].used += activity.cost.amount;
      }
    });
    
    // 计算剩余和利用率
    Object.keys(categoryBreakdown).forEach(category => {
      const data = categoryBreakdown[category];
      data.remaining = data.allocated - data.used;
      data.utilizationRate = data.allocated > 0 ? data.used / data.allocated : 0;
    });
    
    return categoryBreakdown;
  }

  /**
   * 🎯 活动类型映射到预算类别
   */
  private static mapActivityTypeToCategory(type: string): string {
    const mapping: Record<string, string> = {
      'flight': 'flight',
      'accommodation': 'accommodation',
      'transport': 'transport',
      'meal': 'meal',
      'attraction': 'attraction',
      'shopping': 'shopping',
      'entertainment': 'entertainment',
      'culture': 'attraction',
      'nature': 'attraction',
      'museum': 'attraction'
    };
    
    return mapping[type] || 'entertainment';
  }

  /**
   * ⚠️ 生成警告和建议
   */
  private static generateWarningsAndSuggestions(
    totalCost: number,
    constraints: BudgetConstraint,
    dailyBreakdown: any[],
    categoryBreakdown: any,
    warnings: string[],
    suggestions: string[]
  ) {
    // 总预算警告
    if (totalCost > constraints.totalBudget) {
      const overAmount = totalCost - constraints.totalBudget;
      warnings.push(`总预算超支 ${overAmount.toFixed(0)} ${constraints.currency}`);
      suggestions.push('考虑削减非必要活动或选择更经济的选项');
    } else if (totalCost < constraints.totalBudget * 0.7) {
      suggestions.push('预算充裕，可以考虑增加更多活动或升级体验');
    }
    
    // 每日预算警告
    const overLimitDays = dailyBreakdown.filter(day => day.isOverLimit);
    if (overLimitDays.length > 0) {
      warnings.push(`${overLimitDays.length}天超出每日预算限制`);
      suggestions.push('重新分配活动，平衡每日支出');
    }
    
    // 类别预算警告
    Object.entries(categoryBreakdown).forEach(([category, data]: [string, any]) => {
      if (data.utilizationRate > 1.1) {
        warnings.push(`${category}类别超预算 ${((data.utilizationRate - 1) * 100).toFixed(0)}%`);
        suggestions.push(`减少${category}相关支出`);
      } else if (data.utilizationRate > 0.9) {
        warnings.push(`${category}类别接近预算上限`);
      }
    });
    
    // 预算分配建议
    const highUtilizationCategories = Object.entries(categoryBreakdown)
      .filter(([_, data]: [string, any]) => data.utilizationRate > 0.8)
      .map(([category]) => category);
    
    if (highUtilizationCategories.length > 0) {
      suggestions.push(`重点关注高支出类别: ${highUtilizationCategories.join(', ')}`);
    }
  }

  /**
   * 🔧 自动修复预算超支
   */
  static autoFixBudgetOverrun<T extends ActivityWithCost>(
    activities: T[],
    constraints: BudgetConstraint
  ): T[] {
    const validation = this.validateBudgetConstraints(activities, constraints);
    
    if (validation.isValid) {
      console.log('✅ 预算验证通过，无需修复');
      return activities;
    }
    
    console.log(`🔧 开始自动修复预算超支: ${validation.overBudget.toFixed(0)} ${constraints.currency}`);
    
    // 按优先级排序（保护重要活动）
    const sortedActivities = [...activities].sort((a, b) => {
      const priorityA = this.getActivityPriority(a);
      const priorityB = this.getActivityPriority(b);
      return priorityB - priorityA;
    });
    
    let currentTotal = validation.totalCost;
    const targetBudget = constraints.totalBudget;
    const result: T[] = [];
    
    // 逐个添加活动，直到预算用完
    for (const activity of sortedActivities) {
      if (currentTotal - activity.cost.amount >= targetBudget) {
        // 如果移除这个活动后仍然超预算，则移除
        currentTotal -= activity.cost.amount;
        console.log(`🗑️ 移除活动: ${activity.name} (节省 ${activity.cost.amount} ${constraints.currency})`);
      } else {
        result.push(activity);
      }
      
      if (currentTotal <= targetBudget) {
        // 预算达标，添加剩余活动
        const remainingActivities = sortedActivities.slice(sortedActivities.indexOf(activity) + 1);
        for (const remaining of remainingActivities) {
          if (currentTotal + remaining.cost.amount <= targetBudget) {
            result.push(remaining);
            currentTotal += remaining.cost.amount;
          }
        }
        break;
      }
    }
    
    const finalTotal = result.reduce((sum, activity) => sum + activity.cost.amount, 0);
    console.log(`✅ 预算修复完成: 最终费用=${finalTotal} ${constraints.currency}, 活动数量=${result.length}`);
    
    return result;
  }

  /**
   * 📊 获取活动优先级
   */
  private static getActivityPriority(activity: ActivityWithCost): number {
    const typeWeights: Record<string, number> = {
      'flight': 100,
      'accommodation': 90,
      'transport': 70,
      'meal': 60,
      'attraction': 50,
      'shopping': 30,
      'entertainment': 40
    };
    
    return typeWeights[activity.type] || 40;
  }

  /**
   * 📈 生成预算报告
   */
  static generateBudgetReport(validation: BudgetValidationResult): string {
    const lines: string[] = [];
    
    lines.push('💰 预算验证报告');
    lines.push('='.repeat(30));
    lines.push(`总预算: ${validation.totalCost.toFixed(0)} (利用率: ${(validation.utilizationRate * 100).toFixed(1)}%)`);
    lines.push(`状态: ${validation.isValid ? '✅ 通过' : '❌ 超支'}`);
    
    if (validation.overBudget > 0) {
      lines.push(`超支金额: ${validation.overBudget.toFixed(0)}`);
    }
    
    if (validation.warnings.length > 0) {
      lines.push('\n⚠️ 警告:');
      validation.warnings.forEach(warning => lines.push(`  • ${warning}`));
    }
    
    if (validation.suggestions.length > 0) {
      lines.push('\n💡 建议:');
      validation.suggestions.forEach(suggestion => lines.push(`  • ${suggestion}`));
    }
    
    return lines.join('\n');
  }
}
