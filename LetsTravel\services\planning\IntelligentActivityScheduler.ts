/**
 * 🧠 智能活动调度器
 * 专注于活动安排的逻辑性和质量优化
 * 解决时间流程、地理位置、活动平衡、体力消耗等问题
 */

import { Activity, SchedulingConstraints } from './ActivityModels';
import { GeographicCalculationService } from '../algorithms/GeographicCalculationService';
import { safeToLowerCase, safeParseTime } from '../../utils/stringUtils';

export interface ActivityScheduleResult {
  scheduledActivities: Activity[];
  totalTravelTime: number;
  energyDistribution: number;
  qualityScore: number;
  warnings: string[];
}

export interface TimeSlotDefinition {
  name: 'morning' | 'afternoon' | 'evening' | 'night';
  startTime: string;
  endTime: string;
  energyLevel: number; // 0-1，该时段用户的精力水平
  suitableActivityTypes: string[];
}

export class IntelligentActivityScheduler {
  private geographicService: GeographicCalculationService;
  
  // 时间段定义
  private timeSlots: TimeSlotDefinition[] = [
    {
      name: 'morning',
      startTime: '09:00',
      endTime: '12:00',
      energyLevel: 0.9,
      suitableActivityTypes: ['cultural', 'nature', 'sightseeing']
    },
    {
      name: 'afternoon',
      startTime: '13:00',
      endTime: '17:00',
      energyLevel: 0.7,
      suitableActivityTypes: ['shopping', 'food', 'entertainment', 'cultural']
    },
    {
      name: 'evening',
      startTime: '18:00',
      endTime: '21:00',
      energyLevel: 0.5,
      suitableActivityTypes: ['food', 'entertainment', 'shopping']
    },
    {
      name: 'night',
      startTime: '22:00',
      endTime: '08:00',
      energyLevel: 0.2,
      suitableActivityTypes: ['accommodation', 'rest']
    }
  ];

  constructor() {
    this.geographicService = GeographicCalculationService.getInstance();
  }

  /**
   * 🎯 智能安排单日活动
   */
  async scheduleActivitiesForDay(
    activities: Activity[],
    dayNumber: number,
    constraints: SchedulingConstraints,
    userPreferences?: any
  ): Promise<ActivityScheduleResult> {
    console.log(`🎯 开始智能安排第${dayNumber}天活动:`, {
      activitiesCount: activities.length,
      constraints
    });

    // Step 1: 预处理活动数据
    const enhancedActivities = this.enhanceActivityData(activities);
    
    // Step 2: 按优先级和类型分组
    const groupedActivities = this.groupActivitiesByPriorityAndType(enhancedActivities);
    
    // Step 3: 地理位置优化排序
    const geographicallyOptimized = await this.optimizeGeographicalOrder(groupedActivities);
    
    // Step 4: 时间逻辑安排
    const timeOptimized = this.optimizeTimeLogic(geographicallyOptimized, constraints);
    
    // Step 5: 体力消耗平衡
    const energyBalanced = this.balanceEnergyDistribution(timeOptimized, constraints);
    
    // Step 6: 活动类型平衡检查
    const typeBalanced = this.ensureActivityTypeBalance(energyBalanced);
    
    // Step 7: 最终验证和调整
    const finalSchedule = this.validateAndAdjustSchedule(typeBalanced, constraints);
    
    // Step 8: 计算质量指标
    const qualityMetrics = await this.calculateQualityMetrics(finalSchedule);

    console.log(`✅ 第${dayNumber}天活动安排完成:`, {
      scheduledCount: finalSchedule.length,
      qualityScore: qualityMetrics.qualityScore,
      totalTravelTime: qualityMetrics.totalTravelTime
    });

    return {
      scheduledActivities: finalSchedule,
      totalTravelTime: qualityMetrics.totalTravelTime,
      energyDistribution: qualityMetrics.energyDistribution,
      qualityScore: qualityMetrics.qualityScore,
      warnings: qualityMetrics.warnings
    };
  }

  /**
   * 🔧 增强活动数据
   */
  private enhanceActivityData(activities: Activity[]): Activity[] {
    return activities.map(activity => {
      // 如果缺少关键字段，根据类别推断
      if (!activity.timePreference) {
        activity.timePreference = this.inferTimePreference(activity);
      }
      
      if (!activity.energyLevel) {
        activity.energyLevel = this.inferEnergyLevel(activity);
      }
      
      if (!activity.activityType) {
        activity.activityType = this.inferActivityType(activity);
      }
      
      if (activity.flexibility === undefined) {
        activity.flexibility = this.inferFlexibility(activity);
      }

      return activity;
    });
  }

  /**
   * 🎯 推断活动的时间偏好
   */
  private inferTimePreference(activity: Activity): 'morning' | 'afternoon' | 'evening' | 'night' | 'flexible' {
    // 🔧 修复：使用安全字符串工具，避免toLowerCase错误
    const category = safeToLowerCase(activity.category);
    const name = safeToLowerCase(activity.name || activity.title);
    
    // 基于活动类型的时间偏好
    if (category.includes('cultural') || category.includes('temple') || category.includes('museum')) {
      return 'morning'; // 文化活动适合上午
    }
    
    if (category.includes('food') || name.includes('dinner') || name.includes('晚餐')) {
      return 'evening'; // 用餐活动
    }
    
    if (category.includes('shopping') || category.includes('market')) {
      return 'afternoon'; // 购物活动适合下午
    }
    
    if (category.includes('accommodation') || category.includes('hotel')) {
      return 'night'; // 住宿活动
    }
    
    if (category.includes('nature') || name.includes('park') || name.includes('garden')) {
      return 'morning'; // 自然活动适合上午
    }
    
    return 'flexible'; // 默认灵活
  }

  /**
   * ⚡ 推断活动的体力消耗等级
   */
  private inferEnergyLevel(activity: Activity): 'low' | 'medium' | 'high' {
    // 🔧 修复：使用安全字符串工具，避免toLowerCase错误
    const category = safeToLowerCase(activity.category);
    const name = safeToLowerCase(activity.name || activity.title);
    const duration = this.getActivityDuration(activity);
    
    // 基于活动类型推断
    if (category.includes('accommodation') || category.includes('transport')) {
      return 'low';
    }
    
    if (category.includes('nature') || name.includes('hiking') || name.includes('walking')) {
      return 'high';
    }
    
    if (category.includes('shopping') || category.includes('food')) {
      return 'medium';
    }
    
    if (category.includes('cultural') || category.includes('museum')) {
      return duration > 180 ? 'medium' : 'low';
    }
    
    // 基于持续时间推断
    if (duration > 240) return 'high';
    if (duration > 120) return 'medium';
    return 'low';
  }

  /**
   * 🏷️ 推断活动类型
   */
  private inferActivityType(activity: Activity): 'cultural' | 'food' | 'shopping' | 'nature' | 'entertainment' | 'transport' | 'accommodation' {
    // 🔧 修复：使用安全字符串工具，避免toLowerCase错误
    const category = safeToLowerCase(activity.category);
    
    if (category.includes('cultural') || category.includes('temple') || category.includes('museum')) {
      return 'cultural';
    }
    if (category.includes('food') || category.includes('restaurant')) {
      return 'food';
    }
    if (category.includes('shopping') || category.includes('market')) {
      return 'shopping';
    }
    if (category.includes('nature') || category.includes('park')) {
      return 'nature';
    }
    if (category.includes('entertainment')) {
      return 'entertainment';
    }
    if (category.includes('transport')) {
      return 'transport';
    }
    if (category.includes('accommodation') || category.includes('hotel')) {
      return 'accommodation';
    }
    
    return 'cultural'; // 默认
  }

  /**
   * 🔄 推断活动的时间灵活性
   */
  private inferFlexibility(activity: Activity): number {
    // 🔧 修复：使用安全字符串工具，避免toLowerCase错误
    const category = safeToLowerCase(activity.category);
    
    // 关键活动灵活性低
    if (category.includes('flight') || category.includes('transport')) {
      return 0.1;
    }
    
    if (category.includes('accommodation')) {
      return 0.3;
    }
    
    // 用餐活动有一定灵活性
    if (category.includes('food')) {
      return 0.6;
    }
    
    // 观光活动灵活性较高
    if (category.includes('cultural') || category.includes('shopping')) {
      return 0.8;
    }
    
    return 0.5; // 默认中等灵活性
  }

  /**
   * 📊 按优先级和类型分组活动
   */
  private groupActivitiesByPriorityAndType(activities: Activity[]): {
    critical: Activity[];
    high: Activity[];
    medium: Activity[];
    low: Activity[];
  } {
    const grouped = {
      critical: [] as Activity[],
      high: [] as Activity[],
      medium: [] as Activity[],
      low: [] as Activity[]
    };

    activities.forEach(activity => {
      // 关键活动：航班、交通、住宿
      if (['flight', 'transport', 'accommodation'].includes(activity.category)) {
        grouped.critical.push(activity);
      } else if (activity.priority === 'high') {
        grouped.high.push(activity);
      } else if (activity.priority === 'medium') {
        grouped.medium.push(activity);
      } else {
        grouped.low.push(activity);
      }
    });

    return grouped;
  }

  /**
   * 🗺️ 地理位置优化排序
   */
  private async optimizeGeographicalOrder(groupedActivities: any): Promise<Activity[]> {
    const allActivities: Activity[] = [
      ...groupedActivities.critical,
      ...groupedActivities.high,
      ...groupedActivities.medium,
      ...groupedActivities.low
    ];

    // 提取有地理位置的活动
    const activitiesWithLocation = allActivities.filter(activity =>
      activity.location && activity.location.coordinates
    );

    if (activitiesWithLocation.length < 2) {
      console.log('📍 活动数量不足，跳过地理优化');
      return allActivities;
    }

    try {
      // 使用地理计算服务优化路线
      const locations = activitiesWithLocation.map(activity => ({
        name: activity.name,
        coordinates: activity.location!.coordinates!,
        address: activity.location!.address || activity.name
      }));

      const optimizationResult = this.geographicService.optimizeVisitOrder(locations);

      if (optimizationResult && optimizationResult.optimizedOrder) {
        console.log(`🗺️ 地理优化完成，效率提升: ${(optimizationResult.efficiency * 100).toFixed(1)}%`);

        // 按优化后的顺序重新排列活动
        const optimizedActivities: Activity[] = [];

        // 先添加关键活动（保持原有顺序）
        optimizedActivities.push(...groupedActivities.critical);

        // 按地理优化顺序添加其他活动
        optimizationResult.optimizedOrder.forEach(index => {
          const activity = activitiesWithLocation[index];
          // 🔧 修复：检查activity是否存在
          if (activity && !groupedActivities.critical.includes(activity)) {
            optimizedActivities.push(activity);
          }
        });

        // 添加没有地理位置的活动
        const activitiesWithoutLocation = allActivities.filter(activity =>
          !activity.location || !activity.location.coordinates
        );
        optimizedActivities.push(...activitiesWithoutLocation);

        return optimizedActivities;
      }
    } catch (error) {
      console.warn('⚠️ 地理优化失败，使用原始顺序:', error);
    }

    return allActivities;
  }

  /**
   * ⏰ 时间逻辑优化
   */
  private optimizeTimeLogic(activities: Activity[], constraints: SchedulingConstraints): Activity[] {
    console.log('⏰ 开始时间逻辑优化');

    const scheduledActivities: Activity[] = [];
    let currentTime = this.parseTime(constraints.preferredStartTime);

    // 按时间偏好和优先级排序
    const sortedActivities = this.sortActivitiesByTimeLogic(activities);

    for (const activity of sortedActivities) {
      // 计算活动的最佳开始时间
      const optimalStartTime = this.calculateOptimalStartTime(
        activity,
        currentTime,
        scheduledActivities
      );

      // 🔧 修复：统一duration获取逻辑
      const duration = this.getActivityDuration(activity);
      const scheduledActivity = {
        ...activity,
        duration: duration,
        startTime: this.formatTime(optimalStartTime),
        endTime: this.formatTime(optimalStartTime + duration),
        orderIndex: scheduledActivities.length
      };

      scheduledActivities.push(scheduledActivity);

      // 更新当前时间（包括活动时间和可能的交通时间）
      currentTime = optimalStartTime + duration + this.estimateTransitionTime(activity);

      // 🔧 修复活动名称显示问题 - 使用安全的名称获取
      const activityName = activity.name || activity.title || activity.activityName || '未命名活动';
      console.log(`⏰ 安排活动: ${activityName} (${scheduledActivity.startTime}-${scheduledActivity.endTime})`);
    }

    return scheduledActivities;
  }

  /**
   * 📅 按时间逻辑排序活动
   */
  private sortActivitiesByTimeLogic(activities: Activity[]): Activity[] {
    return activities.sort((a, b) => {
      // 1. 优先级排序
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 1;
      const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 1;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // 2. 时间偏好排序
      const timeOrder = { morning: 1, afternoon: 2, evening: 3, night: 4, flexible: 5 };
      const aTimeOrder = timeOrder[a.timePreference as keyof typeof timeOrder] || 5;
      const bTimeOrder = timeOrder[b.timePreference as keyof typeof timeOrder] || 5;

      if (aTimeOrder !== bTimeOrder) {
        return aTimeOrder - bTimeOrder;
      }

      // 3. 体力消耗排序（高体力活动安排在前面）
      const energyOrder = { high: 1, medium: 2, low: 3 };
      const aEnergyOrder = energyOrder[a.energyLevel as keyof typeof energyOrder] || 2;
      const bEnergyOrder = energyOrder[b.energyLevel as keyof typeof energyOrder] || 2;

      return aEnergyOrder - bEnergyOrder;
    });
  }

  /**
   * 🕐 计算活动的最佳开始时间
   */
  private calculateOptimalStartTime(
    activity: Activity,
    currentTime: number,
    scheduledActivities: Activity[]
  ): number {
    // 获取活动的时间偏好
    const timePreference = activity.timePreference || 'flexible';

    // 获取对应时间段的理想开始时间
    const idealTime = this.getIdealTimeForPreference(timePreference);

    // 考虑营业时间
    const operatingHours = activity.operatingHours;
    if (operatingHours) {
      const openTime = this.parseTime(operatingHours.open);
      const closeTime = this.parseTime(operatingHours.close);

      // 确保在营业时间内
      if (idealTime < openTime) {
        return Math.max(currentTime, openTime);
      }
      if (idealTime + this.getActivityDuration(activity) > closeTime) {
        return Math.max(currentTime, openTime);
      }
    }

    // 确保不与已安排的活动冲突
    const conflictFreeTime = this.findConflictFreeTime(
      Math.max(currentTime, idealTime),
      this.getActivityDuration(activity),
      scheduledActivities
    );

    return conflictFreeTime;
  }

  /**
   * 🎯 获取时间偏好对应的理想时间
   */
  private getIdealTimeForPreference(timePreference: string): number {
    const timeMap = {
      morning: this.parseTime('09:00'),
      afternoon: this.parseTime('14:00'),
      evening: this.parseTime('18:00'),
      night: this.parseTime('22:00'),
      flexible: this.parseTime('10:00')
    };

    return timeMap[timePreference as keyof typeof timeMap] || timeMap.flexible;
  }

  /**
   * 🔍 查找无冲突的时间段
   */
  private findConflictFreeTime(
    preferredStartTime: number,
    duration: number,
    scheduledActivities: Activity[]
  ): number {
    let startTime = preferredStartTime;

    // 检查与已安排活动的冲突
    for (const scheduled of scheduledActivities) {
      // 🔧 修复：从多个可能的字段获取时间
      const scheduledStartTime = scheduled.startTime || scheduled.timing?.startTime || '09:00';
      const scheduledEndTime = scheduled.endTime || scheduled.timing?.endTime || '10:00';

      const scheduledStart = this.parseTime(scheduledStartTime);
      const scheduledEnd = this.parseTime(scheduledEndTime);

      // 如果有冲突，调整开始时间
      if (startTime < scheduledEnd && startTime + duration > scheduledStart) {
        startTime = scheduledEnd + 30; // 添加30分钟缓冲时间
      }
    }

    return startTime;
  }

  /**
   * 🕐 解析时间字符串为分钟数
   */
  private parseTime(timeString: string | number | undefined): number {
    // 🔧 修复：处理多种时间格式
    if (typeof timeString === 'number') {
      // 如果是数字，假设是分钟数
      return isNaN(timeString) ? 540 : timeString; // 默认09:00
    }

    if (typeof timeString === 'string') {
      // 使用安全时间解析工具
      const result = safeParseTime(timeString, 540); // 默认09:00
      if (isNaN(result)) {
        console.warn('⚠️ 时间解析失败，使用默认值:', timeString);
        return 540; // 09:00
      }
      return result;
    }

    // 如果是undefined或其他类型，返回默认值
    console.warn('⚠️ 无效的时间类型，使用默认值:', timeString);
    return 540; // 默认09:00
  }

  /**
   * 🕐 格式化分钟数为时间字符串
   */
  private formatTime(minutes: number): string {
    // 🔧 修复：处理NaN和无效值
    if (isNaN(minutes) || minutes < 0) {
      console.warn('⚠️ 无效的时间分钟数:', minutes);
      minutes = 540; // 默认09:00
    }

    // 确保分钟数在合理范围内
    minutes = Math.max(0, Math.min(1440, minutes)); // 0-24小时

    const hours = Math.floor(minutes / 60) % 24;
    const mins = Math.floor(minutes % 60);

    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * 🕐 获取活动持续时间
   */
  private getActivityDuration(activity: Activity): number {
    // 🔧 修复：从多个可能的字段获取duration
    let duration = activity.duration || activity.timing?.duration || 120;

    // 确保duration是有效数字
    if (typeof duration === 'string') {
      duration = parseInt(duration, 10);
    }

    if (isNaN(duration) || duration <= 0) {
      console.warn('⚠️ 无效的活动持续时间，使用默认值:', activity.name, duration);
      duration = 120; // 默认2小时
    }

    // 确保duration在合理范围内（15分钟到8小时）
    duration = Math.max(15, Math.min(480, duration));

    return duration;
  }

  /**
   * 🚶 估算活动间的过渡时间
   */
  private estimateTransitionTime(activity: Activity): number {
    // 🔧 修复：使用安全字符串工具，避免toLowerCase错误
    const category = safeToLowerCase(activity.category);

    if (category.includes('transport')) return 0; // 交通活动本身就是过渡
    if (category.includes('accommodation')) return 15; // 酒店活动后的整理时间
    if (category.includes('food')) return 30; // 用餐后的休息时间

    return 15; // 默认15分钟过渡时间
  }

  /**
   * ⚡ 体力消耗平衡
   */
  private balanceEnergyDistribution(activities: Activity[], constraints: SchedulingConstraints): Activity[] {
    console.log('⚡ 开始体力消耗平衡优化');

    // 🔧 考虑约束条件中的体力限制
    const maxDailyEnergyLevel = constraints.maxDailyActivities || 8;

    const balancedActivities = [...activities];
    let currentEnergyLevel = 1.0; // 开始时精力充沛

    // 🔧 限制处理的活动数量以避免过度疲劳
    const activitiesToProcess = Math.min(balancedActivities.length, maxDailyEnergyLevel);

    for (let i = 0; i < activitiesToProcess; i++) {
      const activity = balancedActivities[i];
      // 🔧 修复：检查activity是否存在
      if (!activity) {
        console.warn('⚠️ balanceEnergyLevels: 活动为空，跳过');
        continue;
      }

      const energyConsumption = this.calculateEnergyConsumption(activity);

      // 如果当前精力不足以支持高强度活动，尝试调整
      if (currentEnergyLevel < 0.3 && activity.energyLevel === 'high') {
        // 寻找可以交换的低强度活动
        const swapIndex = this.findLowEnergyActivityToSwap(balancedActivities, i);
        if (swapIndex !== -1 && balancedActivities[swapIndex]) {
          // 🔧 修复：安全交换活动位置
          const swapActivity = balancedActivities[swapIndex];
          balancedActivities[swapIndex] = activity;
          balancedActivities[i] = swapActivity;

          console.log(`⚡ 体力平衡调整: 交换 ${activity.name} 和 ${swapActivity.name}`);
        }
      }

      // 更新精力水平
      currentEnergyLevel = Math.max(0.1, currentEnergyLevel - energyConsumption);

      // 如果是休息活动（如用餐、住宿），恢复一些精力
      if (['food', 'accommodation'].includes(activity.activityType || '')) {
        currentEnergyLevel = Math.min(1.0, currentEnergyLevel + 0.3);
      }
    }

    return balancedActivities;
  }

  /**
   * 📊 计算活动的体力消耗
   */
  private calculateEnergyConsumption(activity: Activity): number {
    const energyMap = {
      high: 0.4,
      medium: 0.2,
      low: 0.1
    };

    const baseConsumption = energyMap[activity.energyLevel as keyof typeof energyMap] || 0.2;

    // 根据活动持续时间调整
    const durationFactor = Math.min(2.0, this.getActivityDuration(activity) / 120); // 2小时为基准

    return baseConsumption * durationFactor;
  }

  /**
   * 🔄 寻找可交换的低强度活动
   */
  private findLowEnergyActivityToSwap(activities: Activity[], currentIndex: number): number {
    for (let i = currentIndex + 1; i < activities.length; i++) {
      const activity = activities[i];

      // 🔧 修复：检查activity是否存在
      if (!activity) {
        continue;
      }

      // 寻找低强度且时间灵活的活动
      if (activity.energyLevel === 'low' && (activity.flexibility || 0) > 0.5) {
        return i;
      }
    }

    return -1; // 没有找到合适的交换对象
  }

  /**
   * 🎨 确保活动类型平衡
   */
  private ensureActivityTypeBalance(activities: Activity[]): Activity[] {
    console.log('🎨 检查活动类型平衡');

    // 统计各类型活动数量
    const typeCount = this.countActivityTypes(activities);
    const totalActivities = activities.length;

    // 检查是否有类型过于集中
    const warnings: string[] = [];
    Object.entries(typeCount).forEach(([type, count]) => {
      const percentage = count / totalActivities;

      if (percentage > 0.6) {
        warnings.push(`${type}类型活动占比过高 (${(percentage * 100).toFixed(1)}%)`);
      }
    });

    if (warnings.length > 0) {
      console.warn('⚠️ 活动类型不平衡:', warnings);
    }

    // 如果只有1-2种类型，尝试添加多样性
    const uniqueTypes = Object.keys(typeCount).length;
    if (uniqueTypes < 3 && totalActivities > 3) {
      console.log('🎨 活动类型过于单一，建议增加多样性');
    }

    return activities; // 目前返回原活动，未来可以实现自动调整
  }

  /**
   * 📊 统计活动类型数量
   */
  private countActivityTypes(activities: Activity[]): Record<string, number> {
    const typeCount: Record<string, number> = {};

    activities.forEach(activity => {
      const type = activity.activityType || 'other';
      typeCount[type] = (typeCount[type] || 0) + 1;
    });

    return typeCount;
  }

  /**
   * ✅ 验证和调整最终安排
   */
  private validateAndAdjustSchedule(activities: Activity[], constraints: SchedulingConstraints): Activity[] {
    console.log('✅ 验证和调整最终安排');

    const validatedActivities = [...activities];

    // 验证活动数量
    if (validatedActivities.length > constraints.maxDailyActivities) {
      console.warn(`⚠️ 活动数量超限 (${validatedActivities.length}/${constraints.maxDailyActivities})`);
      // 移除优先级最低的活动
      validatedActivities.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 1;
        const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 1;
        return bPriority - aPriority;
      });
      validatedActivities.splice(constraints.maxDailyActivities);
    }

    if (validatedActivities.length < constraints.minDailyActivities) {
      console.warn(`⚠️ 活动数量不足 (${validatedActivities.length}/${constraints.minDailyActivities})`);
    }

    // 验证时间安排的合理性
    this.validateTimeArrangement(validatedActivities);

    // 重新设置orderIndex
    validatedActivities.forEach((activity, index) => {
      activity.orderIndex = index;
    });

    return validatedActivities;
  }

  /**
   * ⏰ 验证时间安排的合理性
   */
  private validateTimeArrangement(activities: Activity[]): void {
    for (let i = 0; i < activities.length - 1; i++) {
      const current = activities[i];
      const next = activities[i + 1];

      // 🔧 修复：检查活动是否存在
      if (!current || !next) {
        continue;
      }

      // 🔧 修复：从多个可能的字段获取时间
      const currentEndTime = current.endTime || current.timing?.endTime;
      const nextStartTime = next.startTime || next.timing?.startTime;

      if (currentEndTime && nextStartTime) {
        const currentEnd = this.parseTime(currentEndTime);
        const nextStart = this.parseTime(nextStartTime);

        // 检查时间冲突
        if (currentEnd > nextStart) {
          console.warn(`⚠️ 时间冲突: ${current.name} (${currentEndTime}) 与 ${next.name} (${nextStartTime})`);
        }

        // 检查过渡时间是否合理
        const gapTime = nextStart - currentEnd;
        if (gapTime < 0) {
          console.warn(`⚠️ 负间隔时间: ${current.name} 到 ${next.name}`);
        } else if (gapTime > 180) { // 超过3小时间隔
          console.warn(`⚠️ 间隔时间过长: ${current.name} 到 ${next.name} (${gapTime}分钟)`);
        }
      }
    }
  }

  /**
   * 📈 计算质量指标
   */
  private async calculateQualityMetrics(activities: Activity[]): Promise<{
    totalTravelTime: number;
    energyDistribution: number;
    qualityScore: number;
    warnings: string[];
  }> {
    const warnings: string[] = [];

    // 计算总旅行时间
    let totalTravelTime = 0;
    for (let i = 0; i < activities.length - 1; i++) {
      const current = activities[i];
      const next = activities[i + 1];

      // 🔧 修复：检查活动是否存在
      if (!current || !next) {
        continue;
      }

      if (current.location?.coordinates && next.location?.coordinates) {
        try {
          const distance = this.geographicService.calculateLocationDistance(
            current.location,
            next.location
          );
          totalTravelTime += Math.round(distance / 50 * 60); // 假设50km/h平均速度
        } catch (error) {
          console.warn('计算旅行时间失败:', error);
        }
      }
    }

    // 计算体力分布均匀度
    const energyDistribution = this.calculateEnergyDistribution(activities);

    // 计算综合质量分数
    const qualityScore = this.calculateOverallQualityScore(activities, totalTravelTime, energyDistribution);

    return {
      totalTravelTime,
      energyDistribution,
      qualityScore,
      warnings
    };
  }

  /**
   * ⚡ 计算体力分布均匀度
   */
  private calculateEnergyDistribution(activities: Activity[]): number {
    const energyLevels = activities.map(activity => {
      const energyMap = { high: 3, medium: 2, low: 1 };
      return energyMap[activity.energyLevel as keyof typeof energyMap] || 2;
    });

    if (energyLevels.length === 0) return 0;

    const mean = energyLevels.reduce((sum, level) => sum + level, 0) / energyLevels.length;
    const variance = energyLevels.reduce((sum, level) => sum + Math.pow(level - mean, 2), 0) / energyLevels.length;

    // 返回均匀度分数 (0-1，1表示最均匀)
    return Math.max(0, 1 - variance / 2);
  }

  /**
   * 🏆 计算综合质量分数
   */
  private calculateOverallQualityScore(
    activities: Activity[],
    totalTravelTime: number,
    energyDistribution: number
  ): number {
    let score = 0;

    // 时间安排合理性 (30%)
    const timeScore = this.calculateTimeArrangementScore(activities);
    score += timeScore * 0.3;

    // 地理位置优化 (25%)
    const geoScore = Math.max(0, 1 - totalTravelTime / 300); // 5小时为满分基准
    score += geoScore * 0.25;

    // 体力分布均匀度 (25%)
    score += energyDistribution * 0.25;

    // 活动类型多样性 (20%)
    const diversityScore = this.calculateActivityDiversityScore(activities);
    score += diversityScore * 0.2;

    return Math.round(score * 100); // 返回0-100分
  }

  /**
   * ⏰ 计算时间安排分数
   */
  private calculateTimeArrangementScore(activities: Activity[]): number {
    let score = 1.0;

    activities.forEach(activity => {
      const timePreference = activity.timePreference || 'flexible';
      const actualTimeSlot = this.getTimeSlotForActivity(activity);

      // 如果活动安排在偏好时间段，加分
      if (timePreference === actualTimeSlot || timePreference === 'flexible') {
        // 完美匹配，不扣分
      } else {
        score -= 0.1; // 每个不匹配的活动扣10%
      }
    });

    return Math.max(0, score);
  }

  /**
   * 🎨 计算活动多样性分数
   */
  private calculateActivityDiversityScore(activities: Activity[]): number {
    const typeCount = this.countActivityTypes(activities);
    const uniqueTypes = Object.keys(typeCount).length;
    const totalActivities = activities.length;

    if (totalActivities === 0) return 0;

    // 🔧 使用uniqueTypes计算多样性评分
    const diversityRatio = uniqueTypes / Math.max(totalActivities, 1);

    // 🔧 结合多样性比例和香农多样性指数
    let shannonDiversity = 0;
    Object.values(typeCount).forEach(count => {
      const proportion = count / totalActivities;
      if (proportion > 0) {
        shannonDiversity -= proportion * Math.log2(proportion);
      }
    });

    // 标准化到0-1范围
    const maxDiversity = Math.log2(Math.min(6, totalActivities)); // 假设最多6种类型
    const normalizedShannon = maxDiversity > 0 ? shannonDiversity / maxDiversity : 0;

    // 结合多样性比例和香农指数
    return (diversityRatio + normalizedShannon) / 2;
  }

  /**
   * 🕐 获取活动所在的时间段
   */
  private getTimeSlotForActivity(activity: Activity): string {
    if (!activity.startTime) return 'unknown';

    const startMinutes = this.parseTime(activity.startTime);

    if (startMinutes >= 540 && startMinutes < 720) return 'morning'; // 9:00-12:00
    if (startMinutes >= 780 && startMinutes < 1020) return 'afternoon'; // 13:00-17:00
    if (startMinutes >= 1080 && startMinutes < 1260) return 'evening'; // 18:00-21:00
    if (startMinutes >= 1320 || startMinutes < 480) return 'night'; // 22:00-8:00

    return 'unknown';
  }
}
