/**
 * 🚇 高级交通路径计算器
 * 
 * 升级版交通路径计算，支持多模式交通、实时数据和智能路径优化
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import { getTransportIcon, getTransportColor } from '../constants/TransportationIcons';

// ===== 接口定义 =====

export interface AdvancedTransportRoute {
  id: string;
  from: LocationPoint;
  to: LocationPoint;
  segments: TransportSegment[];
  summary: RouteSummary;
  alternatives: AlternativeRoute[];
  realTimeData: RealTimeInfo;
  recommendations: RouteRecommendation[];
}

export interface LocationPoint {
  name: string;
  coordinates: { lat: number; lng: number };
  type: 'station' | 'stop' | 'landmark' | 'address';
  amenities?: string[];
}

export interface TransportSegment {
  id: string;
  mode: TransportMode;
  from: LocationPoint;
  to: LocationPoint;
  distance: number;        // 米
  duration: number;        // 分钟
  cost: number;           // 费用
  instructions: string[];
  realTimeStatus: 'on_time' | 'delayed' | 'cancelled' | 'unknown';
  crowdLevel: 1 | 2 | 3 | 4 | 5;  // 拥挤程度
}

export interface RouteSummary {
  totalDistance: number;
  totalDuration: number;
  totalCost: number;
  transferCount: number;
  walkingDistance: number;
  carbonFootprint: number;  // kg CO2
  difficulty: 'easy' | 'moderate' | 'challenging';
  accessibility: AccessibilityInfo;
}

export interface AlternativeRoute {
  route: AdvancedTransportRoute;
  reason: string;          // 为什么推荐这个替代路线
  savings: {
    time?: number;
    cost?: number;
    distance?: number;
  };
}

export interface RealTimeInfo {
  lastUpdated: Date;
  delays: RouteDelay[];
  disruptions: RouteDisruption[];
  crowdLevels: CrowdLevel[];
  weatherImpact: WeatherImpact;
}

export interface RouteRecommendation {
  type: 'time_optimization' | 'cost_optimization' | 'comfort_optimization' | 'accessibility';
  title: string;
  description: string;
  impact: string;
  priority: 'high' | 'medium' | 'low';
}

export enum TransportMode {
  WALKING = 'walking',
  SUBWAY = 'subway',
  BUS = 'bus',
  TAXI = 'taxi',
  TRAIN = 'train',
  BICYCLE = 'bicycle',
  SCOOTER = 'scooter',
  FERRY = 'ferry',
  RIDESHARE = 'rideshare'
}

export interface AccessibilityInfo {
  wheelchairAccessible: boolean;
  elevatorAvailable: boolean;
  visualAidSupport: boolean;
  audioAnnouncements: boolean;
  accessibilityScore: number; // 0-1
}

export interface RouteDelay {
  segmentId: string;
  delayMinutes: number;
  reason: string;
  severity: 'minor' | 'moderate' | 'major';
}

export interface RouteDisruption {
  affectedModes: TransportMode[];
  description: string;
  startTime: Date;
  estimatedEndTime?: Date;
  alternativeSuggestion: string;
}

export interface CrowdLevel {
  segmentId: string;
  level: 1 | 2 | 3 | 4 | 5;
  timeOfDay: string;
  prediction: 'increasing' | 'stable' | 'decreasing';
}

export interface WeatherImpact {
  condition: string;
  temperature: number;
  precipitation: number;
  impact: 'none' | 'minor' | 'moderate' | 'severe';
  recommendations: string[];
}

// ===== 高级交通路径计算器 =====

export class AdvancedTransportationCalculator {
  
  /**
   * 🎯 计算高级交通路径
   */
  static calculateAdvancedRoute(
    from: LocationPoint,
    to: LocationPoint,
    preferences: RoutePreferences = {}
  ): AdvancedTransportRoute {
    
    // 1. 生成多个路径选项
    const routeOptions = this.generateRouteOptions(from, to, preferences);
    
    // 2. 选择最优路径
    const bestRoute = this.selectBestRoute(routeOptions, preferences);
    
    // 3. 获取实时数据
    const realTimeData = this.getRealTimeData(bestRoute);
    
    // 4. 生成替代路线
    const alternatives = this.generateAlternatives(routeOptions, bestRoute);
    
    // 5. 生成建议
    const recommendations = this.generateRecommendations(bestRoute, preferences);
    
    return {
      id: `route_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      from,
      to,
      segments: bestRoute.segments,
      summary: this.calculateRouteSummary(bestRoute.segments),
      alternatives,
      realTimeData,
      recommendations
    };
  }
  
  /**
   * 🛤️ 生成路径选项
   */
  private static generateRouteOptions(
    from: LocationPoint,
    to: LocationPoint,
    preferences: RoutePreferences
  ): RouteOption[] {
    
    const options: RouteOption[] = [];
    
    // 选项1: 最快路径 (地铁+步行)
    options.push(this.generateSubwayRoute(from, to));
    
    // 选项2: 最经济路径 (公交+步行)
    options.push(this.generateBusRoute(from, to));
    
    // 选项3: 最舒适路径 (出租车)
    options.push(this.generateTaxiRoute(from, to));
    
    // 选项4: 环保路径 (步行+自行车)
    if (this.calculateDistance(from, to) < 5000) {
      options.push(this.generateEcoRoute(from, to));
    }
    
    // 选项5: 无障碍路径
    if (preferences.accessibilityRequired) {
      options.push(this.generateAccessibleRoute(from, to));
    }
    
    return options.filter(option => option !== null);
  }
  
  /**
   * 🚇 生成地铁路径
   */
  private static generateSubwayRoute(from: LocationPoint, to: LocationPoint): RouteOption {
    const segments: TransportSegment[] = [];
    
    // 步行到地铁站
    segments.push({
      id: 'walk_to_subway',
      mode: TransportMode.WALKING,
      from,
      to: { ...from, name: '地铁站', type: 'station' },
      distance: 300,
      duration: 4,
      cost: 0,
      instructions: ['步行至最近的地铁站'],
      realTimeStatus: 'on_time',
      crowdLevel: 2
    });
    
    // 地铁段
    segments.push({
      id: 'subway_main',
      mode: TransportMode.SUBWAY,
      from: { ...from, name: '地铁站', type: 'station' },
      to: { ...to, name: '地铁站', type: 'station' },
      distance: this.calculateDistance(from, to) * 0.8,
      duration: Math.ceil(this.calculateDistance(from, to) / 1000 * 2.5),
      cost: Math.max(3, Math.min(8, Math.ceil(this.calculateDistance(from, to) / 1000 * 2))),
      instructions: ['搭乘地铁至目的地附近站点'],
      realTimeStatus: 'on_time',
      crowdLevel: 3
    });
    
    // 步行到目的地
    segments.push({
      id: 'walk_from_subway',
      mode: TransportMode.WALKING,
      from: { ...to, name: '地铁站', type: 'station' },
      to,
      distance: 400,
      duration: 5,
      cost: 0,
      instructions: ['步行至目的地'],
      realTimeStatus: 'on_time',
      crowdLevel: 1
    });
    
    return { segments, priority: 'time' };
  }
  
  /**
   * 🚌 生成公交路径
   */
  private static generateBusRoute(from: LocationPoint, to: LocationPoint): RouteOption {
    const segments: TransportSegment[] = [];
    
    // 步行到公交站
    segments.push({
      id: 'walk_to_bus',
      mode: TransportMode.WALKING,
      from,
      to: { ...from, name: '公交站', type: 'stop' },
      distance: 200,
      duration: 3,
      cost: 0,
      instructions: ['步行至公交站'],
      realTimeStatus: 'on_time',
      crowdLevel: 1
    });
    
    // 公交段
    segments.push({
      id: 'bus_main',
      mode: TransportMode.BUS,
      from: { ...from, name: '公交站', type: 'stop' },
      to: { ...to, name: '公交站', type: 'stop' },
      distance: this.calculateDistance(from, to) * 1.2,
      duration: Math.ceil(this.calculateDistance(from, to) / 1000 * 4),
      cost: Math.max(2, Math.ceil(this.calculateDistance(from, to) / 1000 * 1.5)),
      instructions: ['搭乘公交至目的地附近'],
      realTimeStatus: 'on_time',
      crowdLevel: 2
    });
    
    // 步行到目的地
    segments.push({
      id: 'walk_from_bus',
      mode: TransportMode.WALKING,
      from: { ...to, name: '公交站', type: 'stop' },
      to,
      distance: 300,
      duration: 4,
      cost: 0,
      instructions: ['步行至目的地'],
      realTimeStatus: 'on_time',
      crowdLevel: 1
    });
    
    return { segments, priority: 'cost' };
  }
  
  /**
   * 🚕 生成出租车路径
   */
  private static generateTaxiRoute(from: LocationPoint, to: LocationPoint): RouteOption {
    const distance = this.calculateDistance(from, to);
    
    const segments: TransportSegment[] = [{
      id: 'taxi_direct',
      mode: TransportMode.TAXI,
      from,
      to,
      distance,
      duration: Math.ceil(distance / 1000 * 2.5), // 考虑交通状况
      cost: Math.max(10, Math.ceil(distance / 1000 * 4)),
      instructions: ['搭乘出租车直达目的地'],
      realTimeStatus: 'on_time',
      crowdLevel: 1
    }];
    
    return { segments, priority: 'comfort' };
  }
  
  /**
   * 🌱 生成环保路径
   */
  private static generateEcoRoute(from: LocationPoint, to: LocationPoint): RouteOption {
    const distance = this.calculateDistance(from, to);
    const segments: TransportSegment[] = [];
    
    if (distance < 2000) {
      // 纯步行
      segments.push({
        id: 'walk_direct',
        mode: TransportMode.WALKING,
        from,
        to,
        distance,
        duration: Math.ceil(distance / 80), // 步行速度约80米/分钟
        cost: 0,
        instructions: ['步行至目的地'],
        realTimeStatus: 'on_time',
        crowdLevel: 1
      });
    } else {
      // 步行+自行车
      const midPoint = { ...from, name: '自行车租赁点', type: 'stop' as const };
      
      segments.push({
        id: 'walk_to_bike',
        mode: TransportMode.WALKING,
        from,
        to: midPoint,
        distance: 500,
        duration: 6,
        cost: 0,
        instructions: ['步行至自行车租赁点'],
        realTimeStatus: 'on_time',
        crowdLevel: 1
      });
      
      segments.push({
        id: 'bike_main',
        mode: TransportMode.BICYCLE,
        from: midPoint,
        to: { ...to, name: '自行车还车点', type: 'stop' },
        distance: distance - 800,
        duration: Math.ceil((distance - 800) / 250), // 自行车速度约250米/分钟
        cost: 5,
        instructions: ['骑行至目的地附近'],
        realTimeStatus: 'on_time',
        crowdLevel: 1
      });
      
      segments.push({
        id: 'walk_from_bike',
        mode: TransportMode.WALKING,
        from: { ...to, name: '自行车还车点', type: 'stop' },
        to,
        distance: 300,
        duration: 4,
        cost: 0,
        instructions: ['步行至目的地'],
        realTimeStatus: 'on_time',
        crowdLevel: 1
      });
    }
    
    return { segments, priority: 'eco' };
  }
  
  /**
   * ♿ 生成无障碍路径
   */
  private static generateAccessibleRoute(from: LocationPoint, to: LocationPoint): RouteOption {
    // 优先选择有电梯和无障碍设施的路径
    return this.generateSubwayRoute(from, to); // 简化实现
  }
  
  /**
   * 🎯 选择最佳路径
   */
  private static selectBestRoute(options: RouteOption[], preferences: RoutePreferences): RouteOption {
    if (preferences.priority === 'time') {
      return options.reduce((best, current) => 
        this.getTotalDuration(current.segments) < this.getTotalDuration(best.segments) ? current : best
      );
    } else if (preferences.priority === 'cost') {
      return options.reduce((best, current) => 
        this.getTotalCost(current.segments) < this.getTotalCost(best.segments) ? current : best
      );
    } else {
      // 默认平衡选择
      return options[0];
    }
  }
  
  /**
   * 📊 计算路径摘要
   */
  private static calculateRouteSummary(segments: TransportSegment[]): RouteSummary {
    return {
      totalDistance: segments.reduce((sum, seg) => sum + seg.distance, 0),
      totalDuration: segments.reduce((sum, seg) => sum + seg.duration, 0),
      totalCost: segments.reduce((sum, seg) => sum + seg.cost, 0),
      transferCount: segments.filter(seg => seg.mode !== TransportMode.WALKING).length - 1,
      walkingDistance: segments.filter(seg => seg.mode === TransportMode.WALKING)
        .reduce((sum, seg) => sum + seg.distance, 0),
      carbonFootprint: this.calculateCarbonFootprint(segments),
      difficulty: 'easy',
      accessibility: {
        wheelchairAccessible: true,
        elevatorAvailable: true,
        visualAidSupport: false,
        audioAnnouncements: true,
        accessibilityScore: 0.8
      }
    };
  }
  
  // ===== 辅助方法 =====
  
  private static calculateDistance(from: LocationPoint, to: LocationPoint): number {
    // 简化的距离计算（实际应使用地理计算）
    const latDiff = Math.abs(from.coordinates.lat - to.coordinates.lat);
    const lngDiff = Math.abs(from.coordinates.lng - to.coordinates.lng);
    return Math.sqrt(latDiff * latDiff + lngDiff * lngDiff) * 111000; // 粗略转换为米
  }
  
  private static getTotalDuration(segments: TransportSegment[]): number {
    return segments.reduce((sum, seg) => sum + seg.duration, 0);
  }
  
  private static getTotalCost(segments: TransportSegment[]): number {
    return segments.reduce((sum, seg) => sum + seg.cost, 0);
  }
  
  private static calculateCarbonFootprint(segments: TransportSegment[]): number {
    const emissionFactors: Record<TransportMode, number> = {
      [TransportMode.WALKING]: 0,
      [TransportMode.BICYCLE]: 0,
      [TransportMode.SUBWAY]: 0.05,
      [TransportMode.BUS]: 0.08,
      [TransportMode.TAXI]: 0.2,
      [TransportMode.TRAIN]: 0.04,
      [TransportMode.SCOOTER]: 0.02,
      [TransportMode.FERRY]: 0.1,
      [TransportMode.RIDESHARE]: 0.15
    };
    
    return segments.reduce((total, seg) => {
      const factor = emissionFactors[seg.mode] || 0.1;
      return total + (seg.distance / 1000) * factor;
    }, 0);
  }
  
  private static getRealTimeData(route: RouteOption): RealTimeInfo {
    return {
      lastUpdated: new Date(),
      delays: [],
      disruptions: [],
      crowdLevels: [],
      weatherImpact: {
        condition: '晴朗',
        temperature: 25,
        precipitation: 0,
        impact: 'none',
        recommendations: []
      }
    };
  }
  
  private static generateAlternatives(options: RouteOption[], bestRoute: RouteOption): AlternativeRoute[] {
    return options
      .filter(option => option !== bestRoute)
      .slice(0, 2)
      .map(option => ({
        route: {
          id: 'alt_' + Date.now(),
          from: bestRoute.segments[0].from,
          to: bestRoute.segments[bestRoute.segments.length - 1].to,
          segments: option.segments,
          summary: this.calculateRouteSummary(option.segments),
          alternatives: [],
          realTimeData: this.getRealTimeData(option),
          recommendations: []
        },
        reason: '更经济的选择',
        savings: { cost: 5 }
      }));
  }
  
  private static generateRecommendations(route: RouteOption, preferences: RoutePreferences): RouteRecommendation[] {
    return [
      {
        type: 'time_optimization',
        title: '避开高峰时段',
        description: '建议在非高峰时段出行，可节省15-20分钟',
        impact: '节省时间',
        priority: 'medium'
      }
    ];
  }
}

// ===== 辅助接口 =====

interface RouteOption {
  segments: TransportSegment[];
  priority: string;
}

interface RoutePreferences {
  priority?: 'time' | 'cost' | 'comfort' | 'eco';
  accessibilityRequired?: boolean;
  maxWalkingDistance?: number;
  avoidModes?: TransportMode[];
}

export default AdvancedTransportationCalculator;
