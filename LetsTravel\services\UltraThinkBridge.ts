/**
 * 🌉 Ultra Think 桥接器
 * Phase A: 基础设施准备 - 创建6-Agent到Ultra Think的桥接层
 * 实现渐进式迁移，确保系统稳定性
 */

import { ultraThinkConfig, UltraThinkConfig } from '../config/UltraThinkConfig';
import { AGENT_MODEL_CONFIG } from '../config/AIModelConfig';
import { ultraThinkLLMManager, UltraThinkLLMRequest } from './ai/UltraThinkLLMManager';
import { ultraThinkAPIManager, UltraThinkAPIRequest } from './api/UltraThinkAPIManager';
import { ultraThinkActivityGenerator, UltraThinkActivityRequest } from './activity/UltraThinkActivityGenerator';
import { ultraThinkUIManager, UltraThinkUIRequest } from './ui/UltraThinkUIManager';

// ===== 桥接器接口 =====

export interface BridgeResult<T = any> {
  success: boolean;
  data?: T;
  source: '6-agent' | 'ultra-think' | 'hybrid';
  executionTime: number;
  fallbackUsed: boolean;
  error?: string;
}

export interface MigrationMetrics {
  phase: 'A' | 'B' | 'C' | 'D';
  ultraThinkUsage: number;
  sixAgentUsage: number;
  successRate: number;
  averageResponseTime: number;
  errorRate: number;
}

// ===== Ultra Think 桥接器 =====

export class UltraThinkBridge {
  private static instance: UltraThinkBridge;
  private config: UltraThinkConfig;
  private metrics: MigrationMetrics;
  private isInitialized: boolean = false;

  private constructor() {
    this.config = ultraThinkConfig.getConfig();
    this.metrics = this.initializeMetrics();
  }

  static getInstance(): UltraThinkBridge {
    if (!UltraThinkBridge.instance) {
      UltraThinkBridge.instance = new UltraThinkBridge();
    }
    return UltraThinkBridge.instance;
  }

  /**
   * 🚀 初始化Ultra Think桥接器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log('🌉 初始化Ultra Think桥接器...');
    console.log(`📊 当前Phase: ${this.config.migration.currentPhase}`);
    console.log(`🔧 测试模式: ${this.config.migration.testMode ? '启用' : '禁用'}`);
    console.log(`🛡️ 6-Agent降级: ${this.config.migration.fallbackTo6Agent ? '启用' : '禁用'}`);

    try {
      // Phase A: 基础设施准备
      if (this.config.migration.currentPhase === 'A') {
        await this.initializePhaseA();
      }

      this.isInitialized = true;
      console.log('✅ Ultra Think桥接器初始化完成');
    } catch (error) {
      console.error('❌ Ultra Think桥接器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 🔧 Phase A: 基础设施准备
   */
  private async initializePhaseA(): Promise<void> {
    console.log('🔧 Phase A: 基础设施准备');
    
    // 1. 验证配置
    this.validateConfiguration();
    
    // 2. 初始化监控
    this.initializeMonitoring();
    
    // 3. 准备降级机制
    this.prepareFallbackMechanisms();
    
    console.log('✅ Phase A 初始化完成');
  }

  /**
   * 🎯 智能路由 - 决定使用哪个系统
   */
  async routeRequest<T>(
    requestType: 'llm' | 'activity-generation' | 'api-call' | 'ui-render',
    request: any,
    options?: {
      forceUltraThink?: boolean;
      force6Agent?: boolean;
      enableFallback?: boolean;
      enableMasterSolver?: boolean; // 🎯 强制启用Master Solver
    }
  ): Promise<BridgeResult<T>> {
    const startTime = Date.now();
    
    try {
      // 决定使用哪个系统
      const useUltraThink = this.shouldUseUltraThink(requestType, options);
      
      if (useUltraThink) {
        return await this.executeWithUltraThink<T>(requestType, request, startTime, options);
      } else {
        return await this.executeWith6Agent<T>(requestType, request, startTime);
      }
    } catch (error) {
      console.error(`❌ 桥接器路由失败 (${requestType}):`, error);
      
      // 降级到6-Agent系统
      if (this.config.migration.fallbackTo6Agent) {
        return await this.executeWith6Agent<T>(requestType, request, startTime);
      }
      
      throw error;
    }
  }

  /**
   * 🤖 决定是否使用Ultra Think
   */
  private shouldUseUltraThink(
    requestType: string,
    options?: any
  ): boolean {
    // 强制选项
    if (options?.forceUltraThink) return true;
    if (options?.force6Agent) return false;
    
    // 系统未启用
    if (!this.config.enabled) return false;
    
    // 根据Phase决定
    switch (this.config.migration.currentPhase) {
      case 'A':
        // Phase A: 只在测试模式下使用Ultra Think
        return this.config.migration.testMode && requestType === 'llm';
        
      case 'B':
        // Phase B: 核心模块使用Ultra Think
        return ['llm', 'api-call'].includes(requestType);
        
      case 'C':
        // Phase C: 大部分功能使用Ultra Think
        return ['llm', 'api-call', 'activity-generation', 'ui-render'].includes(requestType);
        
      case 'D':
        // Phase D: 全面使用Ultra Think + 强制Master Solver
        console.log('🎯 Phase D: 强制使用Ultra Think Master Solver');
        return true;
        
      default:
        return false;
    }
  }

  /**
   * 🚀 使用Ultra Think执行
   */
  private async executeWithUltraThink<T>(
    requestType: string,
    request: any,
    startTime: number,
    options?: any
  ): Promise<BridgeResult<T>> {
    console.log(`🚀 使用Ultra Think执行: ${requestType}`);
    if (options?.enableMasterSolver) {
      console.log('🎯 Master Solver已启用！');
    }
    
    try {
      let result: any;
      
      switch (requestType) {
        case 'llm':
          result = await this.executeUltraThinkLLM(request);
          break;
          
        case 'activity-generation':
          result = await this.executeUltraThinkActivityGeneration(request, options);
          break;
          
        case 'api-call':
          result = await this.executeUltraThinkAPICall(request);
          break;
          
        case 'ui-render':
          result = await this.executeUltraThinkUIRender(request);
          break;
          
        default:
          throw new Error(`不支持的请求类型: ${requestType}`);
      }
      
      // 更新指标
      this.updateMetrics('ultra-think', true, Date.now() - startTime);
      
      return {
        success: true,
        data: result,
        source: 'ultra-think',
        executionTime: Date.now() - startTime,
        fallbackUsed: false
      };
      
    } catch (error) {
      console.error(`❌ Ultra Think执行失败 (${requestType}):`, error);
      
      // 更新指标
      this.updateMetrics('ultra-think', false, Date.now() - startTime);
      
      // 如果启用降级，尝试6-Agent
      if (this.config.migration.fallbackTo6Agent) {
        console.log('🔄 降级到6-Agent系统');
        const fallbackResult = await this.executeWith6Agent<T>(requestType, request, startTime);
        return {
          ...fallbackResult,
          source: 'hybrid',
          fallbackUsed: true
        };
      }
      
      throw error;
    }
  }

  /**
   * 🎯 使用6-Agent执行
   */
  private async executeWith6Agent<T>(
    requestType: string,
    request: any,
    startTime: number
  ): Promise<BridgeResult<T>> {
    console.log(`🎯 使用6-Agent执行: ${requestType}`);
    
    try {
      // 这里调用现有的6-Agent系统
      // 暂时返回模拟结果，后续会集成真实的6-Agent调用
      const result = await this.simulate6AgentCall(requestType, request);
      
      // 更新指标
      this.updateMetrics('6-agent', true, Date.now() - startTime);
      
      return {
        success: true,
        data: result,
        source: '6-agent',
        executionTime: Date.now() - startTime,
        fallbackUsed: false
      };
      
    } catch (error) {
      console.error(`❌ 6-Agent执行失败 (${requestType}):`, error);
      
      // 更新指标
      this.updateMetrics('6-agent', false, Date.now() - startTime);
      
      throw error;
    }
  }





  /**
   * 🎨 Ultra Think UI渲染
   */
  private async executeUltraThinkUIRender(request: any): Promise<any> {
    console.log('🎨 Ultra Think UI渲染 (真实)');

    try {
      // 构建Ultra Think UI请求
      const uiRequest: UltraThinkUIRequest = {
        componentType: request.componentType || 'activity-card',
        data: request.data || {},
        options: {
          theme: request.theme || 'light',
          size: request.size || 'medium',
          interactive: request.interactive !== false,
          accessibility: request.accessibility !== false,
          responsive: request.responsive !== false
        }
      };

      // 调用Ultra Think UI管理器
      const result = await ultraThinkUIManager.renderComponent(uiRequest);

      return {
        component: result.component,
        componentType: request.componentType,
        source: 'ultra-think-ui',
        success: result.success,
        metadata: result.metadata,
        renderTime: result.metadata.renderTime,
        accessibility: result.metadata.accessibility,
        responsive: result.metadata.responsive,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Ultra Think UI渲染失败:', error);

      // 返回降级响应
      return {
        component: null,
        componentType: 'error',
        source: 'ultra-think-ui-fallback',
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 🎯 模拟6-Agent调用
   */
  private async simulate6AgentCall(requestType: string, request: any): Promise<any> {
    // 模拟6-Agent系统的响应
    await new Promise(resolve => setTimeout(resolve, 100)); // 模拟延迟
    
    return {
      result: `6-Agent ${requestType} 响应`,
      source: '6-agent-system',
      timestamp: new Date().toISOString()
    };
  }



  /**
   * 🔧 工具方法
   */
  private validateConfiguration(): void {
    console.log('🔧 验证Ultra Think配置...');
    // 配置验证逻辑
  }

  private initializeMonitoring(): void {
    console.log('📊 初始化性能监控...');
    // 监控初始化逻辑
  }

  private prepareFallbackMechanisms(): void {
    console.log('🛡️ 准备降级机制...');
    // 降级机制准备逻辑
  }

  private initializeMetrics(): MigrationMetrics {
    return {
      phase: this.config.migration.currentPhase,
      ultraThinkUsage: 0,
      sixAgentUsage: 0,
      successRate: 1.0,
      averageResponseTime: 0,
      errorRate: 0
    };
  }

  /**
   * 📊 获取迁移指标
   */
  getMetrics(): MigrationMetrics {
    return { ...this.metrics };
  }

  /**
   * 🔄 更新Phase
   */
  updatePhase(phase: 'A' | 'B' | 'C' | 'D'): void {
    this.config.migration.currentPhase = phase;
    this.metrics.phase = phase;
    console.log(`🔄 Ultra Think迁移Phase更新为: ${phase}`);
  }

  /**
   * 🤖 Ultra Think LLM调用
   */
  private async executeUltraThinkLLM(request: any): Promise<any> {
    console.log('🤖 Ultra Think LLM调用 (真实)');

    try {
      // 构建Ultra Think LLM请求
      const llmRequest: UltraThinkLLMRequest = {
        prompt: request.prompt || request.messages?.[0]?.content || '',
        systemPrompt: request.systemPrompt || request.system,
        taskType: request.taskType || 'planner',
        context: {
          destination: request.destination,
          duration: request.duration,
          budget: request.budget,
          complexity: request.complexity || 'medium',
          priority: request.priority || 'medium'
        },
        options: {
          temperature: request.temperature || 0.7,
          maxTokens: request.maxTokens || 4000,
          enableFallback: true
        }
      };

      // 调用Ultra Think LLM管理器
      const result = await ultraThinkLLMManager.callLLM(llmRequest);

      return {
        response: result.content,
        model: result.model,
        source: 'ultra-think-llm',
        success: result.success,
        executionTime: result.executionTime,
        fallbackUsed: result.fallbackUsed,
        metadata: result.metadata,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Ultra Think LLM调用失败:', error);

      // 返回降级响应
      return {
        response: '抱歉，AI服务暂时不可用，请稍后重试。',
        model: 'emergency',
        source: 'ultra-think-llm-fallback',
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 🎯 Ultra Think活动生成
   */
  private async executeUltraThinkActivityGeneration(request: any, options?: any): Promise<any> {
    console.log('🎯 Ultra Think活动生成 (Master Solver模式)');
    console.log('🚀 强制启用Master Solver - 彻底解决所有问题');
    if (options?.enableMasterSolver) {
      console.log('🎯🎯🎯 Master Solver强制启用确认！🎯🎯🎯');
    }

    try {
      // 构建Ultra Think活动生成请求
      const activityRequest: UltraThinkActivityRequest = {
        destination: request.destination || '东京',
        duration: request.duration || 3,
        travelers: request.travelers || 2,
        budget: request.budget || 8000,
        currency: request.currency || 'MYR',
        startDate: request.startDate ? new Date(request.startDate) : new Date(),
        preferences: {
          travelStyle: request.travelStyle || ['cultural'],
          accommodation: request.accommodation || ['mid_range'],
          transport: request.transport || ['public'],
          interests: request.interests || ['sightseeing']
        },
        options: {
          minActivitiesPerDay: 3,
          maxActivitiesPerDay: 6,
          includeTransport: true,
          includeAccommodation: true,
          includeFlights: true,
          includeMeals: true, // 🔧 修复：启用餐饮系统
          enableSeasonalOptimization: true
        }
      };

      // 调用Ultra Think活动生成器
      const result = await ultraThinkActivityGenerator.generateActivities(activityRequest);

      // 🔧 修复：转换为Journey格式
      const journeyData = this.convertToJourneyFormat(result, activityRequest);

      console.log('🎯 Ultra Think生成的Journey数据:', {
        activitiesCount: journeyData.activities?.length || 0,
        destination: journeyData.destination,
        title: journeyData.title,
        source: 'ultra-think',
        executionTime: result.executionTime
      });

      return journeyData;

    } catch (error) {
      console.error('❌ Ultra Think活动生成失败:', error);

      // 返回降级响应
      return {
        activities: [
          {
            id: 'fallback-activity-1',
            name: '基础旅行活动',
            type: 'attraction',
            duration: 120,
            cost: 50,
            description: '由于服务暂时不可用，这是基础的活动建议'
          }
        ],
        source: 'ultra-think-generator-fallback',
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 🔧 Ultra Think API调用
   */
  private async executeUltraThinkAPICall(request: any): Promise<any> {
    console.log('🔧 Ultra Think API调用 (真实)');

    try {
      // 构建Ultra Think API请求
      const apiRequest: UltraThinkAPIRequest = {
        type: request.type || 'places',
        params: {
          ...request.params,
          query: request.query,
          location: request.location,
          destination: request.destination,
          origin: request.origin,
          checkIn: request.checkIn,
          checkOut: request.checkOut,
          guests: request.guests,
          passengers: request.passengers
        },
        options: {
          enableFallback: true,
          timeout: request.timeout || 15000,
          priority: request.priority || 'medium',
          cacheEnabled: true
        }
      };

      // 调用Ultra Think API管理器
      const result = await ultraThinkAPIManager.callAPI(apiRequest);

      return {
        data: result.data,
        source: 'ultra-think-api',
        enhanced: true,
        success: result.success,
        executionTime: result.executionTime,
        apiUsed: result.apiUsed,
        fallbackUsed: result.fallbackUsed,
        metadata: result.metadata,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Ultra Think API调用失败:', error);

      // 返回降级响应
      return {
        data: { message: '暂时无法获取数据，请稍后重试' },
        source: 'ultra-think-api-fallback',
        enhanced: false,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }




  /**
   * 🔄 转换为Journey格式
   */
  private convertToJourneyFormat(ultraThinkResult: any, request: any): any {
    const startDate = new Date(request.startDate);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + request.duration - 1);

    // 🔧 修复：转换UltraThinkActivity为前端期望的Activity格式
    const convertedActivities = this.convertActivitiesToFrontendFormat(ultraThinkResult.activities || []);

    console.log('🔄 活动数据转换:', {
      原始活动数量: ultraThinkResult.activities?.length || 0,
      转换后活动数量: convertedActivities.length,
      示例原始活动: ultraThinkResult.activities?.[0] ? {
        name: ultraThinkResult.activities[0].name,
        timing: ultraThinkResult.activities[0].timing,
        cost: ultraThinkResult.activities[0].cost
      } : null,
      示例转换活动: convertedActivities[0] ? {
        title: convertedActivities[0].title,
        startTime: convertedActivities[0].startTime,
        endTime: convertedActivities[0].endTime,
        cost: convertedActivities[0].cost
      } : null
    });

    return {
      id: `journey_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: `${request.destination}${request.duration}天精彩之旅`,
      destination: {
        name: request.destination,
        coordinates: { lat: 35.6762, lng: 139.6503 }, // 默认坐标，后续可优化
        country: '日本' // 根据destination动态设置
      },
      startDate: startDate,
      endDate: endDate,
      duration: request.duration,
      travelers: request.travelers,
      budget: {
        total: request.budget,
        currency: request.currency,
        breakdown: this.convertBudgetBreakdown(ultraThinkResult.summary?.budget, convertedActivities, request.budget)
      },
      activities: convertedActivities, // 🔧 使用转换后的活动数据
      preferences: request.preferences,
      metadata: {
        source: 'ultra-think',
        generatedAt: new Date().toISOString(),
        qualityScore: ultraThinkResult.optimization?.diversityScore || 85,
        executionTime: ultraThinkResult.executionTime,
        totalActivities: convertedActivities.length
      },
      // 保持与原有系统兼容
      summary: ultraThinkResult.summary,
      optimization: ultraThinkResult.optimization
    };
  }

  /**
   * 🔄 转换UltraThinkActivity为前端期望的Activity格式
   */
  private convertActivitiesToFrontendFormat(ultraThinkActivities: any[]): any[] {
    return ultraThinkActivities.map((activity, index) => {
      // 处理时间字段
      const startTime = activity.startTime || activity.timing?.startTime || '09:00';
      const endTime = activity.endTime || activity.timing?.endTime || '10:00';
      const duration = activity.duration || activity.timing?.duration || 60;

      // 处理成本字段
      const cost = typeof activity.cost === 'number' ? activity.cost :
                   (activity.cost?.amount || 0);
      const currency = activity.currency || activity.cost?.currency || 'MYR';

      // 处理位置字段
      const location = activity.location ? {
        name: activity.location.name || activity.location.address || '',
        address: activity.location.address || activity.location.name || '',
        coordinates: activity.location.coordinates
      } : undefined;

      // 🔧 修复：构建丰富的描述和详细信息
      const highlights = activity.details?.highlights || [];
      const tips = activity.details?.tips || [];
      const rating = activity.details?.rating || activity.rating;

      // 构建详细描述
      let detailedDescription = activity.description || activity.description_zh || '';
      if (highlights.length > 0) {
        detailedDescription += (detailedDescription ? '\n\n' : '') + '✨ 亮点：\n' + highlights.map(h => `• ${h}`).join('\n');
      }
      if (rating) {
        detailedDescription += (detailedDescription ? '\n\n' : '') + `⭐ 评分：${rating}/5`;
      }

      // 构建详细备注
      let detailedNotes = '';
      if (tips.length > 0) {
        detailedNotes += '💡 小贴士：\n' + tips.map(t => `• ${t}`).join('\n');
      }
      if (activity.details?.seasonalInfo) {
        detailedNotes += (detailedNotes ? '\n\n' : '') + `🌸 季节信息：${activity.details.seasonalInfo}`;
      }
      if (activity.details?.bookingRequired) {
        detailedNotes += (detailedNotes ? '\n\n' : '') + '📝 需要预订';
      }

      return {
        id: activity.id || `activity_${index}_${Date.now()}`,
        journeyId: undefined, // 将在保存时设置
        title: activity.name || activity.title || `活动 ${index + 1}`,
        description: detailedDescription || `探索${activity.name || '精彩活动'}`,
        type: activity.type || 'attraction',
        location: location,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        cost: cost,
        currency: currency,
        bookingInfo: activity.details?.bookingRequired ? {
          platform: activity.details?.platform || '待确认',
          confirmationNumber: '',
          bookingUrl: activity.details?.website || '',
          status: 'pending' as const
        } : undefined,
        notes: detailedNotes || '暂无特别说明',
        photos: activity.photos || [],
        isCompleted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        dayNumber: activity.timing?.day || Math.ceil((index + 1) / 4), // 估算天数
        orderIndex: index
      };
    });
  }

  /**
   * 💰 转换预算分类为前端期望格式
   */
  private convertBudgetBreakdown(ultraThinkBudget: any, activities: any[], totalBudget: number): any {
    // 如果Ultra Think提供了预算分类，使用它
    if (ultraThinkBudget && typeof ultraThinkBudget === 'object') {
      return {
        accommodation: ultraThinkBudget.accommodation || ultraThinkBudget.hotel || 0,
        transportation: ultraThinkBudget.transportation || ultraThinkBudget.transport || 0,
        food: ultraThinkBudget.food || ultraThinkBudget.dining || ultraThinkBudget.meals || 0,
        activities: ultraThinkBudget.activities || ultraThinkBudget.attractions || 0,
        shopping: ultraThinkBudget.shopping || 0
      };
    }

    // 否则根据活动类型估算预算分配
    const breakdown = {
      accommodation: 0,
      transportation: 0,
      food: 0,
      activities: 0,
      shopping: 0
    };

    // 统计各类型活动的费用
    activities.forEach(activity => {
      const cost = activity.cost || 0;
      switch (activity.type) {
        case 'hotel':
        case 'accommodation':
          breakdown.accommodation += cost;
          break;
        case 'transport':
        case 'transportation':
          breakdown.transportation += cost;
          break;
        case 'dining':
        case 'restaurant':
        case 'food':
          breakdown.food += cost;
          break;
        case 'shopping':
          breakdown.shopping += cost;
          break;
        default:
          breakdown.activities += cost;
          break;
      }
    });

    // 如果活动费用总和小于总预算，按比例分配剩余预算
    const totalActivityCost = Object.values(breakdown).reduce((sum, cost) => sum + cost, 0);
    if (totalActivityCost < totalBudget && totalActivityCost > 0) {
      const ratio = totalBudget / totalActivityCost;
      Object.keys(breakdown).forEach(key => {
        breakdown[key as keyof typeof breakdown] = Math.round(breakdown[key as keyof typeof breakdown] * ratio);
      });
    } else if (totalActivityCost === 0) {
      // 如果没有活动费用，使用标准分配比例
      breakdown.accommodation = Math.round(totalBudget * 0.35);
      breakdown.food = Math.round(totalBudget * 0.25);
      breakdown.activities = Math.round(totalBudget * 0.20);
      breakdown.transportation = Math.round(totalBudget * 0.15);
      breakdown.shopping = Math.round(totalBudget * 0.05);
    }

    console.log('💰 预算分类转换:', {
      原始: ultraThinkBudget,
      转换后: breakdown,
      总预算: totalBudget,
      活动费用总和: totalActivityCost
    });

    return breakdown;
  }

  /**
   * 📊 更新指标
   */
  private updateMetrics(
    source: '6-agent' | 'ultra-think',
    success: boolean,
    responseTime: number
  ): void {
    if (source === 'ultra-think') {
      this.metrics.ultraThinkUsage++;
    } else {
      this.metrics.sixAgentUsage++;
    }

    // 更新成功率和响应时间
    const totalRequests = this.metrics.ultraThinkUsage + this.metrics.sixAgentUsage;
    this.metrics.successRate = success ?
      (this.metrics.successRate * (totalRequests - 1) + 1) / totalRequests :
      (this.metrics.successRate * (totalRequests - 1)) / totalRequests;

    this.metrics.averageResponseTime =
      (this.metrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;
  }
}

// ===== 导出桥接器实例 =====

export const ultraThinkBridge = UltraThinkBridge.getInstance();
