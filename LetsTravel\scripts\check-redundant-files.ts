/**
 * 🔍 冗余文件检查器
 * 检查方案A中创建但在方案B中可能不需要的文件
 */

import * as fs from 'fs';
import * as path from 'path';

interface RedundantFile {
  path: string;
  reason: string;
  size: number;
  isUsed: boolean;
  alternatives: string[];
}

function checkRedundantFiles(): RedundantFile[] {
  console.log('🔍🔍🔍 开始检查冗余文件 🔍🔍🔍');
  console.log('='.repeat(50));
  
  const redundantFiles: RedundantFile[] = [];
  
  // 方案A中创建的文件列表
  const schemeAFiles = [
    {
      path: 'services/timeline/UnifiedTimelineBuilder.ts',
      reason: '方案A的时间线构建器，方案B使用直接修复方式',
      alternatives: ['services/budget/SimpleBudgetFixer.ts', 'components/journey/FixedDayCard.tsx']
    },
    {
      path: 'components/journey/TimelineDayCard.tsx',
      reason: '方案A的时间线卡片组件，方案B使用FixedDayCard',
      alternatives: ['components/journey/FixedDayCard.tsx']
    },
    {
      path: 'components/adapters/TimelineAdapter.tsx',
      reason: '方案A的时间线适配器，方案B使用FixedJourneyAdapter',
      alternatives: ['components/adapters/FixedJourneyAdapter.tsx']
    },
    {
      path: 'services/converters/TimelineDataConverter.ts',
      reason: '方案A的时间线数据转换器，方案B直接修复数据传递链',
      alternatives: ['services/converters/JSONDataConverter.ts (已修复)']
    },
    {
      path: 'components/journey/OptimizedDayCard.tsx',
      reason: '早期优化尝试，被后续方案替代',
      alternatives: ['components/journey/FixedDayCard.tsx']
    },
    {
      path: 'components/adapters/JSONDataAdapter.tsx',
      reason: '中间方案的适配器，方案B使用更简单的修复方式',
      alternatives: ['components/adapters/FixedJourneyAdapter.tsx']
    },
    {
      path: 'scripts/test-timeline-system.ts',
      reason: '方案A的测试脚本，方案B有专门的测试脚本',
      alternatives: ['scripts/test-fixes.ts']
    },
    {
      path: 'scripts/test-complete-system.ts',
      reason: '早期的完整系统测试，功能重复',
      alternatives: ['scripts/test-fixes.ts', 'scripts/quick-test.ts']
    },
    {
      path: 'docs/OPTIMIZATION_COMPLETE.md',
      reason: '方案A的文档，内容已过时',
      alternatives: ['docs/TIMELINE_SYSTEM_STATUS.md (需要更新)']
    }
  ];
  
  console.log('\n📋 检查文件状态...');
  
  schemeAFiles.forEach(file => {
    const fullPath = path.join(process.cwd(), file.path);
    const exists = fs.existsSync(fullPath);
    
    if (exists) {
      const stats = fs.statSync(fullPath);
      const isUsed = checkFileUsage(file.path);
      
      redundantFiles.push({
        path: file.path,
        reason: file.reason,
        size: stats.size,
        isUsed,
        alternatives: file.alternatives
      });
      
      console.log(`${isUsed ? '🟡' : '🔴'} ${file.path} (${(stats.size / 1024).toFixed(1)}KB)`);
      console.log(`   理由: ${file.reason}`);
      console.log(`   使用状态: ${isUsed ? '仍在使用' : '未被使用'}`);
      console.log(`   替代方案: ${file.alternatives.join(', ')}`);
      console.log('');
    } else {
      console.log(`⚪ ${file.path} (不存在)`);
    }
  });
  
  return redundantFiles;
}

/**
 * 🔍 检查文件是否被使用
 */
function checkFileUsage(filePath: string): boolean {
  const fileName = path.basename(filePath, path.extname(filePath));
  const searchDirs = ['components', 'screens', 'services', 'utils'];
  
  try {
    for (const dir of searchDirs) {
      const dirPath = path.join(process.cwd(), dir);
      if (fs.existsSync(dirPath)) {
        const isUsed = searchInDirectory(dirPath, fileName);
        if (isUsed) {
          return true;
        }
      }
    }
    return false;
  } catch (error) {
    console.warn(`⚠️ 检查文件使用状态时出错: ${filePath}`, error.message);
    return true; // 出错时保守处理，认为文件在使用
  }
}

/**
 * 🔍 在目录中搜索文件引用
 */
function searchInDirectory(dirPath: string, fileName: string): boolean {
  try {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const fullPath = path.join(dirPath, file);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (searchInDirectory(fullPath, fileName)) {
          return true;
        }
      } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
        const content = fs.readFileSync(fullPath, 'utf-8');
        if (content.includes(fileName)) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * 📊 生成冗余文件报告
 */
function generateRedundancyReport(redundantFiles: RedundantFile[]): string {
  const totalFiles = redundantFiles.length;
  const unusedFiles = redundantFiles.filter(f => !f.isUsed);
  const totalSize = redundantFiles.reduce((sum, f) => sum + f.size, 0);
  const unusedSize = unusedFiles.reduce((sum, f) => sum + f.size, 0);
  
  const report = [
    '🔍 冗余文件检查报告',
    '='.repeat(40),
    `📊 总检查文件: ${totalFiles}个`,
    `🔴 未使用文件: ${unusedFiles.length}个`,
    `🟡 仍在使用文件: ${totalFiles - unusedFiles.length}个`,
    `💾 总文件大小: ${(totalSize / 1024).toFixed(1)}KB`,
    `🗑️ 可清理大小: ${(unusedSize / 1024).toFixed(1)}KB`,
    '',
    '📋 详细分析:',
    ''
  ];
  
  // 按使用状态分组
  const usedFiles = redundantFiles.filter(f => f.isUsed);
  const notUsedFiles = redundantFiles.filter(f => !f.isUsed);
  
  if (notUsedFiles.length > 0) {
    report.push('🔴 可以安全删除的文件:');
    notUsedFiles.forEach(file => {
      report.push(`  • ${file.path} (${(file.size / 1024).toFixed(1)}KB)`);
      report.push(`    理由: ${file.reason}`);
      report.push(`    替代: ${file.alternatives.join(', ')}`);
      report.push('');
    });
  }
  
  if (usedFiles.length > 0) {
    report.push('🟡 仍在使用但可能冗余的文件:');
    usedFiles.forEach(file => {
      report.push(`  • ${file.path} (${(file.size / 1024).toFixed(1)}KB)`);
      report.push(`    理由: ${file.reason}`);
      report.push(`    建议: 考虑迁移到 ${file.alternatives.join(' 或 ')}`);
      report.push('');
    });
  }
  
  report.push('💡 建议操作:');
  report.push('1. 先测试方案B的修复效果');
  report.push('2. 确认新的修复版组件工作正常');
  report.push('3. 逐步移除未使用的方案A文件');
  report.push('4. 更新文档以反映最新架构');
  
  return report.join('\n');
}

/**
 * 🎯 主函数
 */
function main() {
  try {
    const redundantFiles = checkRedundantFiles();
    const report = generateRedundancyReport(redundantFiles);
    
    console.log('\n' + report);
    
    // 保存报告到文件
    const reportPath = path.join(process.cwd(), 'docs', 'REDUNDANT_FILES_REPORT.md');
    fs.writeFileSync(reportPath, report);
    console.log(`\n📄 报告已保存到: ${reportPath}`);
    
    return {
      success: true,
      redundantFiles,
      report
    };
    
  } catch (error) {
    console.error('❌ 冗余文件检查失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行检查
if (require.main === module) {
  main();
}

export { checkRedundantFiles, generateRedundancyReport };
