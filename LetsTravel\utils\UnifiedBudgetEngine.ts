/**
 * 💰 统一预算引擎
 * 单一真实来源的预算计算，解决多重计算源不一致问题
 */

export interface UnifiedBudgetResult {
  total: number;
  byDay: { [day: number]: number };
  byCategory: { [category: string]: number };
  breakdown: BudgetBreakdownItem[];
  summary: BudgetSummary;
}

export interface BudgetBreakdownItem {
  activity: string;
  day: number;
  category: string;
  cost: number;
  currency: string;
  reasoning?: string;
}

export interface BudgetSummary {
  dailyAverage: number;
  highestDay: { day: number; amount: number };
  lowestDay: { day: number; amount: number };
  categoryDistribution: { [category: string]: number };
  recommendations: string[];
}

export class UnifiedBudgetEngine {
  
  // 🏷️ 统一的预算规则（单一真实来源）
  private static readonly UNIFIED_BUDGET_RULES = {
    attraction: { base: 25, max: 85, factor: 1.0 },
    meal: { base: 45, max: 125, factor: 1.2 },
    transport: { base: 12, max: 35, factor: 0.8 },
    accommodation: { base: 180, max: 420, factor: 1.5 },
    shopping: { base: 60, max: 220, factor: 1.3 },
    snack: { base: 20, max: 45, factor: 0.9 }
  };
  
  /**
   * 💵 统一预算计算 - 单一真实来源
   */
  static calculateUnifiedBudget(activities: any[]): UnifiedBudgetResult {
    console.log('💰 开始统一预算计算，活动数量:', activities.length);
    
    const budget: UnifiedBudgetResult = {
      total: 0,
      byDay: {},
      byCategory: {},
      breakdown: [],
      summary: {
        dailyAverage: 0,
        highestDay: { day: 1, amount: 0 },
        lowestDay: { day: 1, amount: 0 },
        categoryDistribution: {},
        recommendations: []
      }
    };
    
    // 计算每个活动的费用
    activities.forEach((activity, index) => {
      const cost = this.calculateSingleActivityCost(activity);
      const day = activity.day || 1;
      const category = this.getCostCategory(activity);
      
      // 统一累加到所有计算中
      budget.total += cost;
      budget.byDay[day] = (budget.byDay[day] || 0) + cost;
      budget.byCategory[category] = (budget.byCategory[category] || 0) + cost;
      
      budget.breakdown.push({
        activity: activity.name || activity.title || `活动${index + 1}`,
        day,
        category,
        cost,
        currency: 'MYR',
        reasoning: this.generateCostReasoning(activity, cost)
      });
    });
    
    // 生成预算摘要
    budget.summary = this.generateBudgetSummary(budget);
    
    console.log(`✅ 统一预算计算完成: 总计RM${budget.total}`);
    console.log('📊 每日分布:', Object.entries(budget.byDay).map(([day, amount]) => `Day${day}: RM${amount}`));
    
    return budget;
  }
  
  /**
   * 💵 计算单个活动费用 - 统一算法
   */
  private static calculateSingleActivityCost(activity: any): number {
    const category = this.getCostCategory(activity);
    const rule = this.UNIFIED_BUDGET_RULES[category] || this.UNIFIED_BUDGET_RULES.attraction;
    
    // 如果活动已有合理费用，使用现有费用
    let existingCost = this.extractExistingCost(activity);
    if (existingCost > 0 && existingCost <= rule.max) {
      return Math.round(existingCost);
    }
    
    // 如果现有费用过高，限制在最大值
    if (existingCost > rule.max) {
      console.log(`⚠️ 活动"${activity.name}"费用过高(${existingCost})，限制为${rule.max}`);
      return rule.max;
    }
    
    // 基于活动特征和规则计算费用
    return this.calculateCostByFeatures(activity, rule);
  }
  
  /**
   * 💰 提取现有费用
   */
  private static extractExistingCost(activity: any): number {
    if (typeof activity.cost === 'number') {
      return activity.cost;
    }
    
    if (activity.cost && typeof activity.cost === 'object') {
      if (activity.cost.amount !== undefined) {
        return activity.cost.amount;
      }
    }
    
    return 0;
  }
  
  /**
   * 🎯 基于活动特征计算费用
   */
  private static calculateCostByFeatures(activity: any, rule: any): number {
    const name = (activity.name || activity.title || '').toLowerCase();
    let cost = rule.base;
    
    // 根据活动名称特征调整费用
    if (name.includes('免费') || name.includes('公园') || name.includes('寺庙')) {
      cost = 0;
    } else if (name.includes('高级') || name.includes('精选') || name.includes('次郎')) {
      cost = rule.max * 0.85;
    } else if (name.includes('经济') || name.includes('便宜') || name.includes('街边')) {
      cost = rule.base * 0.7;
    } else if (name.includes('人气') || name.includes('推荐')) {
      cost = rule.base * 1.2;
    }
    
    // 根据活动持续时间调整
    const duration = activity.duration || 90;
    if (duration > 150) { // 超过2.5小时
      cost *= 1.2;
    } else if (duration < 60) { // 少于1小时
      cost *= 0.8;
    }
    
    // 添加小幅随机变化使费用更自然
    cost *= (0.9 + Math.random() * 0.2); // ±10%变化
    
    return Math.round(Math.min(cost, rule.max));
  }
  
  /**
   * 🏷️ 获取费用分类
   */
  private static getCostCategory(activity: any): string {
    if (activity.type) {
      return activity.type;
    }
    
    const name = (activity.name || activity.title || '').toLowerCase();
    
    // 住宿
    if (name.includes('酒店') || name.includes('hotel') || name.includes('入住')) {
      return 'accommodation';
    }
    
    // 餐饮
    if (name.includes('餐') || name.includes('食') || name.includes('寿司') || 
        name.includes('拉面') || name.includes('料理') || name.includes('咖啡')) {
      return 'meal';
    }
    
    // 小食
    if (name.includes('小食') || name.includes('甜品') || name.includes('茶点')) {
      return 'snack';
    }
    
    // 交通
    if (name.includes('地铁') || name.includes('巴士') || name.includes('taxi') || 
        name.includes('前往') || name.includes('交通')) {
      return 'transport';
    }
    
    // 购物
    if (name.includes('购物') || name.includes('商店') || name.includes('市场')) {
      return 'shopping';
    }
    
    // 默认为景点
    return 'attraction';
  }
  
  /**
   * 📝 生成费用推理说明
   */
  private static generateCostReasoning(activity: any, cost: number): string {
    const category = this.getCostCategory(activity);
    const name = activity.name || activity.title || '活动';
    
    if (cost === 0) {
      return `${name}为免费体验`;
    }
    
    if (category === 'meal') {
      if (cost > 80) return `${name}为精选餐饮体验`;
      if (cost < 30) return `${name}为经济实惠选择`;
      return `${name}为当地特色美食`;
    }
    
    if (category === 'attraction') {
      if (cost > 60) return `${name}为热门付费景点`;
      if (cost < 20) return `${name}为超值体验`;
      return `${name}门票费用合理`;
    }
    
    if (category === 'transport') {
      return `${name}交通费用`;
    }
    
    return `${name}体验费用`;
  }
  
  /**
   * 📊 生成预算摘要
   */
  private static generateBudgetSummary(budget: UnifiedBudgetResult): BudgetSummary {
    const days = Object.keys(budget.byDay).map(Number);
    const dayAmounts = Object.values(budget.byDay);
    
    // 找出最高和最低消费日
    const highestAmount = Math.max(...dayAmounts);
    const lowestAmount = Math.min(...dayAmounts);
    const highestDay = days.find(day => budget.byDay[day] === highestAmount) || 1;
    const lowestDay = days.find(day => budget.byDay[day] === lowestAmount) || 1;
    
    // 计算分类占比
    const categoryDistribution: { [category: string]: number } = {};
    Object.entries(budget.byCategory).forEach(([category, amount]) => {
      categoryDistribution[category] = Math.round((amount / budget.total) * 100);
    });
    
    // 生成建议
    const recommendations = this.generateBudgetRecommendations(budget);
    
    return {
      dailyAverage: Math.round(budget.total / days.length),
      highestDay: { day: highestDay, amount: highestAmount },
      lowestDay: { day: lowestDay, amount: lowestAmount },
      categoryDistribution,
      recommendations
    };
  }
  
  /**
   * 💡 生成预算建议
   */
  private static generateBudgetRecommendations(budget: UnifiedBudgetResult): string[] {
    const recommendations: string[] = [];
    const total = budget.total;
    const categories = budget.byCategory;
    
    // 餐饮费用建议
    const mealPercentage = ((categories.meal || 0) / total) * 100;
    if (mealPercentage > 45) {
      recommendations.push('🍽️ 餐饮费用较高，可考虑尝试更多当地平价美食');
    } else if (mealPercentage < 25) {
      recommendations.push('🍽️ 可以适当增加特色餐饮体验');
    }
    
    // 交通费用建议
    const transportPercentage = ((categories.transport || 0) / total) * 100;
    if (transportPercentage > 20) {
      recommendations.push('🚇 建议购买交通一日券以节省费用');
    }
    
    // 景点费用建议
    const attractionPercentage = ((categories.attraction || 0) / total) * 100;
    if (attractionPercentage < 15) {
      recommendations.push('🏛️ 可以考虑增加一些付费景点体验');
    }
    
    // 总预算建议
    if (total < 800) {
      recommendations.push('💰 预算较为经济，适合深度体验免费景点');
    } else if (total > 2000) {
      recommendations.push('💰 预算充足，可以享受更多优质体验');
    }
    
    return recommendations;
  }
  
  /**
   * 📊 获取情感化预算描述
   */
  static getEmotionalBudgetText(amount: number): string {
    if (amount === 0) return '免费体验';
    if (amount < 25) return '超值选择';
    if (amount < 60) return '合理消费';
    if (amount < 100) return '精选体验';
    return '优质享受';
  }
  
  /**
   * 📈 获取预算范围显示
   */
  static getBudgetRangeText(dayBudget: number): string {
    const variance = dayBudget * 0.1; // ±10%变化
    const min = Math.round(dayBudget - variance);
    const max = Math.round(dayBudget + variance);
    
    if (min === max) return `RM${dayBudget}`;
    return `约RM${min}-${max}`;
  }
}
