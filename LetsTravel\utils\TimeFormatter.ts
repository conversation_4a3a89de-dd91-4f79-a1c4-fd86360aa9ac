/**
 * 🔧 时间格式化工具类
 * 统一处理所有时间显示格式，解决"时间待定"问题
 */

export interface TimeFormatOptions {
  showSeconds?: boolean;
  use12Hour?: boolean;
  showDate?: boolean;
  locale?: string;
}

export class TimeFormatter {
  private static readonly DEFAULT_OPTIONS: TimeFormatOptions = {
    showSeconds: false,
    use12Hour: false,
    showDate: false,
    locale: 'zh-CN'
  };

  /**
   * 🔧 格式化活动时间范围
   */
  static formatActivityTimeRange(startTime: any, endTime?: any, options?: TimeFormatOptions): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    
    if (!startTime) {
      return '时间待定';
    }

    try {
      const start = this.parseTime(startTime);
      if (!start) {
        return '时间待定';
      }

      const startStr = this.formatSingleTime(start, opts);
      
      if (endTime) {
        const end = this.parseTime(endTime);
        if (end) {
          const endStr = this.formatSingleTime(end, opts);
          return `${startStr}-${endStr}`;
        }
      }

      return startStr;
    } catch (error) {
      console.warn('⚠️ 时间格式化失败:', { startTime, endTime, error });
      return '时间待定';
    }
  }

  /**
   * 🔧 格式化单个时间 - 修复版，确保统一格式
   */
  static formatSingleTime(time: any, options?: TimeFormatOptions): string {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };

    try {
      const date = this.parseTime(time);
      if (!date) {
        return '时间待定';
      }

      // 使用标准格式，确保一致性
      const hours = date.getHours();
      const minutes = date.getMinutes();

      // 格式化为两位数
      const hoursStr = hours.toString().padStart(2, '0');
      const minutesStr = minutes.toString().padStart(2, '0');

      let result = `${hoursStr}:${minutesStr}`;

      // 如果需要显示秒
      if (opts.showSeconds) {
        const seconds = date.getSeconds();
        const secondsStr = seconds.toString().padStart(2, '0');
        result += `:${secondsStr}`;
      }

      // 如果需要显示日期
      if (opts.showDate) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        result = `${year}-${month}-${day} ${result}`;
      }

      // 如果需要12小时制
      if (opts.use12Hour) {
        const period = hours >= 12 ? 'PM' : 'AM';
        const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        const displayHoursStr = displayHours.toString().padStart(2, '0');
        result = `${displayHoursStr}:${minutesStr} ${period}`;
      }

      return result;
    } catch (error) {
      console.warn('⚠️ 单个时间格式化失败:', { time, error });
      return '时间待定';
    }
  }

  /**
   * 🔧 格式化多个时间段 - 新增功能
   * 支持格式如: "09:00-11:15, 11:30-12:30"
   */
  static formatMultipleTimeRanges(timeRanges: Array<{startTime: any, endTime: any}>, options?: TimeFormatOptions): string {
    if (!timeRanges || timeRanges.length === 0) {
      return '时间待定';
    }

    try {
      const formattedRanges = timeRanges
        .map(range => this.formatActivityTimeRange(range.startTime, range.endTime, options))
        .filter(range => range !== '时间待定');

      if (formattedRanges.length === 0) {
        return '时间待定';
      }

      return formattedRanges.join(', ');
    } catch (error) {
      console.warn('⚠️ 多时间段格式化失败:', { timeRanges, error });
      return '时间待定';
    }
  }

  /**
   * 🔧 格式化日程时间线
   * 将一天的活动时间格式化为连续的时间线
   */
  static formatDayTimeline(activities: Array<{startTime: any, endTime: any, name: string}>, options?: TimeFormatOptions): string {
    if (!activities || activities.length === 0) {
      return '暂无安排';
    }

    try {
      const timelineItems = activities
        .map(activity => {
          const timeRange = this.formatActivityTimeRange(activity.startTime, activity.endTime, options);
          return timeRange !== '时间待定' ? `${timeRange} ${activity.name}` : null;
        })
        .filter(item => item !== null);

      return timelineItems.join(' | ') || '时间待定';
    } catch (error) {
      console.warn('⚠️ 日程时间线格式化失败:', { activities, error });
      return '时间待定';
    }
  }

  /**
   * 🔧 解析各种时间格式
   */
  private static parseTime(timeValue: any): Date | null {
    if (!timeValue) {
      return null;
    }

    try {
      // 如果已经是Date对象
      if (timeValue instanceof Date) {
        return isNaN(timeValue.getTime()) ? null : timeValue;
      }

      // 如果是字符串
      if (typeof timeValue === 'string') {
        // 处理简单时间格式（如 "09:00", "14:30"）
        const simpleTimeMatch = timeValue.match(/^(\d{1,2}):(\d{2})$/);
        if (simpleTimeMatch) {
          const [, hours, minutes] = simpleTimeMatch;
          const date = new Date();
          date.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);
          return date;
        }

        // 处理带AM/PM的时间格式
        const ampmTimeMatch = timeValue.match(/^(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)$/i);
        if (ampmTimeMatch) {
          const [, hours, minutes, period] = ampmTimeMatch;
          let hour24 = parseInt(hours, 10);
          
          if (period.toLowerCase() === 'pm' && hour24 !== 12) {
            hour24 += 12;
          } else if (period.toLowerCase() === 'am' && hour24 === 12) {
            hour24 = 0;
          }
          
          const date = new Date();
          date.setHours(hour24, parseInt(minutes, 10), 0, 0);
          return date;
        }

        // 尝试直接解析ISO字符串或其他格式
        const parsedDate = new Date(timeValue);
        return isNaN(parsedDate.getTime()) ? null : parsedDate;
      }

      // 如果是数字（时间戳）
      if (typeof timeValue === 'number') {
        const date = new Date(timeValue);
        return isNaN(date.getTime()) ? null : date;
      }

      return null;
    } catch (error) {
      console.warn('⚠️ 时间解析失败:', timeValue, error);
      return null;
    }
  }

  /**
   * 🔧 验证时间格式是否有效
   */
  static isValidTime(timeValue: any): boolean {
    return this.parseTime(timeValue) !== null;
  }

  /**
   * 🔧 获取时间段描述
   */
  static getTimePeriod(timeValue: any): string {
    const date = this.parseTime(timeValue);
    if (!date) {
      return '未知时段';
    }

    const hour = date.getHours();
    
    if (hour >= 6 && hour < 12) {
      return '上午';
    } else if (hour >= 12 && hour < 14) {
      return '中午';
    } else if (hour >= 14 && hour < 18) {
      return '下午';
    } else if (hour >= 18 && hour < 22) {
      return '晚上';
    } else {
      return '深夜';
    }
  }

  /**
   * 🔧 计算时间差（分钟）
   */
  static getTimeDifferenceInMinutes(startTime: any, endTime: any): number {
    const start = this.parseTime(startTime);
    const end = this.parseTime(endTime);
    
    if (!start || !end) {
      return 0;
    }

    return Math.round((end.getTime() - start.getTime()) / (1000 * 60));
  }

  /**
   * 🔧 生成时间范围描述
   */
  static getTimeRangeDescription(startTime: any, endTime?: any): string {
    const start = this.parseTime(startTime);
    if (!start) {
      return '时间待定';
    }

    const startPeriod = this.getTimePeriod(start);
    
    if (endTime) {
      const end = this.parseTime(endTime);
      if (end) {
        const endPeriod = this.getTimePeriod(end);
        const duration = this.getTimeDifferenceInMinutes(start, end);
        
        if (startPeriod === endPeriod) {
          return `${startPeriod}活动 (${duration}分钟)`;
        } else {
          return `${startPeriod}至${endPeriod}活动 (${duration}分钟)`;
        }
      }
    }

    return `${startPeriod}活动`;
  }

  /**
   * 🔧 转换为ISO字符串
   */
  static toISOString(timeValue: any): string {
    const date = this.parseTime(timeValue);
    return date ? date.toISOString() : '';
  }

  /**
   * 🔧 转换为简单时间格式（HH:MM）
   */
  static toSimpleTimeString(timeValue: any): string {
    const date = this.parseTime(timeValue);
    if (!date) {
      return '';
    }

    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  /**
   * 🔧 检查时间是否在合理范围内
   */
  static isReasonableTime(timeValue: any): boolean {
    const date = this.parseTime(timeValue);
    if (!date) {
      return false;
    }

    const hour = date.getHours();
    // 合理的活动时间：6:00 - 23:59
    return hour >= 6 && hour <= 23;
  }

  /**
   * 🔧 格式化持续时间
   */
  static formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}分钟`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours}小时`;
    }

    return `${hours}小时${remainingMinutes}分钟`;
  }
}

// 🔧 便捷函数导出
export const formatActivityTime = (startTime: any, endTime?: any) => 
  TimeFormatter.formatActivityTimeRange(startTime, endTime);

export const formatSingleTime = (time: any) => 
  TimeFormatter.formatSingleTime(time);

export const isValidTime = (time: any) => 
  TimeFormatter.isValidTime(time);

export const getTimePeriod = (time: any) => 
  TimeFormatter.getTimePeriod(time);
