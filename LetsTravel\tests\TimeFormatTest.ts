/**
 * ⏰ 时间格式测试脚本
 * 验证TimeFormatter和TimeFormatValidator的功能
 */

import { TimeFormatter } from '../utils/TimeFormatter';
import { TimeFormatValidator } from '../utils/TimeFormatValidator';

// 测试函数
function runTimeFormatTests() {
  console.log('🧪 开始时间格式测试...\n');

  // 1. 运行基础格式测试
  console.log('📋 基础格式测试:');
  const testResults = TimeFormatValidator.runTimeFormatTests();
  
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`📊 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%\n`);

  // 2. 显示失败的测试用例
  if (testResults.failed > 0) {
    console.log('❌ 失败的测试用例:');
    testResults.results
      .filter(r => !r.passed)
      .forEach((r, index) => {
        console.log(`${index + 1}. ${r.testCase.description}`);
        console.log(`   输入: ${JSON.stringify(r.testCase.input)}`);
        console.log(`   期望: "${r.testCase.expected}"`);
        console.log(`   实际: "${r.result}"`);
        console.log(`   问题: ${r.validation.issues.join(', ')}\n`);
      });
  }

  // 3. 测试Ultra Think数据格式
  console.log('🎯 Ultra Think数据格式测试:');
  const ultraThinkTestData = [
    {
      id: 'test1',
      name: '测试活动1',
      startTime: '09:00',
      endTime: '11:15',
      timing: {
        startTime: '09:00',
        endTime: '11:15'
      }
    },
    {
      id: 'test2',
      name: '测试活动2',
      startTime: '9:00', // 单位数小时
      endTime: '11:15',
      timing: {
        startTime: '9:00',
        endTime: '11:15'
      }
    },
    {
      id: 'test3',
      name: '测试活动3',
      startTime: null,
      endTime: null,
      timing: {
        startTime: null,
        endTime: null
      }
    }
  ];

  const ultraThinkValidation = TimeFormatValidator.validateUltraThinkActivityTimes(ultraThinkTestData);
  console.log(`✅ 有效时间: ${ultraThinkValidation.validCount}`);
  console.log(`❌ 无效时间: ${ultraThinkValidation.invalidCount}`);
  
  if (ultraThinkValidation.issues.length > 0) {
    console.log('\n⚠️ 时间格式问题:');
    ultraThinkValidation.issues.forEach(issue => {
      console.log(`  • ${issue.activityName}: ${issue.timeIssues.join(', ')}`);
    });
  }

  // 4. 生成完整报告
  console.log('\n📊 完整验证报告:');
  console.log(TimeFormatValidator.generateValidationReport());

  // 5. 测试时间格式修复功能
  console.log('\n🔧 时间格式修复测试:');
  const fixTestCases = [
    '9:00-11:15',    // 单位数小时
    '09:00:30-11:15:45', // 带秒
    '9.00-11.15',    // 点号分隔
    '9:00 AM-11:15 AM', // 12小时制
    'invalid-time'   // 无效格式
  ];

  fixTestCases.forEach(testCase => {
    const fixed = TimeFormatValidator.fixTimeFormat(testCase);
    const validation = TimeFormatValidator.validateTimeFormat(fixed);
    console.log(`  "${testCase}" -> "${fixed}" (${validation.isValid ? '✅' : '❌'})`);
  });
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTimeFormatTests();
}

export { runTimeFormatTests };
