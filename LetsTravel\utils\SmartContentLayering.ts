/**
 * 🎨 智能内容分层器
 * 智能决定什么内容在什么层级显示，解决信息密度和认知负担问题
 */

export interface LayeredContent {
  primaryActivities: LayeredActivity[];    // 主时间线显示
  secondaryActivities: LayeredActivity[];  // 展开时显示
  hiddenActivities: LayeredActivity[];     // 完全隐藏或合并
}

export interface LayeredActivity {
  id: string;
  name: string;
  type: string;
  startTime: string;
  endTime: string;
  cost: number;
  displayPriority: number;
  layerReason: string;
  icon: string;
  isConnector?: boolean; // 是否为连接性活动（如交通）
}

export interface DisplayContext {
  totalActivities: number;
  dayNumber: number;
  userPreference: 'simple' | 'detailed';
  screenSize: 'small' | 'medium' | 'large';
}

export class SmartContentLayering {
  
  /**
   * 📱 智能内容分层主方法
   */
  static layerContent(activities: any[], context: DisplayContext): LayeredContent {
    console.log(`🎨 开始智能内容分层，活动数量: ${activities.length}`);
    
    // 1. 计算每个活动的显示优先级
    const prioritizedActivities = this.calculateDisplayPriorities(activities, context);
    
    // 2. 根据优先级和类型分层
    const layered = this.distributeToLayers(prioritizedActivities, context);
    
    // 3. 优化分层结果
    const optimized = this.optimizeLayering(layered, context);
    
    console.log(`✅ 内容分层完成:`);
    console.log(`  📱 主要显示: ${optimized.primaryActivities.length}个`);
    console.log(`  🔽 展开显示: ${optimized.secondaryActivities.length}个`);
    console.log(`  👻 隐藏活动: ${optimized.hiddenActivities.length}个`);
    
    return optimized;
  }
  
  /**
   * 📊 计算显示优先级
   */
  private static calculateDisplayPriorities(activities: any[], context: DisplayContext): LayeredActivity[] {
    return activities.map(activity => {
      const priority = this.calculateSingleActivityPriority(activity, context);
      const layerReason = this.generateLayerReason(activity, priority);
      
      return {
        id: activity.id || `activity_${Date.now()}_${Math.random()}`,
        name: activity.name || activity.title || '未命名活动',
        type: this.detectActivityType(activity),
        startTime: activity.startTime || '09:00',
        endTime: activity.endTime || '10:00',
        cost: activity.cost || 0,
        displayPriority: priority,
        layerReason,
        icon: this.getActivityIcon(activity),
        isConnector: this.isConnectorActivity(activity)
      };
    });
  }
  
  /**
   * 🎯 计算单个活动的显示优先级
   */
  private static calculateSingleActivityPriority(activity: any, context: DisplayContext): number {
    let priority = 5; // 基础优先级
    
    const type = this.detectActivityType(activity);
    const name = (activity.name || activity.title || '').toLowerCase();
    
    // 🏷️ 类型优先级
    const typePriorities = {
      accommodation: 8,  // 住宿最重要
      meal: 7,          // 餐饮很重要
      attraction: 6,    // 景点重要
      shopping: 5,      // 购物一般
      transport: 2,     // 交通优先级低
      snack: 4          // 小食优先级中等
    };
    
    priority = typePriorities[type] || 5;
    
    // 🌟 内容质量调整
    if (name.includes('免费') || name.includes('公园')) {
      priority += 1; // 免费景点用户喜欢
    }
    
    if (name.includes('高级') || name.includes('精选') || name.includes('次郎')) {
      priority += 2; // 高质量体验
    }
    
    if (name.includes('人气') || name.includes('推荐')) {
      priority += 1; // 受欢迎的地点
    }
    
    // ⏰ 时间长度调整
    const duration = activity.duration || 90;
    if (duration > 120) {
      priority += 1; // 长时间活动更重要
    } else if (duration < 30) {
      priority -= 1; // 短时间活动不太重要
    }
    
    // 📱 用户偏好调整
    if (context.userPreference === 'simple') {
      if (type === 'transport') priority -= 2; // 简单模式下交通更不重要
    }
    
    // 📊 活动密度调整
    if (context.totalActivities > 8) {
      if (type === 'transport' || type === 'snack') {
        priority -= 1; // 活动多时降低次要活动优先级
      }
    }
    
    return Math.max(1, Math.min(10, priority));
  }
  
  /**
   * 📱 分配到不同层级
   */
  private static distributeToLayers(activities: LayeredActivity[], context: DisplayContext): LayeredContent {
    // 按优先级排序
    const sorted = [...activities].sort((a, b) => b.displayPriority - a.displayPriority);
    
    const layered: LayeredContent = {
      primaryActivities: [],
      secondaryActivities: [],
      hiddenActivities: []
    };
    
    sorted.forEach(activity => {
      if (this.shouldShowInPrimary(activity, context)) {
        layered.primaryActivities.push(activity);
      } else if (this.shouldShowInSecondary(activity, context)) {
        layered.secondaryActivities.push(activity);
      } else {
        layered.hiddenActivities.push(activity);
      }
    });
    
    return layered;
  }
  
  /**
   * 📱 判断是否应在主时间线显示
   */
  private static shouldShowInPrimary(activity: LayeredActivity, context: DisplayContext): boolean {
    // 高优先级活动
    if (activity.displayPriority >= 6) return true;
    
    // 重要类型
    if (['accommodation', 'meal', 'attraction'].includes(activity.type)) {
      return true;
    }
    
    // 交通活动通常不在主时间线
    if (activity.type === 'transport') return false;
    
    // 如果主时间线活动太少，适当放宽标准
    return activity.displayPriority >= 4;
  }
  
  /**
   * 🔽 判断是否应在展开时显示
   */
  private static shouldShowInSecondary(activity: LayeredActivity, context: DisplayContext): boolean {
    // 交通活动主要在展开时显示
    if (activity.type === 'transport') return true;
    
    // 小食和购物在展开时显示
    if (['snack', 'shopping'].includes(activity.type)) return true;
    
    // 中等优先级活动
    if (activity.displayPriority >= 3) return true;
    
    return false;
  }
  
  /**
   * 🎨 优化分层结果
   */
  private static optimizeLayering(layered: LayeredContent, context: DisplayContext): LayeredContent {
    // 确保主时间线不会太空
    if (layered.primaryActivities.length < 3 && layered.secondaryActivities.length > 0) {
      const promoted = layered.secondaryActivities.splice(0, 1);
      layered.primaryActivities.push(...promoted);
    }
    
    // 确保主时间线不会太满
    if (layered.primaryActivities.length > 6) {
      const demoted = layered.primaryActivities.splice(6);
      layered.secondaryActivities.unshift(...demoted);
    }
    
    // 按时间排序
    layered.primaryActivities.sort((a, b) => this.timeToMinutes(a.startTime) - this.timeToMinutes(b.startTime));
    layered.secondaryActivities.sort((a, b) => this.timeToMinutes(a.startTime) - this.timeToMinutes(b.startTime));
    
    return layered;
  }
  
  /**
   * 📝 生成分层原因说明
   */
  private static generateLayerReason(activity: LayeredActivity, priority: number): string {
    const type = activity.type;
    const name = activity.name;
    
    if (priority >= 8) {
      return `${name}为核心体验，优先显示`;
    } else if (priority >= 6) {
      return `${name}为重要活动，主时间线显示`;
    } else if (priority >= 4) {
      return `${name}为辅助信息，展开时显示`;
    } else if (type === 'transport') {
      return `${name}为交通信息，展开时显示`;
    } else {
      return `${name}为次要活动，可选显示`;
    }
  }
  
  /**
   * 🏷️ 检测活动类型
   */
  private static detectActivityType(activity: any): string {
    if (activity.type) return activity.type;
    
    const name = (activity.name || activity.title || '').toLowerCase();
    
    if (name.includes('酒店') || name.includes('入住')) return 'accommodation';
    if (name.includes('餐') || name.includes('寿司') || name.includes('料理')) return 'meal';
    if (name.includes('小食') || name.includes('茶点') || name.includes('咖啡')) return 'snack';
    if (name.includes('地铁') || name.includes('前往') || name.includes('交通')) return 'transport';
    if (name.includes('购物') || name.includes('商店')) return 'shopping';
    
    return 'attraction';
  }
  
  /**
   * 🎨 获取活动图标
   */
  private static getActivityIcon(activity: any): string {
    const type = this.detectActivityType(activity);
    
    const icons = {
      accommodation: '🏨',
      meal: '🍽️',
      snack: '☕',
      transport: '🚇',
      shopping: '🛍️',
      attraction: '🏛️'
    };
    
    return icons[type] || '📍';
  }
  
  /**
   * 🔗 判断是否为连接性活动
   */
  private static isConnectorActivity(activity: any): boolean {
    const type = this.detectActivityType(activity);
    const name = (activity.name || activity.title || '').toLowerCase();
    
    return type === 'transport' || 
           name.includes('前往') || 
           name.includes('到达') || 
           name.includes('离开');
  }
  
  /**
   * 🔢 时间转换辅助方法
   */
  private static timeToMinutes(time: string): number {
    if (!time) return 0;
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
}
