/**
 * 🏷️ 智能活动分类器
 * 
 * 解决以下问题：
 * 1. 美食活动显示景点图标
 * 2. 活动类型识别不准确
 * 3. 图标映射错误
 * 4. 缺乏多维度特征识别
 * 
 * <AUTHOR> Think System
 * @version 2.0.0
 * @created 2025-01-30
 */

export interface ActivityClassificationRequest {
  id: string;
  name: string;
  name_zh?: string;
  type?: string;
  category?: string;
  description?: string;
  description_zh?: string;
  location?: {
    name: string;
    address: string;
  };
  details?: any;
  metadata?: any;
}

export interface ClassificationResult {
  primaryType: 'attraction' | 'meal' | 'transport' | 'accommodation' | 'shopping' | 'cultural' | 'entertainment';
  subCategory: string;
  confidence: number; // 0-1
  iconType: 'attraction' | 'restaurant' | 'transport' | 'hotel' | 'shopping' | 'cultural' | 'entertainment';
  reasoning: string[];
  alternativeTypes: Array<{
    type: string;
    confidence: number;
    reason: string;
  }>;
  tags: string[];
}

/**
 * 智能活动分类器
 * 
 * 核心特性：
 * 1. 多维度分析：基于名称、描述、位置等多个维度
 * 2. 语义理解：理解中英文语义特征
 * 3. 准确映射：确保类型与图标的正确对应
 * 4. 置信度评估：提供分类的可信度评分
 */
export class IntelligentActivityClassifier {
  
  // 活动类型关键词库
  private static readonly TYPE_KEYWORDS = {
    meal: {
      primary: [
        // 中文关键词
        '餐厅', '饭店', '酒楼', '食堂', '小吃', '美食', '料理', '菜馆',
        '火锅', '烧烤', '自助餐', '快餐', '茶餐厅', '咖啡厅', '酒吧',
        '早餐', '午餐', '晚餐', '夜宵', '下午茶', '甜品', '糕点',
        '拉面', '寿司', '刺身', '天妇罗', '烤肉', '涮锅', '粥品',
        // 英文关键词
        'restaurant', 'cafe', 'bar', 'bistro', 'diner', 'eatery',
        'food', 'cuisine', 'dining', 'meal', 'breakfast', 'lunch', 'dinner',
        'ramen', 'sushi', 'bbq', 'grill', 'buffet', 'fast food',
        'coffee', 'tea', 'dessert', 'bakery', 'pastry'
      ],
      brands: [
        '一兰', '吉野家', '松屋', '麦当劳', '肯德基', '星巴克',
        'ichiran', 'yoshinoya', 'matsuya', 'mcdonalds', 'kfc', 'starbucks'
      ],
      locations: [
        '美食街', '食品市场', '餐饮区', 'food court', 'food market'
      ]
    },
    
    attraction: {
      primary: [
        // 中文关键词
        '景点', '名胜', '古迹', '遗址', '建筑', '塔', '桥', '门',
        '公园', '花园', '广场', '街道', '步行街', '观景台', '瞭望台',
        '寺庙', '神社', '教堂', '宫殿', '城堡', '要塞',
        '山', '湖', '海', '河', '瀑布', '温泉', '海滩',
        // 英文关键词
        'attraction', 'landmark', 'monument', 'heritage', 'site',
        'temple', 'shrine', 'church', 'palace', 'castle', 'fort',
        'park', 'garden', 'square', 'street', 'viewpoint', 'observatory',
        'mountain', 'lake', 'sea', 'river', 'waterfall', 'beach', 'hot spring'
      ],
      specific: [
        '浅草寺', '涩谷十字路口', '东京塔', '富士山', '金阁寺',
        '天安门', '故宫', '长城', '兵马俑', '西湖',
        'sensoji', 'shibuya crossing', 'tokyo tower', 'mount fuji',
        'golden pavilion', 'forbidden city', 'great wall'
      ]
    },
    
    cultural: {
      primary: [
        // 中文关键词
        '博物馆', '美术馆', '艺术馆', '展览馆', '文化中心', '图书馆',
        '剧院', '音乐厅', '歌剧院', '演出', '表演', '展览', '艺术',
        '历史', '文化', '传统', '民俗', '工艺', '手工艺',
        // 英文关键词
        'museum', 'gallery', 'art', 'exhibition', 'cultural center',
        'theater', 'theatre', 'opera', 'concert', 'performance',
        'history', 'culture', 'traditional', 'craft', 'heritage'
      ]
    },
    
    entertainment: {
      primary: [
        // 中文关键词
        '游乐园', '主题公园', '水族馆', '动物园', '马戏团',
        '电影院', '游戏厅', 'KTV', '卡拉OK', '夜店', '酒吧',
        '温泉', 'SPA', '按摩', '娱乐', '休闲', '放松',
        // 英文关键词
        'amusement park', 'theme park', 'aquarium', 'zoo', 'circus',
        'cinema', 'movie', 'karaoke', 'spa', 'massage', 'entertainment'
      ]
    },
    
    shopping: {
      primary: [
        // 中文关键词
        '商场', '购物中心', '百货', '超市', '市场', '商店', '专卖店',
        '奥特莱斯', '免税店', '纪念品', '土特产', '购物', '买',
        // 英文关键词
        'mall', 'shopping', 'store', 'shop', 'market', 'outlet',
        'duty free', 'souvenir', 'buy', 'purchase'
      ]
    },
    
    transport: {
      primary: [
        // 中文关键词
        '交通', '出行', '前往', '到达', '乘坐', '搭乘',
        '地铁', '公交', '巴士', '出租车', '的士', '步行', '徒步',
        '火车', '高铁', '飞机', '航班', '船', '轮渡',
        // 英文关键词
        'transport', 'travel', 'go to', 'take', 'ride',
        'subway', 'metro', 'bus', 'taxi', 'walk', 'walking',
        'train', 'flight', 'plane', 'ferry', 'boat'
      ]
    },
    
    accommodation: {
      primary: [
        // 中文关键词
        '酒店', '宾馆', '旅馆', '民宿', '青年旅社', '度假村',
        '入住', '退房', '住宿', '过夜',
        // 英文关键词
        'hotel', 'inn', 'hostel', 'resort', 'accommodation',
        'check in', 'check out', 'stay', 'overnight'
      ]
    }
  };

  // 图标映射规则
  private static readonly ICON_MAPPING = {
    meal: 'restaurant',
    attraction: 'attraction',
    cultural: 'cultural',
    entertainment: 'entertainment',
    shopping: 'shopping',
    transport: 'transport',
    accommodation: 'hotel'
  };

  /**
   * 🏷️ 智能分类活动
   */
  static classifyActivity(request: ActivityClassificationRequest): ClassificationResult {
    console.log(`🏷️ 开始智能分类活动: ${request.name}`);
    
    // 1. 收集所有文本内容
    const textContent = this.extractTextContent(request);
    
    // 2. 多维度分析
    const typeScores = this.analyzeMultipleDimensions(textContent, request);
    
    // 3. 确定主要类型
    const primaryType = this.determinePrimaryType(typeScores);
    
    // 4. 生成子分类
    const subCategory = this.generateSubCategory(primaryType, textContent);
    
    // 5. 计算置信度
    const confidence = this.calculateConfidence(typeScores, primaryType);
    
    // 6. 确定图标类型
    const iconType = this.ICON_MAPPING[primaryType] || 'attraction';
    
    // 7. 生成推理过程
    const reasoning = this.generateReasoning(textContent, primaryType, typeScores);
    
    // 8. 生成备选类型
    const alternativeTypes = this.generateAlternativeTypes(typeScores, primaryType);
    
    // 9. 生成标签
    const tags = this.generateTags(textContent, primaryType);
    
    const result: ClassificationResult = {
      primaryType,
      subCategory,
      confidence,
      iconType,
      reasoning,
      alternativeTypes,
      tags
    };
    
    console.log(`✅ 分类完成: ${primaryType} (${subCategory}) - 置信度${(confidence * 100).toFixed(1)}%`);
    console.log(`🎯 图标类型: ${iconType}`);
    
    return result;
  }

  /**
   * 📝 提取文本内容
   */
  private static extractTextContent(request: ActivityClassificationRequest): {
    name: string;
    description: string;
    location: string;
    combined: string;
  } {
    const name = `${request.name || ''} ${request.name_zh || ''}`.toLowerCase().trim();
    const description = `${request.description || ''} ${request.description_zh || ''}`.toLowerCase().trim();
    const location = `${request.location?.name || ''} ${request.location?.address || ''}`.toLowerCase().trim();
    const combined = `${name} ${description} ${location}`.trim();
    
    return { name, description, location, combined };
  }

  /**
   * 🔍 多维度分析
   */
  private static analyzeMultipleDimensions(
    textContent: any,
    request: ActivityClassificationRequest
  ): Record<string, number> {
    const scores: Record<string, number> = {
      meal: 0,
      attraction: 0,
      cultural: 0,
      entertainment: 0,
      shopping: 0,
      transport: 0,
      accommodation: 0
    };
    
    // 1. 基于关键词的分析
    Object.entries(this.TYPE_KEYWORDS).forEach(([type, keywords]) => {
      // 主要关键词
      keywords.primary.forEach(keyword => {
        if (textContent.combined.includes(keyword)) {
          scores[type] += 10;
          
          // 名称中的关键词权重更高
          if (textContent.name.includes(keyword)) {
            scores[type] += 15;
          }
        }
      });
      
      // 品牌关键词
      if (keywords.brands) {
        keywords.brands.forEach(brand => {
          if (textContent.combined.includes(brand)) {
            scores[type] += 20; // 品牌识别权重很高
          }
        });
      }
      
      // 位置关键词
      if (keywords.locations) {
        keywords.locations.forEach(location => {
          if (textContent.location.includes(location)) {
            scores[type] += 8;
          }
        });
      }
      
      // 特定景点
      if (keywords.specific) {
        keywords.specific.forEach(specific => {
          if (textContent.combined.includes(specific)) {
            scores[type] += 25; // 特定识别权重最高
          }
        });
      }
    });
    
    // 2. 基于现有类型的分析
    if (request.type) {
      const existingType = request.type.toLowerCase();
      if (scores[existingType] !== undefined) {
        scores[existingType] += 5; // 现有类型有一定权重，但不是决定性的
      }
    }
    
    // 3. 基于分类的分析
    if (request.category) {
      const category = request.category.toLowerCase();
      
      // 餐饮相关分类
      if (['restaurant', 'food', 'dining', 'cafe', 'bar'].includes(category)) {
        scores.meal += 15;
      }
      
      // 景点相关分类
      if (['tourist_attraction', 'landmark', 'monument'].includes(category)) {
        scores.attraction += 15;
      }
      
      // 文化相关分类
      if (['museum', 'gallery', 'cultural_site'].includes(category)) {
        scores.cultural += 15;
      }
    }
    
    // 4. 语义模式识别
    this.applySemanticPatterns(textContent, scores);
    
    console.log('📊 多维度分析结果:', scores);
    
    return scores;
  }

  /**
   * 🧠 应用语义模式
   */
  private static applySemanticPatterns(textContent: any, scores: Record<string, number>): void {
    const combined = textContent.combined;
    
    // 用餐相关模式
    if (combined.match(/(品尝|享用|用餐|就餐|吃|喝)/)) {
      scores.meal += 12;
    }
    
    if (combined.match(/(菜单|口味|美味|好吃|推荐菜)/)) {
      scores.meal += 10;
    }
    
    // 观光相关模式
    if (combined.match(/(参观|游览|观赏|欣赏|拍照|打卡)/)) {
      scores.attraction += 8;
    }
    
    if (combined.match(/(风景|景色|美景|壮观|历史)/)) {
      scores.attraction += 6;
    }
    
    // 文化相关模式
    if (combined.match(/(展览|收藏|艺术品|文物|历史)/)) {
      scores.cultural += 10;
    }
    
    // 娱乐相关模式
    if (combined.match(/(游戏|娱乐|放松|休闲|体验)/)) {
      scores.entertainment += 8;
    }
    
    // 购物相关模式
    if (combined.match(/(购买|选购|逛街|血拼|打折)/)) {
      scores.shopping += 10;
    }
    
    // 交通相关模式
    if (combined.match(/(前往|到达|出发|路线|交通)/)) {
      scores.transport += 15;
    }
  }

  /**
   * 🎯 确定主要类型
   */
  private static determinePrimaryType(scores: Record<string, number>): string {
    let maxScore = 0;
    let primaryType = 'attraction'; // 默认类型
    
    Object.entries(scores).forEach(([type, score]) => {
      if (score > maxScore) {
        maxScore = score;
        primaryType = type;
      }
    });
    
    // 如果所有分数都很低，使用启发式规则
    if (maxScore < 5) {
      primaryType = 'attraction'; // 默认为景点
    }
    
    return primaryType;
  }

  /**
   * 🏷️ 生成子分类
   */
  private static generateSubCategory(primaryType: string, textContent: any): string {
    const combined = textContent.combined;
    
    switch (primaryType) {
      case 'meal':
        if (combined.includes('早餐') || combined.includes('breakfast')) return '早餐';
        if (combined.includes('午餐') || combined.includes('lunch')) return '午餐';
        if (combined.includes('晚餐') || combined.includes('dinner')) return '晚餐';
        if (combined.includes('咖啡') || combined.includes('coffee')) return '咖啡厅';
        if (combined.includes('酒吧') || combined.includes('bar')) return '酒吧';
        if (combined.includes('甜品') || combined.includes('dessert')) return '甜品店';
        return '餐厅';
        
      case 'attraction':
        if (combined.includes('寺庙') || combined.includes('temple')) return '宗教建筑';
        if (combined.includes('公园') || combined.includes('park')) return '公园';
        if (combined.includes('广场') || combined.includes('square')) return '广场';
        if (combined.includes('观景') || combined.includes('viewpoint')) return '观景点';
        if (combined.includes('建筑') || combined.includes('building')) return '建筑';
        return '景点';
        
      case 'cultural':
        if (combined.includes('博物馆') || combined.includes('museum')) return '博物馆';
        if (combined.includes('美术馆') || combined.includes('gallery')) return '美术馆';
        if (combined.includes('剧院') || combined.includes('theater')) return '剧院';
        return '文化场所';
        
      case 'entertainment':
        if (combined.includes('游乐园') || combined.includes('amusement')) return '游乐园';
        if (combined.includes('水族馆') || combined.includes('aquarium')) return '水族馆';
        if (combined.includes('动物园') || combined.includes('zoo')) return '动物园';
        return '娱乐场所';
        
      case 'shopping':
        if (combined.includes('商场') || combined.includes('mall')) return '购物中心';
        if (combined.includes('市场') || combined.includes('market')) return '市场';
        if (combined.includes('免税') || combined.includes('duty free')) return '免税店';
        return '购物场所';
        
      case 'transport':
        if (combined.includes('步行') || combined.includes('walk')) return '步行';
        if (combined.includes('地铁') || combined.includes('subway')) return '地铁';
        if (combined.includes('公交') || combined.includes('bus')) return '公交';
        if (combined.includes('出租车') || combined.includes('taxi')) return '出租车';
        return '交通';
        
      case 'accommodation':
        if (combined.includes('酒店') || combined.includes('hotel')) return '酒店';
        if (combined.includes('民宿') || combined.includes('hostel')) return '民宿';
        return '住宿';
        
      default:
        return '其他';
    }
  }

  /**
   * 📊 计算置信度
   */
  private static calculateConfidence(scores: Record<string, number>, primaryType: string): number {
    const primaryScore = scores[primaryType] || 0;
    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
    
    if (totalScore === 0) return 0.5; // 默认置信度
    
    const confidence = primaryScore / totalScore;
    
    // 调整置信度范围
    if (confidence > 0.8) return Math.min(0.95, confidence);
    if (confidence > 0.6) return confidence;
    if (confidence > 0.4) return Math.max(0.6, confidence);
    
    return Math.max(0.3, confidence);
  }

  /**
   * 📝 生成推理过程
   */
  private static generateReasoning(
    textContent: any,
    primaryType: string,
    scores: Record<string, number>
  ): string[] {
    const reasoning: string[] = [];
    const primaryScore = scores[primaryType] || 0;
    
    // 基于分数的推理
    if (primaryScore > 20) {
      reasoning.push(`强烈特征匹配，识别为${this.getTypeDisplayName(primaryType)}`);
    } else if (primaryScore > 10) {
      reasoning.push(`明显特征匹配，分类为${this.getTypeDisplayName(primaryType)}`);
    } else if (primaryScore > 5) {
      reasoning.push(`基于关键词分析，判断为${this.getTypeDisplayName(primaryType)}`);
    } else {
      reasoning.push(`基于默认规则，分类为${this.getTypeDisplayName(primaryType)}`);
    }
    
    // 基于具体特征的推理
    if (primaryType === 'meal') {
      if (textContent.name.includes('餐') || textContent.name.includes('食')) {
        reasoning.push('名称包含餐饮相关词汇');
      }
      if (textContent.combined.includes('一兰') || textContent.combined.includes('ichiran')) {
        reasoning.push('识别为知名餐饮品牌');
      }
    }
    
    if (primaryType === 'attraction') {
      if (textContent.combined.includes('浅草寺') || textContent.combined.includes('涩谷')) {
        reasoning.push('识别为知名景点');
      }
    }
    
    // 置信度推理
    const confidence = this.calculateConfidence(scores, primaryType);
    if (confidence > 0.8) {
      reasoning.push('高置信度分类结果');
    } else if (confidence < 0.6) {
      reasoning.push('中等置信度，建议人工确认');
    }
    
    return reasoning;
  }

  /**
   * 🔄 生成备选类型
   */
  private static generateAlternativeTypes(
    scores: Record<string, number>,
    primaryType: string
  ): Array<{ type: string; confidence: number; reason: string }> {
    const alternatives: Array<{ type: string; confidence: number; reason: string }> = [];
    
    // 排序并获取前3个备选类型
    const sortedTypes = Object.entries(scores)
      .filter(([type]) => type !== primaryType)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3);
    
    sortedTypes.forEach(([type, score]) => {
      if (score > 3) { // 只包含有一定分数的备选类型
        const totalScore = Object.values(scores).reduce((sum, s) => sum + s, 0);
        const confidence = totalScore > 0 ? score / totalScore : 0;
        
        alternatives.push({
          type,
          confidence: Math.round(confidence * 100) / 100,
          reason: `基于关键词匹配，得分${score}`
        });
      }
    });
    
    return alternatives;
  }

  /**
   * 🏷️ 生成标签
   */
  private static generateTags(textContent: any, primaryType: string): string[] {
    const tags: string[] = [primaryType];
    const combined = textContent.combined;
    
    // 基于内容生成标签
    if (combined.includes('免费') || combined.includes('free')) tags.push('免费');
    if (combined.includes('预订') || combined.includes('booking')) tags.push('需预订');
    if (combined.includes('排队') || combined.includes('queue')) tags.push('可能排队');
    if (combined.includes('网红') || combined.includes('popular')) tags.push('网红打卡');
    if (combined.includes('传统') || combined.includes('traditional')) tags.push('传统文化');
    if (combined.includes('现代') || combined.includes('modern')) tags.push('现代');
    if (combined.includes('户外') || combined.includes('outdoor')) tags.push('户外');
    if (combined.includes('室内') || combined.includes('indoor')) tags.push('室内');
    
    return [...new Set(tags)]; // 去重
  }

  /**
   * 🏷️ 获取类型显示名称
   */
  private static getTypeDisplayName(type: string): string {
    const displayNames = {
      meal: '餐饮',
      attraction: '景点',
      cultural: '文化',
      entertainment: '娱乐',
      shopping: '购物',
      transport: '交通',
      accommodation: '住宿'
    };
    
    return displayNames[type] || type;
  }

  /**
   * 🎯 批量分类活动
   */
  static classifyActivities(activities: ActivityClassificationRequest[]): ClassificationResult[] {
    console.log(`🏷️ 开始批量分类${activities.length}个活动`);
    
    const results = activities.map(activity => this.classifyActivity(activity));
    
    // 统计分类结果
    const typeCount = results.reduce((count, result) => {
      count[result.primaryType] = (count[result.primaryType] || 0) + 1;
      return count;
    }, {} as Record<string, number>);
    
    console.log('📊 批量分类统计:', typeCount);
    
    return results;
  }

  /**
   * 🔧 验证分类结果
   */
  static validateClassification(
    activity: ActivityClassificationRequest,
    result: ClassificationResult
  ): { isValid: boolean; issues: string[]; suggestions: string[] } {
    const issues: string[] = [];
    const suggestions: string[] = [];
    
    // 检查置信度
    if (result.confidence < 0.5) {
      issues.push('分类置信度较低');
      suggestions.push('建议人工确认分类结果');
    }
    
    // 检查类型与图标的一致性
    if (result.primaryType === 'meal' && result.iconType !== 'restaurant') {
      issues.push('餐饮活动图标类型不匹配');
      suggestions.push('应使用餐厅图标');
    }
    
    // 检查名称与类型的一致性
    const name = (activity.name || '').toLowerCase();
    if (name.includes('餐') && result.primaryType !== 'meal') {
      issues.push('名称暗示餐饮但分类为其他类型');
      suggestions.push('重新检查餐饮相关特征');
    }
    
    const isValid = issues.length === 0 && result.confidence >= 0.6;
    
    return { isValid, issues, suggestions };
  }
}