/**
 * 🧪 Ultra Think Master Solver 测试脚本
 * 验证所有问题是否已彻底解决
 */

import { UltraThinkMasterSolver } from '../services/ultra-think/UltraThinkMasterSolver';

async function testMasterSolver() {
  console.log('🧪 开始测试 Ultra Think Master Solver');
  console.log('='.repeat(50));

  const testRequest = {
    destination: '吉隆坡',
    duration: 3,
    budget: 6000,
    currency: 'MYR',
    travelers: 2,
    startDate: new Date(),
    preferences: {
      travelStyle: ['文化探索', '美食体验'],
      accommodation: ['酒店'],
      transport: ['公共交通', '出租车']
    }
  };

  try {
    const result = await UltraThinkMasterSolver.solveMasterProblems(testRequest);

    console.log('📊 测试结果:');
    console.log(`✅ 成功: ${result.success}`);
    console.log(`📈 质量分: ${result.qualityScore}`);
    console.log(`⏱️ 执行时间: ${result.executionTime}ms`);
    console.log(`🎯 活动数量: ${result.activities.length}`);
    
    console.log('\n🔧 修复项目:');
    result.fixes.forEach((fix, index) => {
      console.log(`  ${index + 1}. ${fix}`);
    });

    if (result.issues.length > 0) {
      console.log('\n⚠️ 发现问题:');
      result.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }

    console.log('\n📋 活动详情验证:');
    
    // 验证1: 时间格式
    const timeFormatIssues = result.activities.filter(activity => {
      const startTime = activity.timing?.startTime;
      const endTime = activity.timing?.endTime;
      return !startTime || !endTime || !/^\d{2}:\d{2}$/.test(startTime) || !/^\d{2}:\d{2}$/.test(endTime);
    });
    
    console.log(`⏰ 时间格式检查: ${timeFormatIssues.length === 0 ? '✅ 通过' : `❌ ${timeFormatIssues.length}个问题`}`);

    // 验证2: 预算约束
    const totalCost = result.activities.reduce((sum, activity) => sum + (activity.cost?.amount || 0), 0);
    const budgetOK = totalCost <= testRequest.budget;
    console.log(`💰 预算约束检查: ${budgetOK ? '✅ 通过' : '❌ 超预算'} (${totalCost}/${testRequest.budget} ${testRequest.currency})`);

    // 验证3: 交通活动
    const transportCount = result.activities.filter(a => a.type === 'transport').length;
    console.log(`🚗 交通活动检查: ${transportCount > 0 ? '✅ 通过' : '❌ 缺失'} (${transportCount}个)`);

    // 验证4: 美食活动
    const foodCount = result.activities.filter(a => a.type === 'restaurant').length;
    console.log(`🍽️ 美食活动检查: ${foodCount >= testRequest.duration * 2 ? '✅ 通过' : '❌ 不足'} (${foodCount}个)`);

    // 验证5: 展开内容
    const expandedContentCount = result.activities.filter(a => a.expandedContent && Object.keys(a.expandedContent).length > 0).length;
    console.log(`📝 展开内容检查: ${expandedContentCount === result.activities.length ? '✅ 通过' : '❌ 不完整'} (${expandedContentCount}/${result.activities.length})`);

    // 验证6: 输出格式
    const formatIssues = result.activities.filter(activity => {
      return !activity.id || !activity.name || !activity.timing || !activity.cost;
    });
    console.log(`🎨 输出格式检查: ${formatIssues.length === 0 ? '✅ 通过' : `❌ ${formatIssues.length}个问题`}`);

    console.log('\n📊 活动分布统计:');
    const activityTypes = {};
    result.activities.forEach(activity => {
      const type = activity.type || 'unknown';
      activityTypes[type] = (activityTypes[type] || 0) + 1;
    });
    
    Object.entries(activityTypes).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}个`);
    });

    console.log('\n📅 每日活动分布:');
    for (let day = 1; day <= testRequest.duration; day++) {
      const dayActivities = result.activities.filter(a => a.timing?.day === day);
      console.log(`  第${day}天: ${dayActivities.length}个活动`);
    }

    console.log('\n💡 总结:');
    const allChecksPass = timeFormatIssues.length === 0 && 
                         budgetOK && 
                         transportCount > 0 && 
                         foodCount >= testRequest.duration * 2 && 
                         expandedContentCount === result.activities.length && 
                         formatIssues.length === 0;

    if (allChecksPass) {
      console.log('🎉 所有检查通过！Master Solver成功解决了所有问题！');
    } else {
      console.log('⚠️ 仍有部分问题需要解决');
    }

    return result;

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testMasterSolver()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试失败:', error);
      process.exit(1);
    });
}

export { testMasterSolver };
