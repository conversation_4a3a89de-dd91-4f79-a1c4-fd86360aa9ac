/**
 * 🧹 数据质量处理器
 * 解决重复活动、数据清洗和内容增强问题
 */

export interface CleanedActivity {
  id: string;
  name: string;
  type: string;
  day: number;
  startTime: string;
  endTime: string;
  duration: number;
  cost: number;
  location?: any;
  isGenerated?: boolean;
  priority: number;
}

export class DataQualityProcessor {
  
  /**
   * 🔍 主要数据清洗和去重方法
   */
  static cleanAndDeduplicateActivities(activities: any[]): CleanedActivity[] {
    console.log('🧹 开始数据质量处理，输入活动数量:', activities.length);
    
    // 1. 去重逻辑
    const uniqueActivities = this.removeDuplicates(activities);
    console.log(`✅ 去重完成: ${activities.length} -> ${uniqueActivities.length}`);
    
    // 2. 数据验证和标准化
    const validatedActivities = this.validateAndStandardize(uniqueActivities);
    console.log(`✅ 数据验证完成: ${validatedActivities.length}个活动`);
    
    // 3. 内容增强
    const enhancedActivities = this.enhanceActivityContent(validatedActivities);
    console.log(`✅ 内容增强完成: ${enhancedActivities.length}个活动`);
    
    return enhancedActivities;
  }
  
  /**
   * 🗑️ 智能去重逻辑
   */
  private static removeDuplicates(activities: any[]): any[] {
    const seen = new Map();
    const uniqueActivities: any[] = [];
    
    activities.forEach(activity => {
      // 生成唯一标识符
      const key = this.generateActivityKey(activity);
      
      if (seen.has(key)) {
        const existing = seen.get(key);
        console.log(`🗑️ 发现重复活动: ${activity.name || activity.title} (Day ${activity.day})`);
        
        // 保留更完整的数据
        if (this.isMoreComplete(activity, existing)) {
          console.log(`🔄 替换为更完整的版本`);
          const index = uniqueActivities.findIndex(a => this.generateActivityKey(a) === key);
          if (index !== -1) {
            uniqueActivities[index] = activity;
          }
          seen.set(key, activity);
        }
      } else {
        seen.set(key, activity);
        uniqueActivities.push(activity);
      }
    });
    
    return uniqueActivities;
  }
  
  /**
   * 🔑 生成活动唯一标识符
   */
  private static generateActivityKey(activity: any): string {
    const name = (activity.name || activity.title || activity.activityName || '').toLowerCase().trim();
    const type = this.detectActivityType(activity);
    const day = activity.day || activity.timing?.day || 1;
    
    // 对于交通活动，包含起点终点信息
    if (type === 'transport') {
      const location = activity.location?.name || activity.destination || '';
      return `${name}-${type}-${day}-${location}`;
    }
    
    return `${name}-${type}-${day}`;
  }
  
  /**
   * 📊 判断哪个活动数据更完整
   */
  private static isMoreComplete(activity1: any, activity2: any): boolean {
    const score1 = this.calculateCompletenessScore(activity1);
    const score2 = this.calculateCompletenessScore(activity2);
    return score1 > score2;
  }
  
  /**
   * 📈 计算数据完整性评分
   */
  private static calculateCompletenessScore(activity: any): number {
    let score = 0;
    
    // 基础信息
    if (activity.name || activity.title) score += 10;
    if (activity.startTime) score += 10;
    if (activity.endTime) score += 10;
    if (activity.cost !== undefined) score += 5;
    if (activity.location) score += 5;
    if (activity.description) score += 5;
    
    // 时间信息
    if (activity.timing?.startTime) score += 8;
    if (activity.timing?.endTime) score += 8;
    if (activity.timing?.duration) score += 3;
    
    // 位置信息
    if (activity.location?.coordinates) score += 5;
    if (activity.location?.address) score += 3;
    
    return score;
  }
  
  /**
   * ✅ 数据验证和标准化
   */
  private static validateAndStandardize(activities: any[]): CleanedActivity[] {
    return activities.map((activity, index) => {
      const standardized: CleanedActivity = {
        id: activity.id || `activity_${Date.now()}_${index}`,
        name: this.cleanActivityName(activity),
        type: this.detectActivityType(activity),
        day: this.validateDay(activity),
        startTime: this.validateTime(activity.startTime || activity.timing?.startTime),
        endTime: this.validateTime(activity.endTime || activity.timing?.endTime),
        duration: this.calculateDuration(activity),
        cost: this.validateCost(activity.cost),
        location: activity.location,
        isGenerated: activity.generated || false,
        priority: this.calculatePriority(activity)
      };
      
      // 确保结束时间晚于开始时间
      if (standardized.startTime === standardized.endTime) {
        standardized.endTime = this.addMinutesToTime(standardized.startTime, standardized.duration);
      }
      
      return standardized;
    });
  }
  
  /**
   * 🏷️ 清理活动名称
   */
  private static cleanActivityName(activity: any): string {
    let name = activity.name || activity.title || activity.activityName || '未命名活动';
    
    // 移除多余的前缀
    name = name.replace(/^(前往|到达|离开|参观|游览|体验)\s*/, '');
    
    // 首字母大写
    name = name.charAt(0).toUpperCase() + name.slice(1);
    
    return name.trim();
  }
  
  /**
   * 🏷️ 检测活动类型
   */
  private static detectActivityType(activity: any): string {
    if (activity.type) return activity.type;
    
    const name = (activity.name || activity.title || '').toLowerCase();
    
    // 住宿
    if (name.includes('酒店') || name.includes('hotel') || name.includes('入住')) {
      return 'accommodation';
    }
    
    // 餐饮
    if (name.includes('餐') || name.includes('食') || name.includes('寿司') || 
        name.includes('拉面') || name.includes('料理') || name.includes('咖啡')) {
      return 'meal';
    }
    
    // 交通
    if (name.includes('地铁') || name.includes('巴士') || name.includes('taxi') || 
        name.includes('前往') || name.includes('到达') || name.includes('离开')) {
      return 'transport';
    }
    
    // 购物
    if (name.includes('购物') || name.includes('商店') || name.includes('市场') || 
        name.includes('百货')) {
      return 'shopping';
    }
    
    // 默认为景点
    return 'attraction';
  }
  
  /**
   * 📅 验证天数
   */
  private static validateDay(activity: any): number {
    const day = activity.day || activity.timing?.day || activity.scheduledDay || 1;
    return Math.max(1, Math.min(7, day)); // 限制在1-7天
  }
  
  /**
   * ⏰ 验证时间格式
   */
  private static validateTime(time: any): string {
    if (!time) return '09:00';
    
    const timeStr = String(time);
    
    // 如果已经是正确格式
    if (timeStr.match(/^\d{2}:\d{2}$/)) {
      return timeStr;
    }
    
    // 如果是 H:M 格式，补零
    if (timeStr.includes(':')) {
      const [hours, minutes] = timeStr.split(':');
      return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
    }
    
    // 默认返回
    return '09:00';
  }
  
  /**
   * ⏱️ 计算活动持续时间
   */
  private static calculateDuration(activity: any): number {
    if (activity.duration) return activity.duration;
    if (activity.timing?.duration) return activity.timing.duration;
    
    // 基于活动类型的默认时长
    const type = this.detectActivityType(activity);
    const defaultDurations = {
      attraction: 120,
      meal: 90,
      transport: 30,
      accommodation: 60,
      shopping: 120
    };
    
    return defaultDurations[type] || 90;
  }
  
  /**
   * 💰 验证费用
   */
  private static validateCost(cost: any): number {
    if (typeof cost === 'number') return Math.max(0, cost);
    if (cost && typeof cost === 'object' && cost.amount !== undefined) {
      return Math.max(0, cost.amount);
    }
    return 0;
  }
  
  /**
   * 📊 计算活动优先级
   */
  private static calculatePriority(activity: any): number {
    let priority = 5; // 基础优先级
    
    const type = this.detectActivityType(activity);
    const name = (activity.name || activity.title || '').toLowerCase();
    
    // 类型优先级
    if (type === 'accommodation') priority += 3;
    else if (type === 'meal') priority += 2;
    else if (type === 'attraction') priority += 1;
    else if (type === 'transport') priority -= 2;
    
    // 知名度优先级
    if (name.includes('塔') || name.includes('寺') || name.includes('神宫')) priority += 2;
    if (name.includes('免费') || name.includes('公园')) priority += 1;
    
    return Math.max(1, Math.min(10, priority));
  }
  
  /**
   * ✨ 内容增强
   */
  private static enhanceActivityContent(activities: CleanedActivity[]): CleanedActivity[] {
    return activities.map(activity => ({
      ...activity,
      // 可以在这里添加更多增强逻辑
    }));
  }
  
  /**
   * 🕐 时间计算辅助方法
   */
  private static addMinutesToTime(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMins = totalMinutes % 60;
    
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  }
}
