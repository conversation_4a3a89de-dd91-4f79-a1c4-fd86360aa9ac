/**
 * 🔄 LLM数据处理流水线
 * 完整的JSON+LLM数据处理流程
 */

import { JSONDataTransfer } from '../../types/JourneyDataTypes';
import { JourneyJSONValidator } from '../../validators/JourneyJSONSchema';
import { LLMContentEnhancer } from './LLMContentEnhancer';
import { LLMQualityChecker } from './LLMQualityChecker';
import { LLMSmartFixer } from './LLMSmartFixer';

export interface PipelineResult {
  success: boolean;
  finalData: JSONDataTransfer;
  processingSteps: ProcessingStep[];
  qualityScore: number;
  executionTime: number;
  errors: string[];
  warnings: string[];
}

export interface ProcessingStep {
  step: string;
  success: boolean;
  duration: number;
  description: string;
  qualityChange?: number;
}

export class LLMDataPipeline {
  private validator: JourneyJSONValidator;
  private contentEnhancer: LLMContentEnhancer;
  private qualityChecker: LLMQualityChecker;
  private smartFixer: LLMSmartFixer;
  
  constructor() {
    this.validator = new JourneyJSONValidator();
    this.contentEnhancer = new LLMContentEnhancer();
    this.qualityChecker = new LLMQualityChecker();
    this.smartFixer = new LLMSmartFixer();
  }
  
  /**
   * 🔄 完整的数据处理流水线
   */
  async processJourneyData(rawJourneyData: JSONDataTransfer): Promise<PipelineResult> {
    console.log('🔄 开始LLM数据处理流水线');
    const startTime = Date.now();
    const processingSteps: ProcessingStep[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];
    
    let currentData = rawJourneyData;
    let currentQualityScore = rawJourneyData.metadata.qualityScore || 0.8;
    
    try {
      // Step 1: JSON Schema验证
      const validationResult = await this.validateJSONData(currentData);
      processingSteps.push({
        step: 'json_validation',
        success: validationResult.success,
        duration: validationResult.duration,
        description: validationResult.description
      });
      
      if (!validationResult.success) {
        errors.push(...validationResult.errors);
        if (validationResult.fixedData) {
          currentData = validationResult.fixedData;
          warnings.push('使用自动修复的数据');
        } else {
          throw new Error('JSON验证失败且无法自动修复');
        }
      } else {
        currentData = validationResult.data;
      }
      
      // Step 2: LLM内容增强
      const enhancementResult = await this.enhanceContent(currentData);
      processingSteps.push({
        step: 'llm_enhancement',
        success: enhancementResult.success,
        duration: enhancementResult.duration,
        description: enhancementResult.description,
        qualityChange: enhancementResult.qualityChange
      });
      
      if (enhancementResult.success) {
        currentData = enhancementResult.data;
        currentQualityScore += enhancementResult.qualityChange || 0;
      } else {
        warnings.push('LLM内容增强失败，使用原始内容');
      }
      
      // Step 3: LLM质量检查
      const qualityResult = await this.checkQuality(currentData);
      processingSteps.push({
        step: 'quality_check',
        success: qualityResult.success,
        duration: qualityResult.duration,
        description: qualityResult.description
      });
      
      if (!qualityResult.success) {
        warnings.push('质量检查失败，跳过智能修复');
      }
      
      // Step 4: 智能修复（如果质量分数低于阈值）
      if (qualityResult.success && qualityResult.qualityScore < 0.85) {
        const fixResult = await this.smartFix(currentData, qualityResult.qualityReport);
        processingSteps.push({
          step: 'smart_fix',
          success: fixResult.success,
          duration: fixResult.duration,
          description: fixResult.description,
          qualityChange: fixResult.qualityChange
        });
        
        if (fixResult.success) {
          currentData = fixResult.data;
          currentQualityScore += fixResult.qualityChange || 0;
        } else {
          warnings.push('智能修复失败');
        }
      }
      
      // Step 5: 最终验证
      const finalValidationResult = await this.finalValidation(currentData);
      processingSteps.push({
        step: 'final_validation',
        success: finalValidationResult.success,
        duration: finalValidationResult.duration,
        description: finalValidationResult.description
      });
      
      if (!finalValidationResult.success) {
        warnings.push('最终验证发现问题');
        errors.push(...finalValidationResult.errors);
      }
      
      // 更新最终元数据
      currentData.metadata = {
        ...currentData.metadata,
        qualityScore: Math.min(0.98, currentQualityScore),
        pipelineProcessed: true,
        processingTime: Date.now() - startTime,
        processingSteps: processingSteps.length,
        llmEnhanced: processingSteps.some(step => step.step === 'llm_enhancement' && step.success),
        autoFixed: processingSteps.some(step => step.step === 'smart_fix' && step.success)
      };
      
      const executionTime = Date.now() - startTime;
      console.log(`✅ LLM数据处理流水线完成: ${executionTime}ms, 质量分${currentData.metadata.qualityScore}`);
      
      return {
        success: errors.length === 0,
        finalData: currentData,
        processingSteps,
        qualityScore: currentData.metadata.qualityScore,
        executionTime,
        errors,
        warnings
      };
      
    } catch (error) {
      console.error('❌ LLM数据处理流水线失败:', error);
      
      processingSteps.push({
        step: 'pipeline_error',
        success: false,
        duration: Date.now() - startTime,
        description: `流水线失败: ${error.message}`
      });
      
      return {
        success: false,
        finalData: rawJourneyData,
        processingSteps,
        qualityScore: rawJourneyData.metadata.qualityScore || 0.5,
        executionTime: Date.now() - startTime,
        errors: [error.message],
        warnings
      };
    }
  }
  
  /**
   * 🔍 JSON数据验证
   */
  private async validateJSONData(data: JSONDataTransfer): Promise<{
    success: boolean;
    data?: JSONDataTransfer;
    fixedData?: JSONDataTransfer;
    duration: number;
    description: string;
    errors: string[];
  }> {
    const startTime = Date.now();
    console.log('🔍 Step 1: JSON数据验证');
    
    try {
      const validationResult = this.validator.validate(data);
      const duration = Date.now() - startTime;
      
      if (validationResult.isValid) {
        return {
          success: true,
          data: data,
          duration,
          description: `JSON验证通过: ${validationResult.warnings.length}个警告`,
          errors: []
        };
      } else {
        return {
          success: false,
          fixedData: validationResult.fixedData,
          duration,
          description: `JSON验证失败: ${validationResult.errors.length}个错误`,
          errors: validationResult.errors.map(e => e.message)
        };
      }
      
    } catch (error) {
      return {
        success: false,
        duration: Date.now() - startTime,
        description: `JSON验证异常: ${error.message}`,
        errors: [error.message]
      };
    }
  }
  
  /**
   * 🤖 LLM内容增强
   */
  private async enhanceContent(data: JSONDataTransfer): Promise<{
    success: boolean;
    data: JSONDataTransfer;
    duration: number;
    description: string;
    qualityChange?: number;
  }> {
    const startTime = Date.now();
    console.log('🤖 Step 2: LLM内容增强');
    
    try {
      const originalQuality = data.metadata.qualityScore || 0.8;
      const enhancedData = await this.contentEnhancer.enhanceJourneyContent(data);
      const duration = Date.now() - startTime;
      
      const newQuality = enhancedData.metadata.qualityScore || originalQuality;
      const qualityChange = newQuality - originalQuality;
      
      return {
        success: enhancedData.metadata.llmEnhanced || false,
        data: enhancedData,
        duration,
        description: `LLM内容增强${enhancedData.metadata.llmEnhanced ? '成功' : '失败'}`,
        qualityChange
      };
      
    } catch (error) {
      console.error('LLM内容增强失败:', error);
      return {
        success: false,
        data: data,
        duration: Date.now() - startTime,
        description: `LLM内容增强失败: ${error.message}`
      };
    }
  }
  
  /**
   * 🔍 质量检查
   */
  private async checkQuality(data: JSONDataTransfer): Promise<{
    success: boolean;
    duration: number;
    description: string;
    qualityScore: number;
    qualityReport?: any;
  }> {
    const startTime = Date.now();
    console.log('🔍 Step 3: LLM质量检查');
    
    try {
      const qualityReport = await this.qualityChecker.validateJourneyQuality(data);
      const duration = Date.now() - startTime;
      
      return {
        success: true,
        duration,
        description: `质量检查完成: 评分${qualityReport.overallScore.toFixed(2)}, ${qualityReport.criticalIssues.length}个关键问题`,
        qualityScore: qualityReport.overallScore,
        qualityReport
      };
      
    } catch (error) {
      console.error('质量检查失败:', error);
      return {
        success: false,
        duration: Date.now() - startTime,
        description: `质量检查失败: ${error.message}`,
        qualityScore: data.metadata.qualityScore || 0.8
      };
    }
  }
  
  /**
   * 🔧 智能修复
   */
  private async smartFix(data: JSONDataTransfer, qualityReport: any): Promise<{
    success: boolean;
    data: JSONDataTransfer;
    duration: number;
    description: string;
    qualityChange?: number;
  }> {
    const startTime = Date.now();
    console.log('🔧 Step 4: LLM智能修复');
    
    try {
      const originalQuality = data.metadata.qualityScore || 0.8;
      const fixResult = await this.smartFixer.autoFixIssues(data, qualityReport);
      const duration = Date.now() - startTime;
      
      if (fixResult.success) {
        return {
          success: true,
          data: fixResult.fixedData,
          duration,
          description: `智能修复成功: ${fixResult.appliedFixes.length}个修复`,
          qualityChange: fixResult.qualityImprovement
        };
      } else {
        return {
          success: false,
          data: data,
          duration,
          description: `智能修复失败: ${fixResult.remainingIssues.length}个未解决问题`
        };
      }
      
    } catch (error) {
      console.error('智能修复失败:', error);
      return {
        success: false,
        data: data,
        duration: Date.now() - startTime,
        description: `智能修复失败: ${error.message}`
      };
    }
  }
  
  /**
   * ✅ 最终验证
   */
  private async finalValidation(data: JSONDataTransfer): Promise<{
    success: boolean;
    duration: number;
    description: string;
    errors: string[];
  }> {
    const startTime = Date.now();
    console.log('✅ Step 5: 最终验证');
    
    try {
      const validationResult = this.validator.validate(data);
      const duration = Date.now() - startTime;
      
      // 检查关键指标
      const errors: string[] = [];
      
      if (!data.payload.dayPlans || data.payload.dayPlans.length === 0) {
        errors.push('缺少日程计划');
      }
      
      if (!data.payload.budget || !data.payload.budget.total) {
        errors.push('缺少预算信息');
      }
      
      const totalActivities = data.payload.dayPlans?.reduce((sum, day) => sum + (day.activities?.length || 0), 0) || 0;
      if (totalActivities === 0) {
        errors.push('没有活动安排');
      }
      
      return {
        success: validationResult.isValid && errors.length === 0,
        duration,
        description: `最终验证${errors.length === 0 ? '通过' : '发现问题'}: ${totalActivities}个活动`,
        errors
      };
      
    } catch (error) {
      return {
        success: false,
        duration: Date.now() - startTime,
        description: `最终验证异常: ${error.message}`,
        errors: [error.message]
      };
    }
  }
  
  /**
   * 📊 生成处理报告
   */
  generateProcessingReport(result: PipelineResult): string {
    const report = [
      '🔄 LLM数据处理流水线报告',
      '='.repeat(40),
      `✅ 处理状态: ${result.success ? '成功' : '失败'}`,
      `📊 最终质量分: ${result.qualityScore.toFixed(2)}`,
      `⏱️ 执行时间: ${result.executionTime}ms`,
      `🔧 处理步骤: ${result.processingSteps.length}个`,
      '',
      '📋 处理步骤详情:',
      ...result.processingSteps.map(step => 
        `  ${step.success ? '✅' : '❌'} ${step.step}: ${step.description} (${step.duration}ms)`
      ),
      ''
    ];
    
    if (result.warnings.length > 0) {
      report.push('⚠️ 警告:');
      report.push(...result.warnings.map(warning => `  - ${warning}`));
      report.push('');
    }
    
    if (result.errors.length > 0) {
      report.push('❌ 错误:');
      report.push(...result.errors.map(error => `  - ${error}`));
      report.push('');
    }
    
    return report.join('\n');
  }
}
