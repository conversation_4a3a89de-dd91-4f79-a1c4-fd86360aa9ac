/**
 * 🍽️ 餐厅数据服务
 * 
 * 集成OSM Overpass API + Yelp Fusion API，实现餐厅基础数据和评价数据获取
 * 成本优化：从$100-180/月降至$0-30/月
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// ===== 接口定义 =====

export interface RestaurantData {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  address: string;
  phone?: string;
  website?: string;
  cuisine: string[];
  priceLevel: 1 | 2 | 3 | 4; // 1=便宜, 4=昂贵
  rating?: number;
  reviewCount?: number;
  openingHours?: OpeningHours;
  features: RestaurantFeature[];
  photos?: string[];
  description?: string;
  source: DataSource[];
  lastUpdated: Date;
  confidence: number;
}

export interface OpeningHours {
  monday?: string;
  tuesday?: string;
  wednesday?: string;
  thursday?: string;
  friday?: string;
  saturday?: string;
  sunday?: string;
  isOpen24Hours?: boolean;
  specialHours?: { date: string; hours: string }[];
}

export interface RestaurantFeature {
  type: FeatureType;
  value: string | boolean;
  source: string;
}

export interface DataSource {
  provider: 'osm' | 'yelp' | 'cache' | 'merged';
  confidence: number;
  lastUpdated: Date;
}

export interface RestaurantSearchOptions {
  latitude: number;
  longitude: number;
  radius?: number; // 米
  cuisine?: string[];
  priceLevel?: number[];
  rating?: number;
  limit?: number;
  useCache?: boolean;
  includePhotos?: boolean;
  language?: string;
}

export enum FeatureType {
  DELIVERY = 'delivery',
  TAKEOUT = 'takeout',
  OUTDOOR_SEATING = 'outdoor_seating',
  WIFI = 'wifi',
  PARKING = 'parking',
  WHEELCHAIR_ACCESSIBLE = 'wheelchair_accessible',
  ACCEPTS_CREDIT_CARDS = 'accepts_credit_cards',
  RESERVATIONS = 'reservations',
  VEGETARIAN_FRIENDLY = 'vegetarian_friendly',
  HALAL = 'halal',
  ALCOHOL = 'alcohol'
}

// ===== 餐厅数据服务 =====

export class RestaurantDataService {
  private static instance: RestaurantDataService;
  private osmOverpassUrl = 'https://overpass-api.de/api/interpreter';
  private yelpApiUrl = 'https://api.yelp.com/v3';
  private yelpApiKey = process.env.YELP_API_KEY || '';
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessing = false;
  private lastOSMRequest = 0;
  private lastYelpRequest = 0;
  private readonly osmInterval = 1000; // OSM 1秒间隔
  private readonly yelpInterval = 100; // Yelp 100ms间隔
  private readonly cachePrefix = 'restaurant_cache_';
  private readonly cacheTTL = 24 * 60 * 60 * 1000; // 24小时缓存

  private constructor() {}

  /**
   * 🎯 获取单例实例
   */
  static getInstance(): RestaurantDataService {
    if (!RestaurantDataService.instance) {
      RestaurantDataService.instance = new RestaurantDataService();
    }
    return RestaurantDataService.instance;
  }

  /**
   * 🔍 搜索餐厅
   */
  async searchRestaurants(options: RestaurantSearchOptions): Promise<RestaurantData[]> {
    const cacheKey = this.generateCacheKey('search', options);
    
    // 1. 检查缓存
    if (options.useCache !== false) {
      const cached = await this.getCachedResult(cacheKey);
      if (cached) {
        return cached;
      }
    }

    try {
      // 2. 并行获取OSM和Yelp数据
      const [osmResults, yelpResults] = await Promise.allSettled([
        this.searchOSMRestaurants(options),
        this.searchYelpRestaurants(options)
      ]);

      // 3. 处理结果
      const osmData = osmResults.status === 'fulfilled' ? osmResults.value : [];
      const yelpData = yelpResults.status === 'fulfilled' ? yelpResults.value : [];

      // 4. 合并数据
      const mergedResults = this.mergeRestaurantData(osmData, yelpData);

      // 5. 排序和过滤
      const filteredResults = this.filterAndSortResults(mergedResults, options);

      // 6. 缓存结果
      if (options.useCache !== false) {
        await this.cacheResult(cacheKey, filteredResults);
      }

      return filteredResults;

    } catch (error) {
      console.error('餐厅搜索失败:', error);
      return [];
    }
  }

  /**
   * 📍 获取餐厅详情
   */
  async getRestaurantDetails(
    restaurantId: string,
    source: 'osm' | 'yelp' = 'osm'
  ): Promise<RestaurantData | null> {
    
    const cacheKey = `details_${source}_${restaurantId}`;
    
    // 1. 检查缓存
    const cached = await this.getCachedResult(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let result: RestaurantData | null = null;

      if (source === 'osm') {
        result = await this.getOSMRestaurantDetails(restaurantId);
      } else if (source === 'yelp') {
        result = await this.getYelpRestaurantDetails(restaurantId);
      }

      // 2. 缓存结果
      if (result) {
        await this.cacheResult(cacheKey, result);
      }

      return result;

    } catch (error) {
      console.error('获取餐厅详情失败:', error);
      return null;
    }
  }

  /**
   * 🍜 按菜系搜索
   */
  async searchByCuisine(
    cuisine: string,
    latitude: number,
    longitude: number,
    radius: number = 2000
  ): Promise<RestaurantData[]> {
    
    return this.searchRestaurants({
      latitude,
      longitude,
      radius,
      cuisine: [cuisine],
      limit: 20
    });
  }

  /**
   * ⭐ 按评分搜索
   */
  async searchByRating(
    minRating: number,
    latitude: number,
    longitude: number,
    radius: number = 2000
  ): Promise<RestaurantData[]> {
    
    return this.searchRestaurants({
      latitude,
      longitude,
      radius,
      rating: minRating,
      limit: 15
    });
  }

  // ===== OSM Overpass API 方法 =====

  /**
   * 🗺️ 搜索OSM餐厅数据
   */
  private async searchOSMRestaurants(options: RestaurantSearchOptions): Promise<RestaurantData[]> {
    const query = this.buildOSMQuery(options);
    
    const response = await this.makeRateLimitedRequest(
      this.osmOverpassUrl,
      'POST',
      { body: query },
      'osm'
    );

    return this.processOSMResults(response.elements || []);
  }

  /**
   * 🏗️ 构建OSM查询
   */
  private buildOSMQuery(options: RestaurantSearchOptions): string {
    const { latitude, longitude, radius = 2000 } = options;
    
    let cuisineFilter = '';
    if (options.cuisine && options.cuisine.length > 0) {
      const cuisineList = options.cuisine.map(c => `"${c}"`).join('|');
      cuisineFilter = `[cuisine~"${cuisineList}"]`;
    }

    return `
      [out:json][timeout:25];
      (
        node["amenity"="restaurant"]${cuisineFilter}(around:${radius},${latitude},${longitude});
        node["amenity"="fast_food"]${cuisineFilter}(around:${radius},${latitude},${longitude});
        node["amenity"="cafe"]${cuisineFilter}(around:${radius},${latitude},${longitude});
        node["amenity"="bar"]${cuisineFilter}(around:${radius},${latitude},${longitude});
        way["amenity"="restaurant"]${cuisineFilter}(around:${radius},${latitude},${longitude});
        way["amenity"="fast_food"]${cuisineFilter}(around:${radius},${latitude},${longitude});
        way["amenity"="cafe"]${cuisineFilter}(around:${radius},${latitude},${longitude});
        way["amenity"="bar"]${cuisineFilter}(around:${radius},${latitude},${longitude});
      );
      out center meta;
    `;
  }

  /**
   * 🔄 处理OSM结果
   */
  private processOSMResults(elements: any[]): RestaurantData[] {
    return elements.map(element => {
      const tags = element.tags || {};
      const lat = element.lat || (element.center && element.center.lat) || 0;
      const lon = element.lon || (element.center && element.center.lon) || 0;

      return {
        id: `osm_${element.id}`,
        name: tags.name || tags['name:zh'] || tags['name:en'] || '未知餐厅',
        latitude: lat,
        longitude: lon,
        address: this.buildOSMAddress(tags),
        phone: tags.phone || tags['contact:phone'],
        website: tags.website || tags['contact:website'],
        cuisine: this.parseOSMCuisine(tags.cuisine),
        priceLevel: this.parseOSMPriceLevel(tags),
        openingHours: this.parseOSMOpeningHours(tags.opening_hours),
        features: this.parseOSMFeatures(tags),
        source: [{
          provider: 'osm',
          confidence: 0.8,
          lastUpdated: new Date()
        }],
        lastUpdated: new Date(),
        confidence: 0.8
      };
    }).filter(restaurant => restaurant.name !== '未知餐厅');
  }

  // ===== Yelp API 方法 =====

  /**
   * 🔍 搜索Yelp餐厅数据
   */
  private async searchYelpRestaurants(options: RestaurantSearchOptions): Promise<RestaurantData[]> {
    if (!this.yelpApiKey) {
      console.warn('Yelp API密钥未配置，跳过Yelp数据获取');
      return [];
    }

    const params = this.buildYelpSearchParams(options);
    
    const response = await this.makeRateLimitedRequest(
      `${this.yelpApiUrl}/businesses/search?${params.toString()}`,
      'GET',
      {
        headers: {
          'Authorization': `Bearer ${this.yelpApiKey}`,
          'Accept': 'application/json'
        }
      },
      'yelp'
    );

    return this.processYelpResults(response.businesses || []);
  }

  /**
   * 🏗️ 构建Yelp搜索参数
   */
  private buildYelpSearchParams(options: RestaurantSearchOptions): URLSearchParams {
    const params = new URLSearchParams({
      latitude: options.latitude.toString(),
      longitude: options.longitude.toString(),
      radius: Math.min(options.radius || 2000, 40000).toString(), // Yelp最大40km
      categories: 'restaurants,food',
      limit: Math.min(options.limit || 20, 50).toString(), // Yelp最大50
      sort_by: 'best_match'
    });

    if (options.priceLevel && options.priceLevel.length > 0) {
      params.append('price', options.priceLevel.join(','));
    }

    return params;
  }

  /**
   * 🔄 处理Yelp结果
   */
  private processYelpResults(businesses: any[]): RestaurantData[] {
    return businesses.map(business => ({
      id: `yelp_${business.id}`,
      name: business.name || '未知餐厅',
      latitude: business.coordinates?.latitude || 0,
      longitude: business.coordinates?.longitude || 0,
      address: this.buildYelpAddress(business.location),
      phone: business.phone,
      website: business.url,
      cuisine: this.parseYelpCategories(business.categories),
      priceLevel: this.parseYelpPriceLevel(business.price),
      rating: business.rating,
      reviewCount: business.review_count,
      photos: business.photos || [],
      features: this.parseYelpFeatures(business),
      source: [{
        provider: 'yelp',
        confidence: 0.9,
        lastUpdated: new Date()
      }],
      lastUpdated: new Date(),
      confidence: 0.9
    })).filter(restaurant => restaurant.name !== '未知餐厅');
  }

  // ===== 数据合并方法 =====

  /**
   * 🔗 合并餐厅数据
   */
  private mergeRestaurantData(
    osmData: RestaurantData[],
    yelpData: RestaurantData[]
  ): RestaurantData[] {
    
    const merged: RestaurantData[] = [];
    const processedYelp = new Set<string>();

    // 1. 尝试匹配OSM和Yelp数据
    for (const osmRestaurant of osmData) {
      let bestMatch: RestaurantData | null = null;
      let bestScore = 0;

      for (const yelpRestaurant of yelpData) {
        if (processedYelp.has(yelpRestaurant.id)) continue;

        const score = this.calculateMatchScore(osmRestaurant, yelpRestaurant);
        if (score > bestScore && score > 0.7) {
          bestMatch = yelpRestaurant;
          bestScore = score;
        }
      }

      if (bestMatch) {
        // 合并数据
        const mergedRestaurant = this.mergeRestaurantDetails(osmRestaurant, bestMatch);
        merged.push(mergedRestaurant);
        processedYelp.add(bestMatch.id);
      } else {
        // 只有OSM数据
        merged.push(osmRestaurant);
      }
    }

    // 2. 添加未匹配的Yelp数据
    for (const yelpRestaurant of yelpData) {
      if (!processedYelp.has(yelpRestaurant.id)) {
        merged.push(yelpRestaurant);
      }
    }

    return merged;
  }

  /**
   * 📊 计算匹配分数
   */
  private calculateMatchScore(restaurant1: RestaurantData, restaurant2: RestaurantData): number {
    let score = 0;

    // 名称相似度 (40%)
    const nameScore = this.calculateStringSimilarity(
      restaurant1.name.toLowerCase(),
      restaurant2.name.toLowerCase()
    );
    score += nameScore * 0.4;

    // 距离相似度 (40%)
    const distance = this.calculateDistance(
      restaurant1.latitude, restaurant1.longitude,
      restaurant2.latitude, restaurant2.longitude
    );
    const distanceScore = Math.max(0, 1 - distance / 100); // 100米内认为是同一家
    score += distanceScore * 0.4;

    // 地址相似度 (20%)
    const addressScore = this.calculateStringSimilarity(
      restaurant1.address.toLowerCase(),
      restaurant2.address.toLowerCase()
    );
    score += addressScore * 0.2;

    return score;
  }

  /**
   * 🔗 合并餐厅详情
   */
  private mergeRestaurantDetails(
    osmRestaurant: RestaurantData,
    yelpRestaurant: RestaurantData
  ): RestaurantData {
    
    return {
      id: `merged_${osmRestaurant.id}_${yelpRestaurant.id}`,
      name: yelpRestaurant.name || osmRestaurant.name,
      latitude: yelpRestaurant.latitude || osmRestaurant.latitude,
      longitude: yelpRestaurant.longitude || osmRestaurant.longitude,
      address: yelpRestaurant.address || osmRestaurant.address,
      phone: yelpRestaurant.phone || osmRestaurant.phone,
      website: yelpRestaurant.website || osmRestaurant.website,
      cuisine: [...new Set([...osmRestaurant.cuisine, ...yelpRestaurant.cuisine])],
      priceLevel: yelpRestaurant.priceLevel || osmRestaurant.priceLevel,
      rating: yelpRestaurant.rating || osmRestaurant.rating,
      reviewCount: yelpRestaurant.reviewCount || osmRestaurant.reviewCount,
      openingHours: osmRestaurant.openingHours || yelpRestaurant.openingHours,
      features: [...osmRestaurant.features, ...yelpRestaurant.features],
      photos: yelpRestaurant.photos || osmRestaurant.photos,
      description: yelpRestaurant.description || osmRestaurant.description,
      source: [...osmRestaurant.source, ...yelpRestaurant.source],
      lastUpdated: new Date(),
      confidence: Math.max(osmRestaurant.confidence, yelpRestaurant.confidence)
    };
  }

  // ===== 辅助方法 =====

  /**
   * 🚦 限速请求
   */
  private async makeRateLimitedRequest(
    url: string,
    method: 'GET' | 'POST' = 'GET',
    options: any = {},
    provider: 'osm' | 'yelp'
  ): Promise<any> {
    
    const interval = provider === 'osm' ? this.osmInterval : this.yelpInterval;
    const lastRequest = provider === 'osm' ? this.lastOSMRequest : this.lastYelpRequest;
    
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequest;
    
    if (timeSinceLastRequest < interval) {
      await this.delay(interval - timeSinceLastRequest);
    }

    const response = await fetch(url, {
      method,
      headers: {
        'User-Agent': 'LetsTravel/1.0 (<EMAIL>)',
        'Accept': 'application/json',
        ...options.headers
      },
      ...options
    });

    if (provider === 'osm') {
      this.lastOSMRequest = Date.now();
    } else {
      this.lastYelpRequest = Date.now();
    }

    if (!response.ok) {
      throw new Error(`${provider.toUpperCase()} API错误: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 🔄 过滤和排序结果
   */
  private filterAndSortResults(
    results: RestaurantData[],
    options: RestaurantSearchOptions
  ): RestaurantData[] {
    
    let filtered = results;

    // 按评分过滤
    if (options.rating) {
      filtered = filtered.filter(r => r.rating && r.rating >= options.rating!);
    }

    // 按价格等级过滤
    if (options.priceLevel && options.priceLevel.length > 0) {
      filtered = filtered.filter(r => options.priceLevel!.includes(r.priceLevel));
    }

    // 排序：评分 > 评价数量 > 置信度
    filtered.sort((a, b) => {
      if (a.rating && b.rating && a.rating !== b.rating) {
        return b.rating - a.rating;
      }
      if (a.reviewCount && b.reviewCount && a.reviewCount !== b.reviewCount) {
        return b.reviewCount - a.reviewCount;
      }
      return b.confidence - a.confidence;
    });

    // 限制数量
    return filtered.slice(0, options.limit || 20);
  }

  // ===== 解析方法 =====

  private parseOSMCuisine(cuisine?: string): string[] {
    if (!cuisine) return [];
    return cuisine.split(/[;,]/).map(c => c.trim()).filter(c => c.length > 0);
  }

  private parseOSMPriceLevel(tags: any): 1 | 2 | 3 | 4 {
    // 简化的价格等级判断
    return 2; // 默认中等价位
  }

  private parseOSMOpeningHours(hours?: string): OpeningHours | undefined {
    if (!hours) return undefined;
    // 简化实现，实际需要解析复杂的OSM开放时间格式
    return { monday: hours };
  }

  private parseOSMFeatures(tags: any): RestaurantFeature[] {
    const features: RestaurantFeature[] = [];
    
    if (tags.delivery === 'yes') {
      features.push({ type: FeatureType.DELIVERY, value: true, source: 'osm' });
    }
    if (tags.takeaway === 'yes') {
      features.push({ type: FeatureType.TAKEOUT, value: true, source: 'osm' });
    }
    if (tags.outdoor_seating === 'yes') {
      features.push({ type: FeatureType.OUTDOOR_SEATING, value: true, source: 'osm' });
    }
    if (tags.wifi === 'yes' || tags.wifi === 'free') {
      features.push({ type: FeatureType.WIFI, value: true, source: 'osm' });
    }

    return features;
  }

  private buildOSMAddress(tags: any): string {
    const parts = [];
    if (tags['addr:housenumber']) parts.push(tags['addr:housenumber']);
    if (tags['addr:street']) parts.push(tags['addr:street']);
    if (tags['addr:city']) parts.push(tags['addr:city']);
    return parts.join(' ') || '地址未知';
  }

  private parseYelpCategories(categories: any[]): string[] {
    if (!categories) return [];
    return categories.map(cat => cat.title || cat.alias).filter(Boolean);
  }

  private parseYelpPriceLevel(price?: string): 1 | 2 | 3 | 4 {
    if (!price) return 2;
    return price.length as 1 | 2 | 3 | 4;
  }

  private parseYelpFeatures(business: any): RestaurantFeature[] {
    const features: RestaurantFeature[] = [];
    
    if (business.transactions?.includes('delivery')) {
      features.push({ type: FeatureType.DELIVERY, value: true, source: 'yelp' });
    }
    if (business.transactions?.includes('pickup')) {
      features.push({ type: FeatureType.TAKEOUT, value: true, source: 'yelp' });
    }

    return features;
  }

  private buildYelpAddress(location: any): string {
    if (!location) return '地址未知';
    return location.display_address?.join(', ') || '地址未知';
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // 地球半径（米）
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  private generateCacheKey(operation: string, options: any): string {
    return `${operation}_${JSON.stringify(options)}`.replace(/[^a-zA-Z0-9_]/g, '_');
  }

  private async cacheResult(key: string, data: any): Promise<void> {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        ttl: this.cacheTTL
      };
      
      await AsyncStorage.setItem(
        `${this.cachePrefix}${key}`,
        JSON.stringify(cacheData)
      );
    } catch (error) {
      console.warn('缓存保存失败:', error);
    }
  }

  private async getCachedResult(key: string): Promise<any> {
    try {
      const cached = await AsyncStorage.getItem(`${this.cachePrefix}${key}`);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const now = Date.now();

      if (now - cacheData.timestamp > cacheData.ttl) {
        await AsyncStorage.removeItem(`${this.cachePrefix}${key}`);
        return null;
      }

      return cacheData.data;
    } catch (error) {
      console.warn('缓存读取失败:', error);
      return null;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async getOSMRestaurantDetails(restaurantId: string): Promise<RestaurantData | null> {
    // 实现OSM餐厅详情获取
    return null;
  }

  private async getYelpRestaurantDetails(restaurantId: string): Promise<RestaurantData | null> {
    // 实现Yelp餐厅详情获取
    return null;
  }
}

export default RestaurantDataService;
