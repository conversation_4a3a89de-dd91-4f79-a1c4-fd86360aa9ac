/**
 * Trekmate 4.0 - 活动卡片组件
 * 用于显示行程中的单个活动
 */

import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  Animated,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { TrulyUnifiedBudgetEngine } from '../../utils/TrulyUnifiedBudgetEngine';

// 启用LayoutAnimation (Android需要)
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

import { Card } from '../ui/Card';
import { colors, spacing, borderRadius } from '../../constants/Theme';
import type { Activity } from '../../types/CoreServices';
import { TimeFormatter } from '../../utils/TimeFormatter';
import { ExpandedContentIntegrator, ContentIntegrationOptions } from '../../utils/ExpandedContentIntegrator';
import EnhancedExpandedContent from '../activity/EnhancedExpandedContent';

interface ActivityCardProps {
  activity: Activity;
  onPress?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
  style?: ViewStyle;
  // 新增属性
  expandable?: boolean;
  defaultExpanded?: boolean;
  showWeather?: boolean;
  weather?: {
    icon: string;
    temperature: number;
    description: string;
  };
  showBudgetBreakdown?: boolean;
  budgetBreakdown?: {
    category: string;
    amount: number;
    currency: string;
  }[];
  // 🌟 增强展开内容属性
  useEnhancedContent?: boolean;
  cardFormat?: 'classic' | 'modern';
  contentIntegrationOptions?: ContentIntegrationOptions;
}

export default function ActivityCard({
  activity,
  onPress,
  onEdit,
  onDelete,
  showActions = false,
  style,
  expandable = true,
  defaultExpanded = false,
  showWeather = false,
  weather,
  showBudgetBreakdown = false,
  budgetBreakdown,
  useEnhancedContent = true,
  cardFormat = 'modern',
  contentIntegrationOptions = {},
}: ActivityCardProps) {
  // 展开/折叠状态
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // 🌟 集成增强展开内容
  const integratedContent = useMemo(() => {
    if (!useEnhancedContent) return null;

    return ExpandedContentIntegrator.integrateExpandedContent(activity, {
      preferEnhanced: true,
      fallbackToBasic: true,
      cardFormat,
      ...contentIntegrationOptions
    });
  }, [activity, useEnhancedContent, cardFormat, contentIntegrationOptions]);

  // 🔧 统一预算计算
  const unifiedBudget = useMemo(() => {
    try {
      return TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(activity);
    } catch (error) {
      console.warn('统一预算计算失败，使用原始数据:', error);
      return {
        displayAmount: activity.cost || 0,
        displayCurrency: '¥',
        isFree: (activity.cost || 0) === 0,
        priceLevel: 'unknown',
        budgetTier: 'unknown',
        costBreakdown: [],
        source: 'fallback'
      };
    }
  }, [activity]);

  // 处理展开/折叠 - 优化动画
  const toggleExpanded = () => {
    if (!expandable) return;

    // 使用自定义动画配置
    LayoutAnimation.configureNext({
      duration: 300,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.scaleXY,
      },
      delete: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });

    setIsExpanded(!isExpanded);
  };

  // 🔧 使用新的时间格式化工具
  const getFormattedTime = () => {
    return TimeFormatter.formatActivityTimeRange(activity.startTime, activity.endTime);
  };

  // 🔧 使用新的持续时间格式化
  const getFormattedDuration = () => {
    if (activity.duration) {
      return TimeFormatter.formatDuration(activity.duration);
    }
    return '';
  };

  // 🔧 获取时间段描述
  const getTimePeriod = () => {
    return TimeFormatter.getTimePeriod(activity.startTime);
  };

  // 获取活动类型图标
  const getActivityIcon = (type: string) => {
    const iconMap: Record<string, string> = {
      'sightseeing': 'camera',
      'dining': 'restaurant',
      'shopping': 'bag',
      'entertainment': 'musical-notes',
      'transport': 'car',
      'accommodation': 'bed',
      'other': 'ellipse',
    };
    return iconMap[type] || 'location';
  };

  // 获取活动类型颜色
  const getActivityColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'sightseeing': colors.primary[500],
      'dining': colors.success,
      'shopping': colors.warning,
      'entertainment': colors.info,
      'transport': colors.secondary[500],
      'accommodation': colors.purple[500],
      'other': colors.textSecondary,
    };
    return colorMap[type] || colors.primary[500];
  };

  return (
    <Card style={[styles.container, style]}>
      {/* 主要内容区域 - 始终可见 */}
      <TouchableOpacity
        style={styles.content}
        onPress={expandable ? toggleExpanded : onPress}
        activeOpacity={0.7}
      >
        {/* 时间线指示器 */}
        <View style={styles.timeline}>
          <View style={[
            styles.timelineIcon,
            { backgroundColor: getActivityColor(activity.type) }
          ]}>
            <Ionicons 
              name={getActivityIcon(activity.type) as any} 
              size={16} 
              color={colors.surface} 
            />
          </View>
          <View style={styles.timelineLine} />
        </View>

        {/* 活动内容 */}
        <View style={styles.activityContent}>
          {/* 头部信息 */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <View style={styles.titleRow}>
                <Text style={styles.title}>{activity.title}</Text>
                {expandable && (
                  <Ionicons
                    name={isExpanded ? "chevron-up" : "chevron-down"}
                    size={16}
                    color={colors.textSecondary}
                    style={styles.expandIcon}
                  />
                )}
              </View>

              <View style={styles.timeRow}>
                <Text style={styles.time}>
                  {getFormattedTime()}
                </Text>
                {getFormattedDuration() && (
                  <Text style={styles.duration}>
                    {getFormattedDuration()}
                  </Text>
                )}

                {/* 天气信息 - 简化显示 */}
                {showWeather && weather && (
                  <View style={styles.weatherBadge}>
                    <Text style={styles.weatherIcon}>{weather.icon}</Text>
                    <Text style={styles.weatherTemp}>{weather.temperature}°</Text>
                  </View>
                )}
              </View>
            </View>

            {activity.duration && (
              <View style={styles.durationBadge}>
                <Text style={styles.durationText}>
                  {formatDuration(activity.duration)}
                </Text>
              </View>
            )}
          </View>

          {/* 简化描述 - 始终显示 */}
          {activity.description && (
            <Text style={styles.description} numberOfLines={isExpanded ? undefined : 1}>
              {activity.description}
            </Text>
          )}

          {/* 基本费用信息 - 始终显示 */}
          {true && (
            <View style={styles.costContainer}>
              <Ionicons name={unifiedBudget.isFree ? "gift" : "wallet"} size={14} color={unifiedBudget.isFree ? colors.success[500] : colors.textSecondary} />
              <Text style={[
                styles.costText,
                unifiedBudget.isFree && styles.freeCostText
              ]}>
                {unifiedBudget.isFree ? '免费' : `${unifiedBudget.displayCurrency}${unifiedBudget.displayAmount.toLocaleString()}`}
              </Text>
              {unifiedBudget.priceLevel !== 'unknown' && (
                <Text style={styles.priceLevelText}>
                  {unifiedBudget.priceLevel === 'budget' ? '经济' : 
                   unifiedBudget.priceLevel === 'moderate' ? '中等' : 
                   unifiedBudget.priceLevel === 'premium' ? '高端' : ''}
                </Text>
              )}
            </View>
          )}
        </View>

        {/* 操作按钮 */}
        {showActions && (
          <View style={styles.actions}>
            {onEdit && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={onEdit}
              >
                <Ionicons name="create" size={16} color={colors.primary[500]} />
              </TouchableOpacity>
            )}

            {onDelete && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={onDelete}
              >
                <Ionicons name="trash" size={16} color={colors.error} />
              </TouchableOpacity>
            )}
          </View>
        )}
      </TouchableOpacity>

      {/* 🌟 智能展开内容区域 */}
      {isExpanded && (
        <View style={styles.expandedContent}>
          {integratedContent?.useEnhanced ? (
            // 使用增强展开内容组件
            <EnhancedExpandedContent
              activity={integratedContent.activityData}
              apiData={integratedContent.apiData}
              cardFormat={cardFormat}
            />
          ) : (
            // 使用基础展开内容
            <>
              {/* 详细位置信息 */}
              {activity.location && (
                <View style={styles.detailSection}>
                  <View style={styles.detailHeader}>
                    <Ionicons name="location" size={16} color={colors.primary[500]} />
                    <Text style={styles.detailTitle}>位置信息</Text>
                  </View>
                  <Text style={styles.detailText}>{activity.location.address}</Text>
                  {activity.location.coordinates && (
                    <Text style={styles.coordinatesText}>
                      {activity.location.coordinates.latitude.toFixed(6)}, {activity.location.coordinates.longitude.toFixed(6)}
                    </Text>
                  )}
                </View>
              )}
            </>
          )}

          {/* 集成信息显示 */}
          {integratedContent && (
            <View style={styles.integrationInfo}>
              <Text style={styles.integrationText}>
                💡 {integratedContent.integrationReason}
              </Text>
            </View>
          )}

          {/* 预算明细 - 使用统一预算引擎 */}
          {showBudgetBreakdown && (unifiedBudget.costBreakdown.length > 0 || (budgetBreakdown && budgetBreakdown.length > 0)) && (
            <View style={styles.detailSection}>
              <View style={styles.detailHeader}>
                <Ionicons name="receipt" size={16} color={colors.primary[500]} />
                <Text style={styles.detailTitle}>费用明细</Text>
              </View>
              {/* 优先使用统一预算引擎的成本分解 */}
              {(unifiedBudget.costBreakdown.length > 0 ? unifiedBudget.costBreakdown : budgetBreakdown || []).map((item, index) => (
                <View key={index} style={styles.budgetItem}>
                  <Text style={styles.budgetCategory}>
                    {item.category || item.description || '费用项目'}
                  </Text>
                  <Text style={styles.budgetAmount}>
                    {item.currency || unifiedBudget.displayCurrency}{(item.amount || 0).toLocaleString()}
                  </Text>
                  {item.source && (
                    <Text style={styles.budgetSource}>
                      来源: {item.source}
                    </Text>
                  )}
                </View>
              ))}
            </View>
          )}

          {/* 详细天气信息 */}
          {showWeather && weather && (
            <View style={styles.detailSection}>
              <View style={styles.detailHeader}>
                <Text style={styles.weatherIcon}>{weather.icon}</Text>
                <Text style={styles.detailTitle}>天气信息</Text>
              </View>
              <Text style={styles.detailText}>
                {weather.temperature}°C - {weather.description}
              </Text>
            </View>
          )}

          {/* 状态指示器 */}
          {activity.isCompleted && (
            <View style={styles.completedBadge}>
              <Ionicons name="checkmark-circle" size={16} color={colors.success} />
              <Text style={styles.completedText}>已完成</Text>
            </View>
          )}
        </View>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing[4],
  },
  timeline: {
    alignItems: 'center',
    marginRight: spacing[3],
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing[1],
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: colors.border,
    minHeight: 20,
  },
  activityContent: {
    flex: 1,
    gap: spacing[2],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing[1],
  },
  expandIcon: {
    marginLeft: spacing[2],
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  weatherBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    marginLeft: spacing[2],
  },
  weatherIcon: {
    fontSize: 12,
    marginRight: spacing[1],
  },
  weatherTemp: {
    fontSize: 10,
    color: colors.primary[700],
    fontWeight: '500',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[1],
  },
  time: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  durationBadge: {
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.full,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    marginLeft: spacing[2],
  },
  durationText: {
    fontSize: 10,
    color: colors.primary[700],
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  locationText: {
    fontSize: 12,
    color: colors.textSecondary,
    flex: 1,
  },
  costContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  costText: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  freeCostText: {
    color: colors.success[500],
    fontWeight: '600',
  },
  priceLevelText: {
    fontSize: 10,
    color: colors.textSecondary,
    marginLeft: spacing[1],
    fontStyle: 'italic',
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
    marginTop: spacing[1],
  },
  completedText: {
    fontSize: 12,
    color: colors.success,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'column',
    gap: spacing[2],
    marginLeft: spacing[2],
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // 新增样式
  expandedContent: {
    paddingHorizontal: spacing[4],
    paddingBottom: spacing[4],
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.background,
  },
  detailSection: {
    marginBottom: spacing[3],
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  detailTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginLeft: spacing[2],
  },
  detailText: {
    fontSize: 13,
    color: colors.textSecondary,
    lineHeight: 18,
    marginLeft: spacing[6], // 对齐图标
  },
  coordinatesText: {
    fontSize: 11,
    color: colors.textTertiary,
    fontFamily: 'monospace',
    marginLeft: spacing[6],
    marginTop: spacing[1],
  },
  budgetItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[1],
    marginLeft: spacing[6],
  },
  budgetCategory: {
    fontSize: 13,
    color: colors.textSecondary,
    flex: 1,
  },
  budgetAmount: {
    fontSize: 13,
    color: colors.text,
    fontWeight: '500',
  },
  budgetSource: {
    fontSize: 10,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginTop: 2,
  },
  // 🌟 集成信息样式
  integrationInfo: {
    marginTop: spacing[2],
    padding: spacing[2],
    backgroundColor: colors.background,
    borderRadius: borderRadius.sm,
    borderLeftWidth: 3,
    borderLeftColor: colors.primary[500],
  },
  integrationText: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
});