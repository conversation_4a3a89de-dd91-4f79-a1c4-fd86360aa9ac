/**
 * 🔧 修复版DayCard组件
 * 直接实现用户期望的显示格式，解决所有显示问题
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface FixedDayCardProps {
  dayNumber: number;
  activities: any[];
  totalBudget?: number;
  currency?: string;
  weather?: string;
  date?: string;
  onActivityPress?: (activity: any) => void;
}

export default function FixedDayCard({
  dayNumber,
  activities,
  totalBudget = 0,
  currency = 'MYR',
  weather = '晴朗 8°C',
  date,
  onActivityPress
}: FixedDayCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // 🔧 安全获取活动名称
  const getActivityName = (activity: any): string => {
    return activity.name || activity.title || activity.activityName || '未命名活动';
  };

  // 🔧 安全获取活动类型
  const getActivityType = (activity: any): string => {
    const name = getActivityName(activity).toLowerCase();
    const description = (activity.description || '').toLowerCase();
    const text = `${name} ${description}`;
    
    if (text.includes('餐') || text.includes('食') || text.includes('lunch') || text.includes('dinner')) {
      return '美食';
    }
    if (text.includes('地铁') || text.includes('巴士') || text.includes('taxi') || text.includes('transport')) {
      return '交通';
    }
    if (text.includes('酒店') || text.includes('hotel') || text.includes('住宿')) {
      return '住宿';
    }
    return '景点';
  };

  // 🔧 安全获取时间范围
  const getTimeRange = (activity: any): string => {
    const startTime = activity.startTime || activity.timing?.startTime || '09:00';
    const endTime = activity.endTime || activity.timing?.endTime || '10:00';

    // 🔧 确保时间格式正确，添加安全检查
    const formatTime = (time: any) => {
      // 🔧 确保time是字符串类型
      const safeTime = String(time || '09:00');

      if (safeTime.includes(':')) {
        const [hours, minutes] = safeTime.split(':');
        return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
      }
      return safeTime;
    };

    return `${formatTime(startTime)} - ${formatTime(endTime)}`;
  };

  // 🔧 安全获取活动费用
  const getActivityCost = (activity: any): string => {
    const cost = activity.cost || 0;
    if (typeof cost === 'number') {
      return cost === 0 ? '$0' : `${currency}${cost}`;
    }
    if (cost.amount !== undefined) {
      return cost.amount === 0 ? '$0' : `${currency}${cost.amount}`;
    }
    return '$0';
  };

  // 🔧 安全的按时间排序活动
  const sortedActivities = [...activities].sort((a, b) => {
    const timeA = a.startTime || a.timing?.startTime || '09:00';
    const timeB = b.startTime || b.timing?.startTime || '09:00';

    // 🔧 确保时间是字符串类型
    const safeTimeA = String(timeA || '09:00');
    const safeTimeB = String(timeB || '09:00');

    return safeTimeA.localeCompare(safeTimeB);
  });

  return (
    <View style={styles.container}>
      {/* 📅 头部：Day 1 + 天气 + 总预算 */}
      <TouchableOpacity style={styles.header} onPress={() => setIsExpanded(!isExpanded)}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.dayTitle}>Day {dayNumber}</Text>
            {date && <Text style={styles.date}>{date}</Text>}
          </View>
          
          <View style={styles.headerCenter}>
            <Text style={styles.weather}>{weather}</Text>
          </View>
          
          <View style={styles.headerRight}>
            <Text style={styles.totalBudget}>{currency}{totalBudget}</Text>
            <Ionicons 
              name={isExpanded ? "chevron-up" : "chevron-down"} 
              size={20} 
              color="#666" 
            />
          </View>
        </View>
      </TouchableOpacity>

      {/* 🕐 活动时间线 */}
      <View style={styles.activitiesContainer}>
        {sortedActivities.map((activity, index) => (
          <TouchableOpacity 
            key={index} 
            style={styles.activityItem}
            onPress={() => onActivityPress?.(activity)}
          >
            {/* 未展开：简洁格式 */}
            <View style={styles.compactActivity}>
              <Text style={styles.timeRange}>{getTimeRange(activity)}</Text>
              <Text style={styles.activityName}>
                ({getActivityType(activity)}){getActivityName(activity)}
              </Text>
              {!isExpanded && (
                <Text style={styles.activityCost}>{getActivityCost(activity)}</Text>
              )}
            </View>

            {/* 展开：详细格式 */}
            {isExpanded && (
              <View style={styles.expandedActivity}>
                <View style={styles.expandedHeader}>
                  <Text style={styles.expandedTime}>{getTimeRange(activity)}</Text>
                  <Text style={styles.expandedName}>
                    ({getActivityType(activity)}){getActivityName(activity)}
                  </Text>
                  <Text style={styles.expandedCost}>{getActivityCost(activity)}</Text>
                </View>
                
                {/* 天气信息 */}
                <View style={styles.weatherInfo}>
                  <Text style={styles.weatherTime}>({getTimeRange(activity)}的天气)</Text>
                  <Text style={styles.weatherAdvice}>
                    {weather.includes('晴') ? '阳光充足，适合户外活动和拍照' : 
                     weather.includes('雨') ? '可能有小雨，建议携带雨具' : 
                     `${weather}，适合外出游览`}
                  </Text>
                </View>

                {/* 活动描述 */}
                <Text style={styles.activityDescription}>
                  {activity.description || `探索${getActivityName(activity)}，体验当地文化和历史`}
                </Text>

                {/* 实用建议 */}
                <View style={styles.tipsContainer}>
                  <Text style={styles.tip}>
                    *注 最佳游览时间：{getTimeRange(activity)}
                  </Text>
                  <Text style={styles.tip}>
                    *注 建议提前了解文化背景
                  </Text>
                </View>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  // 📅 头部样式
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  dayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  weather: {
    fontSize: 14,
    color: '#FF9500',
    fontWeight: '500',
  },
  headerRight: {
    flex: 1,
    alignItems: 'flex-end',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  totalBudget: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    marginRight: 8,
  },
  
  // 🕐 活动样式
  activitiesContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  activityItem: {
    marginTop: 12,
  },
  compactActivity: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeRange: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
    minWidth: 100,
  },
  activityName: {
    fontSize: 16,
    color: '#333',
    flex: 1,
    marginLeft: 12,
  },
  activityCost: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  
  // 🎨 展开样式
  expandedActivity: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  expandedHeader: {
    marginBottom: 12,
  },
  expandedTime: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  expandedName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  expandedCost: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  
  // 🌤️ 天气信息
  weatherInfo: {
    backgroundColor: '#FFF8E1',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  weatherTime: {
    fontSize: 12,
    color: '#F57C00',
    fontWeight: '600',
  },
  weatherAdvice: {
    fontSize: 14,
    color: '#F57C00',
    marginTop: 4,
    lineHeight: 20,
  },
  
  // 📝 描述和建议
  activityDescription: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 12,
  },
  tipsContainer: {
    marginBottom: 8,
  },
  tip: {
    fontSize: 13,
    color: '#666',
    marginBottom: 4,
    fontStyle: 'italic',
  },
});
