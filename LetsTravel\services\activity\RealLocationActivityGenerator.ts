/**
 * 🏷️ 真实地点活动生成器
 * 
 * 生成包含真实地点名称、本地化名称、精确分类和验证状态的活动
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import { 
  RealLocationActivity, 
  ActivityType, 
  LocationType, 
  PrimaryCategoryType,
  VerificationLevel 
} from '../../types/RealLocationActivity';

/**
 * 🎯 真实地点活动生成器
 */
export class RealLocationActivityGenerator {
  
  /**
   * 🏗️ 生成真实地点活动
   */
  static generateRealLocationActivity(
    activityName: string,
    location: string,
    type: ActivityType = ActivityType.ATTRACTION
  ): RealLocationActivity {
    
    // 1. 解析真实地点信息
    const realLocation = this.parseRealLocation(activityName, location);
    
    // 2. 生成本地化名称
    const localizedNames = this.generateLocalizedNames(activityName, realLocation);
    
    // 3. 确定精确分类
    const preciseCategory = this.determinePreciseCategory(activityName, type);
    
    // 4. 设置验证状态
    const verificationStatus = this.createVerificationStatus();
    
    // 5. 计算时间和费用
    const timing = this.calculateTiming(type, preciseCategory);
    const cost = this.calculateCost(type, preciseCategory);
    
    // 6. 生成元数据
    const metadata = this.generateMetadata(activityName, type);
    
    return {
      id: `real_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      realLocation,
      localizedNames,
      preciseCategory,
      verificationStatus,
      timing,
      cost,
      metadata
    };
  }
  
  /**
   * 🗺️ 解析真实地点信息
   */
  private static parseRealLocation(activityName: string, location: string) {
    // 真实地点映射数据库
    const realLocationMap: Record<string, any> = {
      '浅草寺': {
        officialName: '浅草寺',
        officialNameEn: 'Sensoji Temple',
        address: {
          full: '东京都台东区浅草2-3-1',
          district: '台东区',
          area: '浅草',
          postalCode: '111-0032'
        },
        coordinates: { lat: 35.7148, lng: 139.7967, accuracy: 'high' },
        locationType: LocationType.TEMPLE,
        operatingInfo: {
          hours: '06:00-17:00',
          closedDays: [],
          seasonalHours: {}
        },
        contact: {
          website: 'https://www.senso-ji.jp/',
          phone: '+81-3-3842-0181'
        }
      },
      '东京晴空塔': {
        officialName: '东京晴空塔',
        officialNameEn: 'Tokyo Skytree',
        address: {
          full: '东京都墨田区押上1-1-2',
          district: '墨田区',
          area: '押上',
          postalCode: '131-0045'
        },
        coordinates: { lat: 35.7101, lng: 139.8107, accuracy: 'high' },
        locationType: LocationType.LANDMARK,
        operatingInfo: {
          hours: '08:00-22:00',
          closedDays: [],
          seasonalHours: {}
        },
        contact: {
          website: 'https://www.tokyo-skytree.jp/',
          phone: '+81-570-55-0634'
        }
      },
      '筑地外市场': {
        officialName: '筑地外市场',
        officialNameEn: 'Tsukiji Outer Market',
        address: {
          full: '东京都中央区筑地4-16-2',
          district: '中央区',
          area: '筑地',
          postalCode: '104-0045'
        },
        coordinates: { lat: 35.6654, lng: 139.7707, accuracy: 'high' },
        locationType: LocationType.MARKET,
        operatingInfo: {
          hours: '05:00-14:00',
          closedDays: ['周日', '节假日'],
          seasonalHours: {}
        }
      }
    };
    
    // 查找匹配的真实地点
    for (const [key, data] of Object.entries(realLocationMap)) {
      if (activityName.includes(key) || location.includes(key)) {
        return data;
      }
    }
    
    // 默认地点信息
    return {
      officialName: this.extractLocationName(activityName),
      officialNameEn: this.translateToEnglish(activityName),
      address: {
        full: location || '地址待确认',
        district: '待确认',
        area: '待确认'
      },
      coordinates: { lat: 35.6762, lng: 139.6503, accuracy: 'low' },
      locationType: LocationType.LANDMARK,
      operatingInfo: {
        hours: '09:00-17:00',
        closedDays: []
      }
    };
  }
  
  /**
   * 🌍 生成本地化名称
   */
  private static generateLocalizedNames(activityName: string, realLocation: any) {
    return {
      zh: {
        primary: realLocation.officialName,
        alternative: [activityName],
        description: this.generateChineseDescription(realLocation)
      },
      en: {
        primary: realLocation.officialNameEn,
        alternative: [this.translateToEnglish(activityName)],
        description: this.generateEnglishDescription(realLocation)
      },
      ja: {
        primary: realLocation.officialName,
        hiragana: this.convertToHiragana(realLocation.officialName),
        romaji: this.convertToRomaji(realLocation.officialName)
      }
    };
  }
  
  /**
   * 🎯 确定精确分类
   */
  private static determinePreciseCategory(activityName: string, type: ActivityType) {
    const categoryMap: Record<string, any> = {
      '寺': {
        primary: PrimaryCategoryType.RELIGIOUS,
        secondary: ['佛教寺庙', '历史建筑', '文化遗产'],
        tags: ['寺庙', '历史', '文化', '建筑', '宗教'],
        features: ['建筑', '庭园', '文化体验'],
        suitableFor: ['家庭', '文化爱好者', '摄影师'],
        bestTime: {
          season: ['春季', '秋季'],
          timeOfDay: ['早晨', '傍晚'],
          duration: 90
        }
      },
      '塔': {
        primary: PrimaryCategoryType.ARCHITECTURAL,
        secondary: ['观景台', '现代建筑', '地标'],
        tags: ['塔楼', '观景', '现代', '地标'],
        features: ['观景台', '餐厅', '购物'],
        suitableFor: ['游客', '摄影师', '情侣'],
        bestTime: {
          season: ['全年'],
          timeOfDay: ['傍晚', '夜晚'],
          duration: 120
        }
      },
      '市场': {
        primary: PrimaryCategoryType.COMMERCIAL,
        secondary: ['传统市场', '美食', '购物'],
        tags: ['市场', '美食', '传统', '购物'],
        features: ['新鲜食材', '传统小吃', '文化体验'],
        suitableFor: ['美食爱好者', '文化体验者'],
        bestTime: {
          season: ['全年'],
          timeOfDay: ['早晨'],
          duration: 60
        }
      }
    };
    
    // 查找匹配的分类
    for (const [key, category] of Object.entries(categoryMap)) {
      if (activityName.includes(key)) {
        return category;
      }
    }
    
    // 默认分类
    return {
      primary: PrimaryCategoryType.CULTURAL,
      secondary: ['文化体验'],
      tags: ['文化', '体验'],
      features: ['参观', '学习'],
      suitableFor: ['游客'],
      bestTime: {
        season: ['全年'],
        timeOfDay: ['白天'],
        duration: 60
      }
    };
  }
  
  /**
   * ✅ 创建验证状态
   */
  private static createVerificationStatus() {
    return {
      overall: 'partial' as const,
      details: {
        locationAccuracy: VerificationLevel.MEDIUM,
        nameAccuracy: VerificationLevel.HIGH,
        categoryAccuracy: VerificationLevel.HIGH,
        operatingHours: VerificationLevel.MEDIUM,
        contactInfo: VerificationLevel.LOW
      },
      sources: [{
        type: 'system_check' as const,
        confidence: 0.7,
        lastChecked: new Date()
      }],
      lastVerified: new Date(),
      verifiedBy: {
        type: 'system' as const,
        id: 'real_location_generator',
        confidence: 0.7
      }
    };
  }
  
  /**
   * ⏰ 计算时间信息
   */
  private static calculateTiming(type: ActivityType, category: any) {
    return {
      recommendedDuration: category.bestTime.duration,
      timeRange: {
        start: '09:00',
        end: this.addMinutes('09:00', category.bestTime.duration)
      },
      optimalTimes: {
        season: category.bestTime.season,
        timeOfDay: category.bestTime.timeOfDay,
        weekday: true,
        weekend: true
      },
      flexibility: 'flexible' as const
    };
  }
  
  /**
   * 💰 计算费用信息
   */
  private static calculateCost(type: ActivityType, category: any) {
    const costMap: Record<string, number> = {
      [PrimaryCategoryType.RELIGIOUS]: 0,
      [PrimaryCategoryType.COMMERCIAL]: 0,
      [PrimaryCategoryType.ARCHITECTURAL]: 25,
      [PrimaryCategoryType.CULTURAL]: 15,
      [PrimaryCategoryType.HISTORICAL]: 10
    };
    
    const baseCost = costMap[category.primary] || 0;
    
    return {
      base: {
        amount: baseCost,
        currency: 'MYR',
        type: baseCost === 0 ? 'free' as const : 'paid' as const
      },
      breakdown: baseCost > 0 ? {
        admission: baseCost
      } : undefined,
      notes: baseCost === 0 ? ['免费参观'] : [`门票 MYR${baseCost}`]
    };
  }
  
  /**
   * 📊 生成元数据
   */
  private static generateMetadata(activityName: string, type: ActivityType) {
    return {
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'RealLocationActivityGenerator',
      dataSources: ['system_generated', 'location_database'],
      qualityScore: {
        overall: 0.75,
        accuracy: 0.8,
        completeness: 0.7,
        freshness: 1.0,
        relevance: 0.8
      },
      systemFlags: {
        isPopular: false,
        isRecommended: true,
        needsUpdate: false,
        hasIssues: false
      }
    };
  }
  
  // ===== 辅助方法 =====
  
  private static extractLocationName(activityName: string): string {
    return activityName.replace(/体验|参观|游览|探索/g, '').trim();
  }
  
  private static translateToEnglish(chineseName: string): string {
    const translations: Record<string, string> = {
      '浅草寺': 'Sensoji Temple',
      '东京晴空塔': 'Tokyo Skytree',
      '筑地市场': 'Tsukiji Market',
      '银座': 'Ginza',
      '新宿': 'Shinjuku',
      '涩谷': 'Shibuya'
    };
    
    return translations[chineseName] || chineseName;
  }
  
  private static generateChineseDescription(location: any): string {
    const typeDescriptions: Record<string, string> = {
      [LocationType.TEMPLE]: '历史悠久的佛教寺庙',
      [LocationType.LANDMARK]: '著名的城市地标',
      [LocationType.MARKET]: '传统的当地市场',
      [LocationType.MUSEUM]: '文化艺术博物馆'
    };
    
    return typeDescriptions[location.locationType] || '值得参观的地点';
  }
  
  private static generateEnglishDescription(location: any): string {
    const typeDescriptions: Record<string, string> = {
      [LocationType.TEMPLE]: 'Historic Buddhist temple',
      [LocationType.LANDMARK]: 'Famous city landmark',
      [LocationType.MARKET]: 'Traditional local market',
      [LocationType.MUSEUM]: 'Cultural art museum'
    };
    
    return typeDescriptions[location.locationType] || 'Worth visiting location';
  }
  
  private static convertToHiragana(name: string): string {
    const hiraganaMap: Record<string, string> = {
      '浅草寺': 'せんそうじ',
      '東京': 'とうきょう',
      '筑地': 'つきじ'
    };
    
    return hiraganaMap[name] || name;
  }
  
  private static convertToRomaji(name: string): string {
    const romajiMap: Record<string, string> = {
      '浅草寺': 'Sensoji',
      '東京': 'Tokyo',
      '筑地': 'Tsukiji'
    };
    
    return romajiMap[name] || name;
  }
  
  private static addMinutes(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMins = totalMinutes % 60;
    
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  }
}

export default RealLocationActivityGenerator;
