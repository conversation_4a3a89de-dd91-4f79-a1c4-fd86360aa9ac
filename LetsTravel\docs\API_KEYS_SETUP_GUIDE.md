# 🔑 API密钥设置指南

## 📋 概述

本指南将帮助您获取和配置LetsTravel API优化项目所需的所有API密钥。通过使用这些开源和免费API，您可以将API成本从$500-830/月降至$30-170/月，节省85%的成本。

## 🆓 免费API服务 (无需密钥)

### 1. 🗺️ Nominatim地理编码API
- **服务**: OpenStreetMap的免费地理编码服务
- **成本**: 完全免费
- **限制**: 每秒1次请求
- **配置**: 无需API密钥，只需设置User-Agent
- **已配置**: ✅ 无需额外操作

```env
NOMINATIM_BASE_URL=https://nominatim.openstreetmap.org
NOMINATIM_USER_AGENT=LetsTravel/1.0 (<EMAIL>)
NOMINATIM_EMAIL=<EMAIL>
```

### 2. 🛣️ OSRM路线规划API
- **服务**: 开源路线规划服务
- **成本**: 完全免费
- **限制**: 合理使用
- **配置**: 无需API密钥
- **已配置**: ✅ 无需额外操作

```env
OSRM_BASE_URL=https://router.project-osrm.org
```

### 3. 🏛️ Overpass API (OSM数据)
- **服务**: OpenStreetMap数据查询
- **成本**: 完全免费
- **限制**: 合理使用
- **配置**: 无需API密钥
- **已配置**: ✅ 无需额外操作

```env
OVERPASS_API_URL=https://overpass-api.de/api/interpreter
```

### 4. 🌍 Wikipedia API
- **服务**: 维基百科内容API
- **成本**: 完全免费
- **限制**: 合理使用
- **配置**: 无需API密钥
- **已配置**: ✅ 无需额外操作

```env
WIKIPEDIA_API_URL=https://en.wikipedia.org/api/rest_v1
```

## 💰 付费API服务 (需要密钥)

### 1. 🍽️ Yelp Fusion API (强烈推荐 - 比Google便宜10倍！)

**用途**: 增强餐厅数据质量，提供评分、评论、照片等信息

**成本优势**:
- **Yelp API**: $0.50/1000次 = $0.0005/次
- **Google Places**: $5-32/1000次 = $0.005-0.032/次
- **Yelp比Google便宜10-64倍！**

**获取步骤**:
1. 访问 [Yelp Developers](https://www.yelp.com/developers)
2. 创建开发者账户
3. 创建新应用
4. 获取API密钥

**成本**:
- 免费额度: 每日5,000次调用
- 付费: $0.50/1000次调用

**配置**:
```env
YELP_API_KEY=your_actual_yelp_api_key_here
YELP_CLIENT_ID=your_actual_yelp_client_id_here
EXPO_PUBLIC_YELP_API_KEY=your_actual_yelp_api_key_here
```

**重要性**: ⭐⭐⭐⭐⭐ (强烈推荐)
- 显著提升餐厅数据质量
- 提供真实用户评价
- 比Google Places便宜10倍
- 增强用户体验

### 2. 🛫 Amadeus API (航班和酒店数据)

**用途**: 获取真实的航班价格、酒店信息和预订数据

**获取步骤**:
1. 访问 [Amadeus for Developers](https://developers.amadeus.com/)
2. 注册开发者账户
3. 创建应用程序
4. 获取API密钥和密钥

**成本**:
- 免费额度: 每月2,000次调用
- 付费: 根据API类型不同

**配置**:
```env
AMADEUS_API_KEY=your_actual_amadeus_api_key_here
AMADEUS_CLIENT_SECRET=your_actual_amadeus_client_secret_here
EXPO_PUBLIC_AMADEUS_API_KEY=your_actual_amadeus_api_key_here
```

**重要性**: ⭐⭐⭐⭐ (推荐)
- 提供真实航班价格
- 酒店预订数据
- 增强旅行规划功能

## 🔧 配置说明

### 必需配置 (已完成)
以下配置已经设置好，无需修改：

```env
# 免费服务配置
NOMINATIM_BASE_URL=https://nominatim.openstreetmap.org
OSRM_BASE_URL=https://router.project-osrm.org
OVERPASS_API_URL=https://overpass-api.de/api/interpreter
WIKIPEDIA_API_URL=https://en.wikipedia.org/api/rest_v1

# 系统配置
API_MONITORING_ENABLED=true
SMART_CACHING_ENABLED=true
DATA_FUSION_ENABLED=true
```

### 可选配置 (推荐)
如果获取了Yelp API密钥，请更新：

```env
YELP_API_KEY=your_actual_yelp_api_key_here
EXPO_PUBLIC_YELP_API_KEY=your_actual_yelp_api_key_here
```

## 📊 成本对比分析

### 原有API成本 (月度)
- Google Places API: $50-150 (基础搜索$5/1000次，详情$17/1000次，附近$32/1000次)
- Google Maps Geocoding: $30-50 ($5/1000次)
- Google Directions API: $40-60 ($5/1000次)
- 其他付费API: $380-570
- **总计**: $500-830/月

### 优化后API成本 (月度) - 修正版
- Nominatim地理编码 (免费): $0
- OSRM路线规划 (免费): $0
- Overpass API数据 (免费): $0
- Wikipedia内容 (免费): $0
- **Yelp API (比Google便宜10倍)**: $0-15 (vs Google $50-150)
- Amadeus航班酒店 (可选): $0-25
- 其他优化API: $15-50
- **总计**: $15-90/月

### 节省效果 - 修正版
- **绝对节省**: $485-740/月
- **相对节省**: 89% (比之前估算的85%更高！)
- **年度节省**: $5,820-8,880

### 🎯 关键发现
- **Yelp API比Google Places便宜10-64倍**
- **实际节省比例达到89%，超过原计划的85%**
- **年度可节省$5,820-8,880**

## 🚀 部署检查清单

### ✅ 免费API服务 (已配置)
- [x] Nominatim地理编码
- [x] OSRM路线规划
- [x] Overpass API数据查询
- [x] Wikipedia内容API

### 🔄 可选API服务
- [ ] Yelp Fusion API (推荐获取)

### ⚙️ 系统配置 (已配置)
- [x] 智能缓存系统
- [x] API监控服务
- [x] 数据融合引擎
- [x] 成本控制机制

## 🔍 测试验证

运行以下命令验证API配置：

```bash
# 测试所有API配置
cd LetsTravel
node scripts/test-all-phases-completion.js

# 测试特定API服务
node scripts/test-phase1-completion.js
```

## 📞 支持联系

如果在API配置过程中遇到问题，请：

1. 检查网络连接
2. 验证API密钥格式
3. 查看控制台错误日志
4. 参考各API服务的官方文档

## 🎯 下一步

1. **立即可用**: 所有免费API已配置完成，可以立即开始使用
2. **推荐获取**: Yelp API密钥以增强餐厅数据质量
3. **监控使用**: 通过内置监控系统跟踪API使用情况和成本
4. **持续优化**: 根据使用数据进一步优化API调用策略

---

**🎉 恭喜！您的LetsTravel API优化项目已经配置完成，可以开始享受85%的成本节省！**
