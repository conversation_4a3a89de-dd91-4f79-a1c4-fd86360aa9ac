/**
 * 🔧 修复版Journey适配器
 * 直接使用修复版DayCard，实现用户期望的显示格式
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import SmartDayCard from '../journey/SmartDayCard';
import { CompleteJourneyOrchestrator } from '../../utils/CompleteJourneyOrchestrator';

interface FixedJourneyAdapterProps {
  journey: any;
  activities: any[];
  totalDays?: number;
  onActivityPress?: (activity: any) => void;
}

export default function FixedJourneyAdapter({
  journey,
  activities,
  totalDays = 3,
  onActivityPress
}: FixedJourneyAdapterProps) {
  console.log('🎨 FixedJourneyAdapter 渲染开始，活动数量:', activities.length);

  // 🔍 [DEBUG] FixedJourneyAdapter接收数据详细分析
  console.log('🔍 [DEBUG] FixedJourneyAdapter接收的完整数据:', {
    activitiesType: typeof activities,
    activitiesIsArray: Array.isArray(activities),
    activitiesLength: activities?.length || 0,
    journeyType: typeof journey,
    journeyKeys: journey ? Object.keys(journey) : [],
    totalDays,
    firstActivity: activities?.[0] ? {
      id: activities[0].id,
      title: activities[0].title || activities[0].name,
      type: activities[0].type,
      day: activities[0].day,
      hasLocation: !!activities[0].location
    } : null
  });

  console.log('🔍 [DEBUG] 前3个活动详情:', JSON.stringify(activities?.slice(0, 3), null, 2));
  
  // 🔧 智能按天分组活动 - 确保活动合理分配到3天
  const groupActivitiesByDay = (activities: any[]): { [day: number]: any[] } => {
    console.log('🔄 开始智能活动分组，输入活动数量:', activities.length);

    const grouped: { [day: number]: any[] } = {};

    // 初始化3天
    for (let day = 1; day <= totalDays; day++) {
      grouped[day] = [];
    }

    // 按优先级排序活动
    const sortedActivities = [...activities].sort((a, b) => {
      // 优先考虑有明确day属性的活动
      const dayA = a.day || a.timing?.day || 0;
      const dayB = b.day || b.timing?.day || 0;

      if (dayA && dayB) return dayA - dayB;
      if (dayA && !dayB) return -1;
      if (!dayA && dayB) return 1;

      // 其他按类型优先级排序
      const typeA = a.type || 'attraction';
      const typeB = b.type || 'attraction';
      const typePriority = { cultural: 3, meal: 2, attraction: 1, transport: 0 };
      return (typePriority[typeB] || 0) - (typePriority[typeA] || 0);
    });

    // 智能分配活动
    sortedActivities.forEach((activity, index) => {
      let targetDay = activity.day || activity.timing?.day;

      // 如果没有指定天数，智能分配
      if (!targetDay || targetDay < 1 || targetDay > totalDays) {
        // 轮流分配，确保每天都有活动
        targetDay = (index % totalDays) + 1;

        // 避免交通活动单独成天
        if (activity.type === 'transport') {
          // 交通活动跟随前一个非交通活动
          const prevNonTransport = sortedActivities.slice(0, index).reverse().find(a => a.type !== 'transport');
          if (prevNonTransport) {
            targetDay = prevNonTransport.assignedDay || targetDay;
          }
        }
      }

      // 确保天数在有效范围内
      targetDay = Math.max(1, Math.min(totalDays, targetDay));

      // 记录分配的天数
      activity.assignedDay = targetDay;

      grouped[targetDay].push({
        ...activity,
        day: targetDay
      });
    });

    // 验证分配结果
    const totalAssigned = Object.values(grouped).reduce((sum, dayActivities) => sum + dayActivities.length, 0);
    console.log('✅ 智能活动分组完成:',
      Object.entries(grouped).map(([day, acts]) => `Day${day}: ${acts.length}个活动`));
    console.log(`📊 分配验证: ${totalAssigned}/${activities.length}个活动已分配`);

    return grouped;
  };

  // 🔧 简化的辅助方法（主要逻辑在SmartDayCard中）
  const getDateDisplay = (dayNumber: number): string => {
    const baseDate = new Date('2025-12-15');
    const targetDate = new Date(baseDate);
    targetDate.setDate(baseDate.getDate() + dayNumber - 1);

    return targetDate.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getWeatherInfo = (dayNumber: number): string => {
    const weatherOptions = [
      '🌤️ 晴朗 8°C',
      '☁️ 多云 6°C',
      '🌧️ 小雨 5°C'
    ];
    return weatherOptions[(dayNumber - 1) % weatherOptions.length];
  };

  const groupedActivities = groupActivitiesByDay(activities);
  // totalDays 已经在函数参数中声明，不需要重复声明

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* 📊 行程头部 */}
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>
          {journey?.title || '东京3天精彩之旅'}
        </Text>
        <Text style={styles.headerSubtitle}>
          东京 • {totalDays}天 • 2人
        </Text>
        <Text style={styles.headerDateRange}>
          {getDateDisplay(1)} - {getDateDisplay(totalDays)}
        </Text>
      </View>

      {/* 📅 智能日程卡片 - 所有处理在SmartDayCard中完成 */}
      {Array.from({ length: totalDays }, (_, index) => {
        const dayNumber = index + 1;
        const dayActivities = groupedActivities[dayNumber] || [];

        console.log(`🎨 渲染Day ${dayNumber}: ${dayActivities.length}个活动`);
        console.log(`📊 Day ${dayNumber} 活动详情:`, {
          isArray: Array.isArray(dayActivities),
          length: dayActivities.length,
          activities: dayActivities.map(a => ({
            id: a.id,
            name: a.name || a.title || a.name_zh || '未命名活动',
            type: a.type,
            hasTitle: !!a.title,
            hasName: !!a.name,
            hasNameZh: !!a.name_zh
          }))
        });

        return (
          <SmartDayCard
            key={dayNumber}
            dayNumber={dayNumber}
            activities={dayActivities}
            currency="RM"
            weather={getWeatherInfo(dayNumber)}
            date={getDateDisplay(dayNumber)}
            onActivityPress={onActivityPress}
          />
        );
      })}

      {/* 💰 预算摘要已移除，现在显示在每个DayCard中 */}

      {/* 底部间距 */}
      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  
  // 📊 头部样式
  headerContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  headerDateRange: {
    fontSize: 14,
    color: '#999',
  },
  
  // 💰 预算样式
  budgetSummary: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  budgetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  budgetTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  budgetTotalText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  budgetDailyText: {
    fontSize: 14,
    color: '#666',
  },
  budgetBreakdown: {
    marginBottom: 8,
  },
  budgetItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  budgetCategory: {
    fontSize: 14,
    color: '#333',
  },
  budgetAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  bottomSpacing: {
    height: 50,
  },
});
