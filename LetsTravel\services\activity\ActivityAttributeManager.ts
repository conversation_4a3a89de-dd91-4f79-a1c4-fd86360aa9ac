/**
 * 🏷️ 活动属性管理器
 * 
 * 负责管理和生成真实的活动属性，替换模糊标签系统
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import {
  RealActivityAttributes,
  OperatingHours,
  RatingInfo,
  PriceRangeInfo,
  PriceLevel,
  AvailabilityStatus,
  BookingRequirement,
  SeasonalityInfo,
  AttributeType,
  AttributeDisplayConfig,
  AttributeError
} from '../../types/ActivityAttributes';

/**
 * 🎯 活动属性管理器
 * 
 * 核心功能：
 * 1. 生成真实的活动属性
 * 2. 替换模糊标签系统
 * 3. 提供智能属性推断
 * 4. 支持多数据源融合
 */
export class ActivityAttributeManager {
  private static instance: ActivityAttributeManager;
  
  private constructor() {}
  
  /**
   * 🏭 获取单例实例
   */
  public static getInstance(): ActivityAttributeManager {
    if (!ActivityAttributeManager.instance) {
      ActivityAttributeManager.instance = new ActivityAttributeManager();
    }
    return ActivityAttributeManager.instance;
  }

  /**
   * 🎯 生成活动的真实属性
   * 
   * @param activity 活动数据
   * @param dataSource 数据来源 ('api' | 'template' | 'user_input')
   * @returns 真实活动属性
   */
  public generateRealAttributes(
    activity: any,
    dataSource: 'api' | 'template' | 'user_input' = 'template'
  ): RealActivityAttributes {
    try {
      return {
        operatingHours: this.generateOperatingHours(activity, dataSource),
        rating: this.generateRatingInfo(activity, dataSource),
        priceRange: this.generatePriceRangeInfo(activity, dataSource),
        availability: this.generateAvailabilityStatus(activity, dataSource),
        bookingRequirement: this.generateBookingRequirement(activity, dataSource),
        seasonality: this.generateSeasonalityInfo(activity, dataSource)
      };
    } catch (error) {
      console.error('❌ 生成活动属性失败:', error);
      return this.generateFallbackAttributes(activity);
    }
  }

  /**
   * 🕐 生成营业时间信息
   */
  private generateOperatingHours(activity: any, dataSource: string): OperatingHours {
    // 如果有API数据，优先使用
    if (dataSource === 'api' && activity.opening_hours) {
      return this.parseAPIOperatingHours(activity.opening_hours);
    }

    // 根据活动类型推断营业时间
    const activityType = activity.type || activity.category || 'attraction';
    const defaultHours = this.getDefaultOperatingHours(activityType);
    
    return {
      isCurrentlyOpen: this.isCurrentlyOpen(defaultHours.todayHours),
      todayHours: defaultHours.todayHours,
      weeklyHours: defaultHours.weeklyHours,
      specialNotes: defaultHours.specialNotes
    };
  }

  /**
   * ⭐ 生成评分信息
   */
  private generateRatingInfo(activity: any, dataSource: string): RatingInfo {
    // API数据优先
    if (dataSource === 'api' && activity.rating) {
      return {
        overallRating: Math.round(activity.rating * 10) / 10,
        reviewCount: activity.user_ratings_total || activity.review_count || 0,
        source: this.detectRatingSource(activity),
        subRatings: this.extractSubRatings(activity),
        trend: 'stable'
      };
    }

    // 基于活动类型和名称推断评分
    const estimatedRating = this.estimateRating(activity);
    
    return {
      overallRating: estimatedRating.rating,
      reviewCount: estimatedRating.reviewCount,
      source: 'estimated',
      trend: 'stable'
    };
  }

  /**
   * 💰 生成价格区间信息
   */
  private generatePriceRangeInfo(activity: any, dataSource: string): PriceRangeInfo {
    // 如果有具体价格数据
    if (activity.cost && typeof activity.cost === 'object') {
      const amount = activity.cost.amount || activity.cost;
      return this.createPriceRangeFromAmount(amount, activity.cost.currency || 'MYR');
    }

    // 如果有简单价格数据
    if (activity.cost && typeof activity.cost === 'number') {
      return this.createPriceRangeFromAmount(activity.cost, 'MYR');
    }

    // 基于活动类型推断价格
    const estimatedPrice = this.estimatePrice(activity);
    return estimatedPrice;
  }

  /**
   * 🟢 生成可用性状态
   */
  private generateAvailabilityStatus(activity: any, dataSource: string): AvailabilityStatus {
    const currentHour = new Date().getHours();
    const activityType = activity.type || activity.category || 'attraction';
    
    // 基于时间和类型判断可用性
    const status = this.determineAvailabilityStatus(activityType, currentHour);
    
    return {
      status: status.status,
      description: status.description,
      nextAvailable: status.nextAvailable,
      crowdLevel: this.estimateCrowdLevel(activityType, currentHour),
      bestTimeToVisit: this.getBestVisitTimes(activityType)
    };
  }

  /**
   * 📋 生成预订要求
   */
  private generateBookingRequirement(activity: any, dataSource: string): BookingRequirement {
    const activityType = activity.type || activity.category || 'attraction';
    const bookingInfo = this.getBookingRequirements(activityType);
    
    return {
      required: bookingInfo.required,
      type: bookingInfo.type,
      advanceTime: bookingInfo.advanceTime,
      platforms: bookingInfo.platforms,
      notes: bookingInfo.notes
    };
  }

  /**
   * 🌤️ 生成季节性信息
   */
  private generateSeasonalityInfo(activity: any, dataSource: string): SeasonalityInfo {
    const activityType = activity.type || activity.category || 'attraction';
    const currentMonth = new Date().getMonth() + 1; // 1-12
    
    return {
      bestSeasons: this.getBestSeasons(activityType),
      currentSeasonRating: this.getCurrentSeasonRating(activityType, currentMonth),
      seasonalNotes: this.getSeasonalNotes(activityType, currentMonth),
      weatherDependent: this.isWeatherDependent(activityType)
    };
  }

  /**
   * 🎨 格式化属性显示
   * 
   * @param attributes 活动属性
   * @param config 显示配置
   * @returns 格式化的显示文本
   */
  public formatAttributeDisplay(
    attributes: RealActivityAttributes,
    config: AttributeDisplayConfig
  ): Record<AttributeType, string> {
    const result: Record<AttributeType, string> = {} as any;

    if (config.showAttributes.includes(AttributeType.OPERATING_HOURS)) {
      result[AttributeType.OPERATING_HOURS] = this.formatOperatingHours(
        attributes.operatingHours,
        config.mode
      );
    }

    if (config.showAttributes.includes(AttributeType.RATING)) {
      result[AttributeType.RATING] = this.formatRating(
        attributes.rating,
        config.mode
      );
    }

    if (config.showAttributes.includes(AttributeType.PRICE_RANGE)) {
      result[AttributeType.PRICE_RANGE] = this.formatPriceRange(
        attributes.priceRange,
        config.mode
      );
    }

    if (config.showAttributes.includes(AttributeType.AVAILABILITY)) {
      result[AttributeType.AVAILABILITY] = this.formatAvailability(
        attributes.availability,
        config.mode
      );
    }

    return result;
  }

  // ===== 私有辅助方法 =====

  /**
   * 🕐 判断当前是否营业
   */
  private isCurrentlyOpen(todayHours: string): boolean {
    if (todayHours === '24小时营业') return true;
    if (todayHours === '暂停营业') return false;
    
    const now = new Date();
    const currentTime = now.getHours() * 100 + now.getMinutes();
    
    // 解析营业时间 "09:00-18:00"
    const match = todayHours.match(/(\d{2}):(\d{2})-(\d{2}):(\d{2})/);
    if (!match) return true; // 无法解析时默认开放
    
    const openTime = parseInt(match[1]) * 100 + parseInt(match[2]);
    const closeTime = parseInt(match[3]) * 100 + parseInt(match[4]);
    
    return currentTime >= openTime && currentTime <= closeTime;
  }

  /**
   * 💰 从金额创建价格区间
   */
  private createPriceRangeFromAmount(amount: number, currency: string): PriceRangeInfo {
    const level = this.determinePriceLevel(amount);
    
    return {
      level,
      range: {
        min: amount,
        max: amount,
        currency
      },
      type: 'per_person',
      description: amount === 0 ? '免费参观' : '门票价格',
      includesExtras: false
    };
  }

  /**
   * 💰 确定价格等级
   */
  private determinePriceLevel(amount: number): PriceLevel {
    if (amount === 0) return PriceLevel.FREE;
    if (amount <= 25) return PriceLevel.BUDGET;
    if (amount <= 60) return PriceLevel.MODERATE;
    if (amount <= 100) return PriceLevel.EXPENSIVE;
    return PriceLevel.LUXURY;
  }

  /**
   * 🆘 生成备用属性
   */
  private generateFallbackAttributes(activity: any): RealActivityAttributes {
    return {
      operatingHours: {
        isCurrentlyOpen: true,
        todayHours: '09:00-18:00',
        weeklyHours: {
          monday: '09:00-18:00',
          tuesday: '09:00-18:00',
          wednesday: '09:00-18:00',
          thursday: '09:00-18:00',
          friday: '09:00-18:00',
          saturday: '09:00-18:00',
          sunday: '09:00-18:00'
        }
      },
      rating: {
        overallRating: 4.0,
        reviewCount: 0,
        source: 'estimated',
        trend: 'stable'
      },
      priceRange: {
        level: PriceLevel.MODERATE,
        range: { min: 30, max: 50, currency: 'MYR' },
        type: 'per_person',
        description: '预估价格',
        includesExtras: false
      },
      availability: {
        status: 'available',
        description: '当前可访问',
        crowdLevel: 3,
        bestTimeToVisit: ['上午', '下午']
      },
      bookingRequirement: {
        required: false,
        type: 'walk_in'
      },
      seasonality: {
        bestSeasons: ['全年'],
        currentSeasonRating: 4,
        seasonalNotes: '全年适宜',
        weatherDependent: false
      }
    };
  }

  /**
   * 🕐 获取默认营业时间
   */
  private getDefaultOperatingHours(activityType: string): OperatingHours {
    const hoursByType: Record<string, any> = {
      restaurant: {
        todayHours: '11:00-22:00',
        weeklyHours: {
          monday: '11:00-22:00',
          tuesday: '11:00-22:00',
          wednesday: '11:00-22:00',
          thursday: '11:00-22:00',
          friday: '11:00-23:00',
          saturday: '11:00-23:00',
          sunday: '11:00-22:00'
        },
        specialNotes: '节假日营业时间可能调整'
      },
      attraction: {
        todayHours: '09:00-18:00',
        weeklyHours: {
          monday: '09:00-18:00',
          tuesday: '09:00-18:00',
          wednesday: '09:00-18:00',
          thursday: '09:00-18:00',
          friday: '09:00-18:00',
          saturday: '09:00-18:00',
          sunday: '09:00-18:00'
        },
        specialNotes: '最后入场时间为闭馆前30分钟'
      },
      shopping: {
        todayHours: '10:00-22:00',
        weeklyHours: {
          monday: '10:00-22:00',
          tuesday: '10:00-22:00',
          wednesday: '10:00-22:00',
          thursday: '10:00-22:00',
          friday: '10:00-22:00',
          saturday: '10:00-22:00',
          sunday: '10:00-22:00'
        }
      }
    };

    return hoursByType[activityType] || hoursByType.attraction;
  }

  /**
   * ⭐ 估算活动评分
   */
  private estimateRating(activity: any): { rating: number; reviewCount: number } {
    const activityType = activity.type || activity.category || 'attraction';
    const name = activity.name || activity.title || '';

    // 基于活动类型的基础评分
    const baseRatings: Record<string, number> = {
      restaurant: 4.2,
      attraction: 4.0,
      shopping: 3.8,
      entertainment: 4.1,
      cultural: 4.3,
      nature: 4.4
    };

    let rating = baseRatings[activityType] || 4.0;

    // 基于名称关键词调整评分
    if (name.includes('博物馆') || name.includes('Museum')) rating += 0.2;
    if (name.includes('公园') || name.includes('Park')) rating += 0.3;
    if (name.includes('市场') || name.includes('Market')) rating -= 0.1;

    // 确保评分在合理范围内
    rating = Math.max(3.0, Math.min(5.0, rating));

    return {
      rating: Math.round(rating * 10) / 10,
      reviewCount: Math.floor(Math.random() * 500) + 50 // 50-550条评价
    };
  }

  /**
   * 💰 估算活动价格
   */
  private estimatePrice(activity: any): PriceRangeInfo {
    const activityType = activity.type || activity.category || 'attraction';

    const priceRanges: Record<string, { min: number; max: number; description: string }> = {
      restaurant: { min: 25, max: 80, description: '人均消费' },
      attraction: { min: 10, max: 50, description: '门票价格' },
      shopping: { min: 20, max: 200, description: '预算范围' },
      entertainment: { min: 30, max: 100, description: '活动费用' },
      cultural: { min: 5, max: 30, description: '参观费用' },
      nature: { min: 0, max: 20, description: '入场费用' }
    };

    const range = priceRanges[activityType] || priceRanges.attraction;
    const level = this.determinePriceLevel((range.min + range.max) / 2);

    return {
      level,
      range: { ...range, currency: 'MYR' },
      type: 'per_person',
      description: range.description,
      includesExtras: false
    };
  }

  /**
   * 🟢 确定可用性状态
   */
  private determineAvailabilityStatus(activityType: string, currentHour: number): {
    status: AvailabilityStatus['status'];
    description: string;
    nextAvailable?: string;
  } {
    // 基于时间和类型判断
    if (activityType === 'restaurant') {
      if (currentHour >= 11 && currentHour <= 22) {
        return { status: 'available', description: '正在营业' };
      } else {
        return {
          status: 'closed',
          description: '暂停营业',
          nextAvailable: currentHour < 11 ? '11:00开始营业' : '明日11:00开始营业'
        };
      }
    }

    if (activityType === 'attraction') {
      if (currentHour >= 9 && currentHour <= 18) {
        const crowdHours = [10, 11, 14, 15, 16];
        if (crowdHours.includes(currentHour)) {
          return { status: 'busy', description: '游客较多' };
        }
        return { status: 'available', description: '当前可参观' };
      } else {
        return {
          status: 'closed',
          description: '闭馆时间',
          nextAvailable: currentHour < 9 ? '09:00开放' : '明日09:00开放'
        };
      }
    }

    return { status: 'available', description: '当前可访问' };
  }

  /**
   * 👥 估算拥挤程度
   */
  private estimateCrowdLevel(activityType: string, currentHour: number): number {
    // 基于时间的拥挤程度 (1-5)
    const peakHours = [10, 11, 14, 15, 16]; // 高峰时段
    const busyHours = [9, 12, 13, 17]; // 繁忙时段

    if (peakHours.includes(currentHour)) return 4;
    if (busyHours.includes(currentHour)) return 3;
    return 2; // 相对空闲
  }

  /**
   * 🕐 获取最佳访问时间
   */
  private getBestVisitTimes(activityType: string): string[] {
    const timesByType: Record<string, string[]> = {
      restaurant: ['11:30-12:30', '18:00-19:00'],
      attraction: ['09:00-10:00', '16:00-17:00'],
      shopping: ['10:00-11:00', '19:00-21:00'],
      entertainment: ['14:00-16:00', '19:00-21:00']
    };

    return timesByType[activityType] || ['上午', '下午'];
  }

  /**
   * 📋 获取预订要求
   */
  private getBookingRequirements(activityType: string): BookingRequirement {
    const requirementsByType: Record<string, BookingRequirement> = {
      restaurant: {
        required: false,
        type: 'recommended',
        advanceTime: '当天预订',
        platforms: ['电话预订', '现场排队'],
        notes: '热门时段建议预订'
      },
      attraction: {
        required: false,
        type: 'walk_in',
        platforms: ['现场购票', '在线购票'],
        notes: '现场购票即可'
      },
      entertainment: {
        required: true,
        type: 'advance',
        advanceTime: '提前1-3天',
        platforms: ['在线预订', '电话预订'],
        notes: '建议提前预订确保有位'
      }
    };

    return requirementsByType[activityType] || requirementsByType.attraction;
  }

  /**
   * 🌤️ 获取最佳季节
   */
  private getBestSeasons(activityType: string): string[] {
    if (activityType === 'nature') return ['旱季(5-9月)'];
    if (activityType === 'cultural') return ['全年'];
    return ['全年适宜'];
  }

  /**
   * 🌤️ 获取当前季节评分
   */
  private getCurrentSeasonRating(activityType: string, month: number): number {
    // 马来西亚气候特点：雨季(10-3月)，旱季(4-9月)
    const isDrySeason = month >= 4 && month <= 9;

    if (activityType === 'nature') {
      return isDrySeason ? 5 : 3; // 自然景点旱季更佳
    }

    return 4; // 其他活动全年适宜
  }

  /**
   * 🌤️ 获取季节性说明
   */
  private getSeasonalNotes(activityType: string, month: number): string {
    const isDrySeason = month >= 4 && month <= 9;

    if (activityType === 'nature') {
      return isDrySeason ? '旱季天气晴朗，适合户外活动' : '雨季可能有阵雨，建议携带雨具';
    }

    return '全年适宜参观';
  }

  /**
   * 🌤️ 判断是否依赖天气
   */
  private isWeatherDependent(activityType: string): boolean {
    return ['nature', 'outdoor', 'beach', 'hiking'].includes(activityType);
  }

  // ===== 格式化方法 =====

  /**
   * 🕐 格式化营业时间显示
   */
  private formatOperatingHours(hours: OperatingHours, mode: string): string {
    if (mode === 'compact') {
      return hours.isCurrentlyOpen ? `营业中 ${hours.todayHours}` : `已闭店 ${hours.todayHours}`;
    }

    return `${hours.todayHours} ${hours.isCurrentlyOpen ? '(营业中)' : '(已闭店)'}`;
  }

  /**
   * ⭐ 格式化评分显示
   */
  private formatRating(rating: RatingInfo, mode: string): string {
    if (mode === 'compact') {
      return `⭐ ${rating.overallRating}`;
    }

    return `⭐ ${rating.overallRating} (${rating.reviewCount}条评价)`;
  }

  /**
   * 💰 格式化价格显示
   */
  private formatPriceRange(price: PriceRangeInfo, mode: string): string {
    if (price.level === PriceLevel.FREE) {
      return '免费';
    }

    if (mode === 'compact') {
      return `${price.range.currency}${price.range.min}${price.range.min !== price.range.max ? `-${price.range.max}` : ''}`;
    }

    return `${price.description}: ${price.range.currency}${price.range.min}${price.range.min !== price.range.max ? `-${price.range.max}` : ''}`;
  }

  /**
   * 🟢 格式化可用性显示
   */
  private formatAvailability(availability: AvailabilityStatus, mode: string): string {
    const statusEmojis = {
      available: '🟢',
      busy: '🟡',
      closed: '🔴',
      fully_booked: '🔴',
      seasonal_closed: '🔴'
    };

    const emoji = statusEmojis[availability.status] || '🟢';

    if (mode === 'compact') {
      return `${emoji} ${availability.description}`;
    }

    return `${emoji} ${availability.description}${availability.nextAvailable ? ` (${availability.nextAvailable})` : ''}`;
  }

  // ===== API数据解析方法 =====

  /**
   * 🕐 解析API营业时间
   */
  private parseAPIOperatingHours(apiHours: any): OperatingHours {
    // 这里实现API数据的解析逻辑
    // 具体实现取决于API的数据格式
    return this.getDefaultOperatingHours('attraction');
  }

  /**
   * ⭐ 检测评分来源
   */
  private detectRatingSource(activity: any): RatingInfo['source'] {
    if (activity.place_id) return 'google';
    if (activity.location_id) return 'tripadvisor';
    return 'local';
  }

  /**
   * ⭐ 提取子评分
   */
  private extractSubRatings(activity: any): RatingInfo['subRatings'] | undefined {
    // 从API数据中提取子评分
    // 具体实现取决于API的数据格式
    return undefined;
  }
}
