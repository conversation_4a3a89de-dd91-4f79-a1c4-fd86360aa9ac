/**
 * 📖 展开内容架构
 * 
 * 提供结构化、模块化的展开内容系统，替换单一文本块
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 核心接口定义 =====

/**
 * 📖 展开内容接口
 */
export interface ExpandedContent {
  id: string;
  type: ContentType;
  
  // 核心内容模块
  modules: ContentModule[];
  
  // 内容元数据
  metadata: ContentMetadata;
  
  // 个性化配置
  personalization: PersonalizationConfig;
  
  // 质量评估
  quality: ContentQuality;
}

/**
 * 🧩 内容模块接口
 */
export interface ContentModule {
  id: string;
  type: ModuleType;
  title: string;
  content: ModuleContent;
  priority: number;           // 1-10, 10最高
  isRequired: boolean;
  displayConfig: ModuleDisplayConfig;
}

/**
 * 📝 模块内容
 */
export interface ModuleContent {
  // 文本内容
  text?: {
    primary: string;          // 主要文本
    secondary?: string;       // 补充文本
    highlights?: string[];    // 重点信息
  };
  
  // 列表内容
  list?: {
    items: string[];
    type: 'bullet' | 'numbered' | 'checklist';
    style: 'compact' | 'detailed';
  };
  
  // 表格内容
  table?: {
    headers: string[];
    rows: string[][];
    caption?: string;
  };
  
  // 媒体内容
  media?: {
    type: 'image' | 'video' | 'audio';
    url: string;
    caption?: string;
    alt?: string;
  };
  
  // 交互内容
  interactive?: {
    type: 'map' | 'timeline' | 'gallery' | 'quiz';
    data: any;
    config: any;
  };
}

/**
 * 🎨 模块显示配置
 */
export interface ModuleDisplayConfig {
  layout: 'full' | 'half' | 'third' | 'quarter';
  background: 'none' | 'light' | 'dark' | 'accent';
  border: boolean;
  shadow: boolean;
  animation: 'none' | 'fade' | 'slide' | 'bounce';
  spacing: 'tight' | 'normal' | 'loose';
}

/**
 * 📊 内容元数据
 */
export interface ContentMetadata {
  createdAt: Date;
  updatedAt: Date;
  version: string;
  language: string;
  
  // 内容来源
  sources: ContentSource[];
  
  // 内容统计
  stats: {
    wordCount: number;
    readingTime: number;      // 分钟
    moduleCount: number;
    interactiveElements: number;
  };
  
  // SEO信息
  seo?: {
    title: string;
    description: string;
    keywords: string[];
  };
}

/**
 * 🎯 个性化配置
 */
export interface PersonalizationConfig {
  // 用户偏好
  userPreferences: {
    contentDepth: 'brief' | 'standard' | 'detailed';
    visualStyle: 'minimal' | 'standard' | 'rich';
    interactivity: 'low' | 'medium' | 'high';
  };
  
  // 上下文信息
  context: {
    deviceType: 'mobile' | 'tablet' | 'desktop';
    connectionSpeed: 'slow' | 'medium' | 'fast';
    timeAvailable: number;    // 分钟
  };
  
  // 适应性设置
  adaptations: {
    reduceAnimations: boolean;
    highContrast: boolean;
    largeText: boolean;
    offlineMode: boolean;
  };
}

/**
 * ✅ 内容质量评估
 */
export interface ContentQuality {
  overall: number;            // 0-1
  
  dimensions: {
    accuracy: number;         // 准确性
    completeness: number;     // 完整性
    relevance: number;        // 相关性
    engagement: number;       // 参与度
    accessibility: number;    // 可访问性
  };
  
  issues: QualityIssue[];
  recommendations: string[];
  lastAssessed: Date;
}

/**
 * ⚠️ 质量问题
 */
export interface QualityIssue {
  type: 'accuracy' | 'completeness' | 'relevance' | 'accessibility' | 'performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  moduleId?: string;
  suggestion: string;
}

/**
 * 📚 内容来源
 */
export interface ContentSource {
  type: 'api' | 'database' | 'user_generated' | 'ai_generated' | 'curated';
  name: string;
  url?: string;
  reliability: number;        // 0-1
  lastUpdated: Date;
}

// ===== 枚举定义 =====

export enum ContentType {
  ACTIVITY_DETAIL = 'activity_detail',
  LOCATION_INFO = 'location_info',
  CULTURAL_INSIGHT = 'cultural_insight',
  PRACTICAL_GUIDE = 'practical_guide',
  HISTORICAL_CONTEXT = 'historical_context',
  LOCAL_TIPS = 'local_tips'
}

export enum ModuleType {
  OVERVIEW = 'overview',
  HIGHLIGHTS = 'highlights',
  PRACTICAL_INFO = 'practical_info',
  CULTURAL_CONTEXT = 'cultural_context',
  VISITOR_TIPS = 'visitor_tips',
  NEARBY_ATTRACTIONS = 'nearby_attractions',
  TRANSPORTATION = 'transportation',
  PRICING = 'pricing',
  REVIEWS = 'reviews',
  GALLERY = 'gallery',
  MAP = 'map',
  TIMELINE = 'timeline'
}

// ===== 展开内容架构管理器 =====

export class ExpandedContentArchitecture {
  
  /**
   * 🏗️ 构建展开内容
   */
  static buildExpandedContent(
    contentType: ContentType,
    baseData: any,
    personalization: PersonalizationConfig
  ): ExpandedContent {
    
    // 1. 确定内容模块
    const modules = this.determineModules(contentType, baseData, personalization);
    
    // 2. 生成内容元数据
    const metadata = this.generateMetadata(modules, contentType);
    
    // 3. 评估内容质量
    const quality = this.assessContentQuality(modules, metadata);
    
    return {
      id: `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: contentType,
      modules,
      metadata,
      personalization,
      quality
    };
  }
  
  /**
   * 🧩 确定内容模块
   */
  private static determineModules(
    contentType: ContentType,
    baseData: any,
    personalization: PersonalizationConfig
  ): ContentModule[] {
    
    const moduleTemplates = this.getModuleTemplates(contentType);
    const modules: ContentModule[] = [];
    
    for (const template of moduleTemplates) {
      // 检查是否应该包含此模块
      if (this.shouldIncludeModule(template, personalization, baseData)) {
        const module = this.buildModule(template, baseData, personalization);
        modules.push(module);
      }
    }
    
    // 按优先级排序
    return modules.sort((a, b) => b.priority - a.priority);
  }
  
  /**
   * 📋 获取模块模板
   */
  private static getModuleTemplates(contentType: ContentType) {
    const templates: Record<ContentType, any[]> = {
      [ContentType.ACTIVITY_DETAIL]: [
        {
          type: ModuleType.OVERVIEW,
          priority: 10,
          isRequired: true,
          title: '概览'
        },
        {
          type: ModuleType.HIGHLIGHTS,
          priority: 9,
          isRequired: true,
          title: '亮点特色'
        },
        {
          type: ModuleType.PRACTICAL_INFO,
          priority: 8,
          isRequired: true,
          title: '实用信息'
        },
        {
          type: ModuleType.CULTURAL_CONTEXT,
          priority: 7,
          isRequired: false,
          title: '文化背景'
        },
        {
          type: ModuleType.VISITOR_TIPS,
          priority: 6,
          isRequired: false,
          title: '参观建议'
        },
        {
          type: ModuleType.TRANSPORTATION,
          priority: 5,
          isRequired: true,
          title: '交通指南'
        },
        {
          type: ModuleType.PRICING,
          priority: 4,
          isRequired: true,
          title: '费用信息'
        }
      ],
      [ContentType.LOCATION_INFO]: [
        {
          type: ModuleType.OVERVIEW,
          priority: 10,
          isRequired: true,
          title: '地点介绍'
        },
        {
          type: ModuleType.MAP,
          priority: 9,
          isRequired: true,
          title: '位置地图'
        },
        {
          type: ModuleType.NEARBY_ATTRACTIONS,
          priority: 8,
          isRequired: false,
          title: '周边景点'
        }
      ],
      [ContentType.CULTURAL_INSIGHT]: [
        {
          type: ModuleType.CULTURAL_CONTEXT,
          priority: 10,
          isRequired: true,
          title: '文化洞察'
        },
        {
          type: ModuleType.TIMELINE,
          priority: 8,
          isRequired: false,
          title: '历史时间线'
        }
      ],
      [ContentType.PRACTICAL_GUIDE]: [
        {
          type: ModuleType.PRACTICAL_INFO,
          priority: 10,
          isRequired: true,
          title: '实用指南'
        },
        {
          type: ModuleType.VISITOR_TIPS,
          priority: 9,
          isRequired: true,
          title: '实用建议'
        }
      ],
      [ContentType.HISTORICAL_CONTEXT]: [
        {
          type: ModuleType.TIMELINE,
          priority: 10,
          isRequired: true,
          title: '历史背景'
        },
        {
          type: ModuleType.CULTURAL_CONTEXT,
          priority: 8,
          isRequired: false,
          title: '文化意义'
        }
      ],
      [ContentType.LOCAL_TIPS]: [
        {
          type: ModuleType.VISITOR_TIPS,
          priority: 10,
          isRequired: true,
          title: '当地建议'
        },
        {
          type: ModuleType.PRACTICAL_INFO,
          priority: 8,
          isRequired: false,
          title: '实用信息'
        }
      ]
    };
    
    return templates[contentType] || [];
  }
  
  /**
   * ✅ 检查是否应该包含模块
   */
  private static shouldIncludeModule(
    template: any,
    personalization: PersonalizationConfig,
    baseData: any
  ): boolean {
    
    // 必需模块总是包含
    if (template.isRequired) return true;
    
    // 根据内容深度偏好决定
    const { contentDepth } = personalization.userPreferences;
    
    if (contentDepth === 'brief' && template.priority < 8) return false;
    if (contentDepth === 'standard' && template.priority < 6) return false;
    // detailed 模式包含所有模块
    
    // 根据可用时间决定
    const timeAvailable = personalization.context.timeAvailable;
    if (timeAvailable < 5 && template.priority < 9) return false;
    if (timeAvailable < 10 && template.priority < 7) return false;
    
    return true;
  }
  
  /**
   * 🏗️ 构建单个模块
   */
  private static buildModule(
    template: any,
    baseData: any,
    personalization: PersonalizationConfig
  ): ContentModule {
    
    const content = this.generateModuleContent(template.type, baseData, personalization);
    const displayConfig = this.generateDisplayConfig(template.type, personalization);
    
    return {
      id: `module_${template.type}_${Date.now()}`,
      type: template.type,
      title: template.title,
      content,
      priority: template.priority,
      isRequired: template.isRequired,
      displayConfig
    };
  }
  
  /**
   * 📝 生成模块内容
   */
  private static generateModuleContent(
    moduleType: ModuleType,
    baseData: any,
    personalization: PersonalizationConfig
  ): ModuleContent {
    
    // 根据模块类型生成相应内容
    switch (moduleType) {
      case ModuleType.OVERVIEW:
        return {
          text: {
            primary: baseData.description || '暂无描述',
            highlights: baseData.highlights || []
          }
        };
        
      case ModuleType.PRACTICAL_INFO:
        return {
          list: {
            items: [
              `营业时间: ${baseData.hours || '待确认'}`,
              `地址: ${baseData.address || '待确认'}`,
              `联系方式: ${baseData.contact || '待确认'}`
            ],
            type: 'bullet',
            style: 'compact'
          }
        };
        
      case ModuleType.TRANSPORTATION:
        return {
          text: {
            primary: baseData.transportation || '交通信息待补充'
          }
        };
        
      default:
        return {
          text: {
            primary: '内容正在准备中...'
          }
        };
    }
  }
  
  /**
   * 🎨 生成显示配置
   */
  private static generateDisplayConfig(
    moduleType: ModuleType,
    personalization: PersonalizationConfig
  ): ModuleDisplayConfig {
    
    const { visualStyle } = personalization.userPreferences;
    const { deviceType } = personalization.context;
    
    return {
      layout: deviceType === 'mobile' ? 'full' : 'half',
      background: visualStyle === 'rich' ? 'light' : 'none',
      border: visualStyle !== 'minimal',
      shadow: visualStyle === 'rich',
      animation: personalization.adaptations.reduceAnimations ? 'none' : 'fade',
      spacing: visualStyle === 'minimal' ? 'tight' : 'normal'
    };
  }
  
  /**
   * 📊 生成元数据
   */
  private static generateMetadata(modules: ContentModule[], contentType: ContentType): ContentMetadata {
    const wordCount = modules.reduce((total, module) => {
      const text = module.content.text?.primary || '';
      return total + text.split(' ').length;
    }, 0);
    
    return {
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0.0',
      language: 'zh-CN',
      sources: [{
        type: 'ai_generated',
        name: 'ExpandedContentArchitecture',
        reliability: 0.8,
        lastUpdated: new Date()
      }],
      stats: {
        wordCount,
        readingTime: Math.ceil(wordCount / 200), // 假设每分钟200字
        moduleCount: modules.length,
        interactiveElements: modules.filter(m => m.content.interactive).length
      }
    };
  }
  
  /**
   * ✅ 评估内容质量
   */
  private static assessContentQuality(modules: ContentModule[], metadata: ContentMetadata): ContentQuality {
    return {
      overall: 0.8,
      dimensions: {
        accuracy: 0.8,
        completeness: 0.7,
        relevance: 0.9,
        engagement: 0.8,
        accessibility: 0.8
      },
      issues: [],
      recommendations: [
        '建议添加更多实用信息',
        '可以增加用户评价模块',
        '考虑添加多媒体内容'
      ],
      lastAssessed: new Date()
    };
  }
}

export default ExpandedContentArchitecture;
