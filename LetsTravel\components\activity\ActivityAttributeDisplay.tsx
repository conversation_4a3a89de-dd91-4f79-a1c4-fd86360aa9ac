/**
 * 🏷️ 活动属性显示组件
 * 
 * 用于显示真实的活动属性，替换模糊标签系统
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import React, { useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import {
  RealActivityAttributes,
  AttributeType,
  AttributeDisplayConfig,
  PriceLevel
} from '../../types/ActivityAttributes';
import { ActivityAttributeManager } from '../../services/activity/ActivityAttributeManager';
import { colors, spacing, typography } from '../../constants/Theme';

// ===== 组件属性接口 =====

interface ActivityAttributeDisplayProps {
  /** 活动属性数据 */
  attributes: RealActivityAttributes;
  
  /** 显示模式 */
  mode?: 'compact' | 'detailed' | 'minimal';
  
  /** 显示的属性类型 */
  showAttributes?: AttributeType[];
  
  /** 主题 */
  theme?: 'light' | 'dark';
  
  /** 自定义样式 */
  style?: any;
  
  /** 是否显示图标 */
  showIcons?: boolean;
}

/**
 * 🎯 活动属性显示组件
 * 
 * 功能：
 * 1. 显示真实的活动属性
 * 2. 支持多种显示模式
 * 3. 替换模糊标签系统
 * 4. 提供丰富的视觉反馈
 */
export const ActivityAttributeDisplay: React.FC<ActivityAttributeDisplayProps> = ({
  attributes,
  mode = 'compact',
  showAttributes = [
    AttributeType.OPERATING_HOURS,
    AttributeType.RATING,
    AttributeType.PRICE_RANGE,
    AttributeType.AVAILABILITY
  ],
  theme = 'light',
  style,
  showIcons = true
}) => {
  // 获取属性管理器实例
  const attributeManager = useMemo(() => 
    ActivityAttributeManager.getInstance(), 
    []
  );

  // 格式化显示配置
  const displayConfig: AttributeDisplayConfig = useMemo(() => ({
    mode,
    theme,
    showAttributes,
    locale: 'zh-CN'
  }), [mode, theme, showAttributes]);

  // 格式化属性显示
  const formattedAttributes = useMemo(() => 
    attributeManager.formatAttributeDisplay(attributes, displayConfig),
    [attributes, displayConfig, attributeManager]
  );

  // 主题样式
  const themeStyles = useMemo(() => ({
    container: {
      backgroundColor: theme === 'dark' ? colors.surface : colors.background,
    },
    text: {
      color: theme === 'dark' ? colors.textDark : colors.text,
    },
    secondaryText: {
      color: theme === 'dark' ? colors.textSecondaryDark : colors.textSecondary,
    }
  }), [theme]);

  /**
   * 🎨 渲染单个属性
   */
  const renderAttribute = (type: AttributeType, value: string) => {
    const config = getAttributeConfig(type, attributes);
    
    return (
      <View key={type} style={[styles.attributeItem, config.style]}>
        {showIcons && (
          <Ionicons 
            name={config.icon as any} 
            size={config.iconSize} 
            color={config.iconColor} 
            style={styles.attributeIcon}
          />
        )}
        <Text style={[
          styles.attributeText,
          themeStyles.text,
          config.textStyle
        ]}>
          {value}
        </Text>
        {config.badge && (
          <View style={[styles.badge, config.badgeStyle]}>
            <Text style={[styles.badgeText, config.badgeTextStyle]}>
              {config.badge}
            </Text>
          </View>
        )}
      </View>
    );
  };

  /**
   * 🎨 渲染紧凑模式
   */
  const renderCompactMode = () => (
    <View style={[styles.compactContainer, themeStyles.container, style]}>
      {showAttributes.map(type => {
        const value = formattedAttributes[type];
        return value ? renderAttribute(type, value) : null;
      })}
    </View>
  );

  /**
   * 🎨 渲染详细模式
   */
  const renderDetailedMode = () => (
    <View style={[styles.detailedContainer, themeStyles.container, style]}>
      {showAttributes.map(type => {
        const value = formattedAttributes[type];
        if (!value) return null;

        return (
          <View key={type} style={styles.detailedItem}>
            <Text style={[styles.detailedLabel, themeStyles.secondaryText]}>
              {getAttributeLabel(type)}
            </Text>
            {renderAttribute(type, value)}
          </View>
        );
      })}
    </View>
  );

  /**
   * 🎨 渲染最小模式
   */
  const renderMinimalMode = () => {
    // 只显示最重要的2个属性
    const priorityAttributes = showAttributes.slice(0, 2);
    
    return (
      <View style={[styles.minimalContainer, themeStyles.container, style]}>
        {priorityAttributes.map(type => {
          const value = formattedAttributes[type];
          return value ? renderAttribute(type, value) : null;
        })}
      </View>
    );
  };

  // 根据模式渲染
  switch (mode) {
    case 'detailed':
      return renderDetailedMode();
    case 'minimal':
      return renderMinimalMode();
    default:
      return renderCompactMode();
  }
};

// ===== 辅助函数 =====

/**
 * 🎨 获取属性配置
 */
function getAttributeConfig(type: AttributeType, attributes: RealActivityAttributes) {
  const baseConfig = {
    iconSize: 16,
    iconColor: colors.primary[500],
    textStyle: {},
    style: {},
    badge: null,
    badgeStyle: {},
    badgeTextStyle: {}
  };

  switch (type) {
    case AttributeType.OPERATING_HOURS:
      return {
        ...baseConfig,
        icon: attributes.operatingHours.isCurrentlyOpen ? 'time' : 'time-outline',
        iconColor: attributes.operatingHours.isCurrentlyOpen ? colors.success : colors.warning,
        badge: attributes.operatingHours.isCurrentlyOpen ? '营业中' : null,
        badgeStyle: {
          backgroundColor: attributes.operatingHours.isCurrentlyOpen ? colors.success : colors.warning
        }
      };

    case AttributeType.RATING:
      return {
        ...baseConfig,
        icon: 'star',
        iconColor: colors.warning,
        textStyle: { fontWeight: '600' as const }
      };

    case AttributeType.PRICE_RANGE:
      const priceLevel = attributes.priceRange.level;
      return {
        ...baseConfig,
        icon: 'wallet',
        iconColor: getPriceLevelColor(priceLevel),
        badge: getPriceLevelBadge(priceLevel),
        badgeStyle: {
          backgroundColor: getPriceLevelColor(priceLevel)
        }
      };

    case AttributeType.AVAILABILITY:
      const status = attributes.availability.status;
      return {
        ...baseConfig,
        icon: getAvailabilityIcon(status),
        iconColor: getAvailabilityColor(status),
        badge: getCrowdLevelBadge(attributes.availability.crowdLevel),
        badgeStyle: {
          backgroundColor: getCrowdLevelColor(attributes.availability.crowdLevel)
        }
      };

    default:
      return baseConfig;
  }
}

/**
 * 🏷️ 获取属性标签
 */
function getAttributeLabel(type: AttributeType): string {
  const labels = {
    [AttributeType.OPERATING_HOURS]: '营业时间',
    [AttributeType.RATING]: '评分',
    [AttributeType.PRICE_RANGE]: '价格',
    [AttributeType.AVAILABILITY]: '可用性',
    [AttributeType.BOOKING]: '预订',
    [AttributeType.SEASONALITY]: '季节性'
  };
  
  return labels[type] || '';
}

/**
 * 💰 获取价格等级颜色
 */
function getPriceLevelColor(level: PriceLevel): string {
  const colors_map = {
    [PriceLevel.FREE]: colors.success,
    [PriceLevel.BUDGET]: colors.info,
    [PriceLevel.MODERATE]: colors.warning,
    [PriceLevel.EXPENSIVE]: colors.error,
    [PriceLevel.LUXURY]: colors.purple[500]
  };
  
  return colors_map[level] || colors.primary[500];
}

/**
 * 💰 获取价格等级徽章
 */
function getPriceLevelBadge(level: PriceLevel): string | null {
  const badges = {
    [PriceLevel.FREE]: '免费',
    [PriceLevel.BUDGET]: '经济',
    [PriceLevel.MODERATE]: '适中',
    [PriceLevel.EXPENSIVE]: '较贵',
    [PriceLevel.LUXURY]: '奢华'
  };
  
  return badges[level] || null;
}

/**
 * 🟢 获取可用性图标
 */
function getAvailabilityIcon(status: string): string {
  const icons = {
    available: 'checkmark-circle',
    busy: 'warning',
    closed: 'close-circle',
    fully_booked: 'close-circle',
    seasonal_closed: 'close-circle'
  };
  
  return icons[status] || 'checkmark-circle';
}

/**
 * 🟢 获取可用性颜色
 */
function getAvailabilityColor(status: string): string {
  const colors_map = {
    available: colors.success,
    busy: colors.warning,
    closed: colors.error,
    fully_booked: colors.error,
    seasonal_closed: colors.gray[500]
  };
  
  return colors_map[status] || colors.success;
}

/**
 * 👥 获取拥挤程度徽章
 */
function getCrowdLevelBadge(level: number): string | null {
  if (level <= 2) return '空闲';
  if (level <= 3) return '适中';
  if (level <= 4) return '繁忙';
  return '拥挤';
}

/**
 * 👥 获取拥挤程度颜色
 */
function getCrowdLevelColor(level: number): string {
  if (level <= 2) return colors.success;
  if (level <= 3) return colors.info;
  if (level <= 4) return colors.warning;
  return colors.error;
}

// ===== 样式定义 =====

const styles = StyleSheet.create({
  // 紧凑模式样式
  compactContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
    padding: spacing.xs,
    borderRadius: 8,
  },

  // 详细模式样式
  detailedContainer: {
    padding: spacing.sm,
    borderRadius: 8,
    gap: spacing.sm,
  },

  detailedItem: {
    marginBottom: spacing.xs,
  },

  detailedLabel: {
    fontSize: typography.sizes.sm,
    fontWeight: '500',
    marginBottom: spacing.xs / 2,
  },

  // 最小模式样式
  minimalContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
    padding: spacing.xs,
  },

  // 属性项样式
  attributeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
  },

  attributeIcon: {
    marginRight: spacing.xs / 2,
  },

  attributeText: {
    fontSize: typography.sizes.sm,
    fontWeight: '500',
  },

  // 徽章样式
  badge: {
    paddingHorizontal: spacing.xs,
    paddingVertical: spacing.xs / 2,
    borderRadius: 12,
    marginLeft: spacing.xs / 2,
  },

  badgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: '600',
    color: colors.surface,
  },
});

export default ActivityAttributeDisplay;
