/**
 * 🎯 Ultra Think集成示例
 * 展示如何在前端组件中正确使用Ultra Think数据适配器
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { ultraThinkBridge } from '../services/UltraThinkBridge';
import DayCard from '../components/journey/DayCard';
import ActivityCard from '../components/journey/ActivityCard';
import EnhancedBudgetDisplay from '../components/journey/EnhancedBudgetDisplay';
import { 
  UltraThinkDataAdapter, 
  DayCardDataAdapter, 
  BudgetDataAdapter, 
  ActivityDataAdapter 
} from '../components/adapters/UltraThinkDataAdapter';

interface UltraThinkIntegrationExampleProps {
  destination: string;
  duration: number;
  budget: number;
  currency: string;
}

export const UltraThinkIntegrationExample: React.FC<UltraThinkIntegrationExampleProps> = ({
  destination,
  duration,
  budget,
  currency
}) => {
  const [ultraThinkData, setUltraThinkData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 生成Ultra Think数据
  const generateUltraThinkData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ultraThinkBridge.routeRequest('activity-generation', {
        destination,
        duration,
        budget,
        currency,
        travelers: 2,
        startDate: new Date(),
        preferences: {
          travelStyle: ['文化探索'],
          accommodation: ['酒店'],
          transport: ['公共交通']
        }
      });

      if (response.success) {
        setUltraThinkData(response.data);
        console.log('✅ Ultra Think数据生成成功:', response.data);
      } else {
        setError(response.error || '生成失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    generateUltraThinkData();
  }, [destination, duration, budget, currency]);

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>🔄 正在生成Ultra Think数据...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>❌ 错误: {error}</Text>
      </View>
    );
  }

  if (!ultraThinkData) {
    return (
      <View style={styles.container}>
        <Text style={styles.emptyText}>暂无数据</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🎯 Ultra Think集成示例</Text>
      
      {/* 1. 使用通用数据适配器显示预算 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>💰 预算显示 (通用适配器)</Text>
        <UltraThinkDataAdapter
          data={ultraThinkData}
          targetFormat="budget"
          fallback={<Text>预算数据加载中...</Text>}
          onError={(error) => console.error('预算适配错误:', error)}
        >
          {(budgetData) => (
            <EnhancedBudgetDisplay
              total={budgetData.total}
              currency={currency}
              breakdown={budgetData.breakdown}
              isExpanded={false}
            />
          )}
        </UltraThinkDataAdapter>
      </View>

      {/* 2. 使用专用预算适配器 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>💰 预算显示 (专用适配器)</Text>
        <BudgetDataAdapter ultraThinkData={ultraThinkData}>
          {(budgetData) => (
            <EnhancedBudgetDisplay
              total={budgetData.total}
              currency={currency}
              breakdown={budgetData.breakdown}
              isExpanded={true}
            />
          )}
        </BudgetDataAdapter>
      </View>

      {/* 3. 按天显示DayCard */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📅 每日行程卡片</Text>
        {Array.from({ length: duration }, (_, index) => {
          const day = index + 1;
          return (
            <View key={day} style={styles.dayCardContainer}>
              <DayCardDataAdapter
                ultraThinkData={ultraThinkData}
                day={day}
                currency={currency}
              >
                {(dayData) => (
                  <DayCard
                    dayData={dayData}
                    isExpanded={false}
                    onToggle={() => console.log(`切换Day ${day}展开状态`)}
                    onActivityPress={(activity) => console.log('点击活动:', activity)}
                  />
                )}
              </DayCardDataAdapter>
            </View>
          );
        })}
      </View>

      {/* 4. 显示所有活动 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🎯 所有活动</Text>
        <ActivityDataAdapter ultraThinkData={ultraThinkData}>
          {(activities) => (
            <View>
              {activities.slice(0, 3).map((activity, index) => (
                <View key={activity.id || index} style={styles.activityContainer}>
                  <ActivityCard
                    activity={activity}
                    expandable={true}
                    defaultExpanded={false}
                    onPress={() => console.log('点击活动:', activity.title)}
                  />
                </View>
              ))}
              {activities.length > 3 && (
                <Text style={styles.moreText}>
                  还有 {activities.length - 3} 个活动...
                </Text>
              )}
            </View>
          )}
        </ActivityDataAdapter>
      </View>

      {/* 5. 原始数据展示 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔍 原始Ultra Think数据</Text>
        <Text style={styles.rawDataText}>
          活动数量: {ultraThinkData.activities?.length || 0}
        </Text>
        <Text style={styles.rawDataText}>
          执行时间: {ultraThinkData.executionTime || 0}ms
        </Text>
        <Text style={styles.rawDataText}>
          数据源: {ultraThinkData.source || 'unknown'}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    marginBottom: 24,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  dayCardContainer: {
    marginBottom: 12,
  },
  activityContainer: {
    marginBottom: 8,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 50,
  },
  errorText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#e74c3c',
    marginTop: 50,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#999',
    marginTop: 50,
  },
  moreText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
  },
  rawDataText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
});

export default UltraThinkIntegrationExample;
