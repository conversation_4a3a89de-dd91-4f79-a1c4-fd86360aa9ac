/**
 * 💰 增强预算服务 - 集成精确预算计算器
 * 基于Ultra Think解决方案的智能预算管理
 */

import { BudgetController, BudgetConstraints, BudgetValidationResult } from './BudgetController';
import { TrulyUnifiedBudgetEngine } from '../../utils/TrulyUnifiedBudgetEngine';
import { EnhancedPreferenceAnalysis } from '../preferences/EnhancedPreferenceService';

export interface EnhancedBudgetAnalysis {
  // 原有的预算控制
  traditional: {
    constraints: BudgetConstraints;
    validation: BudgetValidationResult;
  };
  
  // 新的智能预算分析
  intelligent: {
    optimalBudget: any;
    seasonalAdjustments: any;
    personalizedAllocation: any;
    riskAssessment: any;
    savingOpportunities: any[];
    splurgeRecommendations: any[];
  };
  
  // 综合建议
  recommendations: {
    finalAllocation: Record<string, number>;
    priorityAdjustments: string[];
    warningFlags: string[];
    confidenceScore: number;
  };
}

/**
 * 🎯 增强预算服务类
 */
export class EnhancedBudgetService {
  private budgetController: BudgetController;

  constructor() {
    this.budgetController = new BudgetController();
  }

  /**
   * 🎯 执行完整的预算分析
   */
  async analyzeBudget(
    totalBudget: number,
    currency: string,
    duration: number,
    travelers: number,
    destination: string,
    preferenceAnalysis: EnhancedPreferenceAnalysis
  ): Promise<EnhancedBudgetAnalysis> {
    console.log('🎯 开始执行完整的预算分析');

    // 1. 传统预算分析
    const traditionalConstraints = this.budgetController.createBudgetConstraints(
      totalBudget,
      currency,
      duration,
      preferenceAnalysis.normalized
    );

    // 模拟活动数据进行验证
    const mockActivities = this.generateMockActivities(totalBudget, duration);
    const traditionalValidation = this.budgetController.validateBudget(
      mockActivities,
      traditionalConstraints
    );

    // 2. 智能预算分析
    const travelStyle = preferenceAnalysis.intelligent.travelStyle;
    
    // 使用统一预算引擎进行预算计算
    const mockActivity = {
      id: 'budget-calculation',
      name: '预算计算',
      cost: { amount: totalBudget, currency: currency },
      location: { name: destination }
    };
    
    const optimalBudget = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(mockActivity);

    // 3. 季节性调整
    const seasonalAdjustments = this.calculateSeasonalAdjustments(
      destination,
      preferenceAnalysis.intelligent.seasonalContext?.season || 'spring'
    );

    // 4. 个性化分配
    const personalizedAllocation = this.calculatePersonalizedAllocation(
      optimalBudget.breakdown,
      preferenceAnalysis.intelligent.priorityMatrix,
      preferenceAnalysis.intelligent.personalityProfile
    );

    // 5. 风险评估
    const riskAssessment = this.assessBudgetRisk(
      totalBudget,
      personalizedAllocation,
      preferenceAnalysis.intelligent.riskProfile
    );

    // 6. 节省和挥霍建议
    const savingOpportunities = this.identifySavingOpportunities(
      personalizedAllocation,
      preferenceAnalysis.normalized,
      destination
    );

    const splurgeRecommendations = this.generateSplurgeRecommendations(
      personalizedAllocation,
      preferenceAnalysis.intelligent.personalityProfile,
      destination
    );

    // 7. 综合建议
    const recommendations = this.generateComprehensiveRecommendations(
      traditionalValidation,
      optimalBudget,
      personalizedAllocation,
      riskAssessment
    );

    const result: EnhancedBudgetAnalysis = {
      traditional: {
        constraints: traditionalConstraints,
        validation: traditionalValidation
      },
      intelligent: {
        optimalBudget,
        seasonalAdjustments,
        personalizedAllocation,
        riskAssessment,
        savingOpportunities,
        splurgeRecommendations
      },
      recommendations
    };

    console.log('✅ 预算分析完成:', {
      confidenceScore: recommendations.confidenceScore,
      warningFlags: recommendations.warningFlags.length,
      totalAllocation: Object.values(recommendations.finalAllocation).reduce((a, b) => a + b, 0)
    });

    return result;
  }

  /**
   * 🌸 计算季节性调整
   */
  private calculateSeasonalAdjustments(destination: string, season: string): any {
    const adjustments = {
      accommodation: 1.0,
      transport: 1.0,
      food: 1.0,
      activities: 1.0,
      shopping: 1.0
    };

    // 基于目的地和季节的调整
    if (destination === '东京' || destination === '京都' || destination === '大阪') {
      if (season === 'spring') {
        adjustments.accommodation = 1.4; // 樱花季住宿涨价40%
        adjustments.activities = 1.2;    // 活动价格上涨20%
      } else if (season === 'summer') {
        adjustments.food = 1.1;          // 夏季祭典食物价格上涨
        adjustments.activities = 1.3;    // 夏季活动价格上涨
      } else if (season === 'winter') {
        adjustments.accommodation = 1.2; // 冬季取暖费用
        adjustments.transport = 1.1;     // 冬季交通费用
      }
    }

    return {
      season,
      destination,
      adjustments,
      totalImpact: Object.values(adjustments).reduce((a, b) => a + b, 0) / Object.keys(adjustments).length
    };
  }

  /**
   * 🎨 计算个性化分配
   */
  private calculatePersonalizedAllocation(
    baseAllocation: Record<string, number>,
    priorityMatrix: any,
    personalityProfile: any
  ): Record<string, number> {
    const personalizedAllocation = { ...baseAllocation };

    // 基于优先级矩阵调整
    if (priorityMatrix) {
      Object.keys(priorityMatrix).forEach(category => {
        const priority = priorityMatrix[category];
        if (personalizedAllocation[category]) {
          // 高优先级增加10-20%，低优先级减少5-10%
          const adjustment = priority > 50 ? 1.1 + (priority - 50) / 500 : 0.95 + priority / 1000;
          personalizedAllocation[category] *= adjustment;
        }
      });
    }

    // 基于个性档案调整
    if (personalityProfile) {
      // 奢华倾向影响住宿预算
      if (personalityProfile.luxuryOrientation > 70) {
        personalizedAllocation.accommodation *= 1.2;
        personalizedAllocation.food *= 1.15;
      }

      // 冒险精神影响活动预算
      if (personalityProfile.adventurousness > 70) {
        personalizedAllocation.activities *= 1.3;
      }

      // 文化开放度影响文化活动预算
      if (personalityProfile.culturalOpenness > 70) {
        personalizedAllocation.activities *= 1.2;
      }
    }

    // 归一化确保总和不变
    const total = Object.values(personalizedAllocation).reduce((a, b) => a + b, 0);
    const originalTotal = Object.values(baseAllocation).reduce((a, b) => a + b, 0);
    const ratio = originalTotal / total;

    Object.keys(personalizedAllocation).forEach(key => {
      personalizedAllocation[key] = Math.round(personalizedAllocation[key] * ratio);
    });

    return personalizedAllocation;
  }

  /**
   * ⚠️ 评估预算风险
   */
  private assessBudgetRisk(
    totalBudget: number,
    allocation: Record<string, number>,
    riskProfile: any
  ): any {
    const risks = [];
    let riskScore = 0;

    // 检查预算分配合理性
    const accommodationRatio = allocation.accommodation / totalBudget;
    if (accommodationRatio > 0.5) {
      risks.push('住宿预算占比过高，可能影响其他体验');
      riskScore += 20;
    }

    const activitiesRatio = allocation.activities / totalBudget;
    if (activitiesRatio < 0.1) {
      risks.push('活动预算过低，可能影响旅行体验');
      riskScore += 15;
    }

    // 基于用户风险档案调整
    if (riskProfile) {
      if (riskProfile.riskLevel === 'high') {
        risks.push('建议增加应急预算');
        riskScore += 10;
      }

      riskProfile.riskFactors?.forEach((factor: string) => {
        if (factor.includes('健康')) {
          risks.push('建议为健康相关费用预留额外预算');
          riskScore += 5;
        }
      });
    }

    return {
      riskLevel: riskScore < 20 ? 'low' : riskScore < 40 ? 'medium' : 'high',
      riskScore,
      risks,
      recommendations: this.generateRiskMitigationRecommendations(risks)
    };
  }

  /**
   * 💡 识别节省机会
   */
  private identifySavingOpportunities(
    allocation: Record<string, number>,
    preferences: any,
    destination: string
  ): any[] {
    const opportunities = [];

    // 住宿节省建议
    if (allocation.accommodation > allocation.food + allocation.activities) {
      opportunities.push({
        category: 'accommodation',
        potential: Math.round(allocation.accommodation * 0.2),
        suggestion: '考虑选择性价比更高的住宿，如民宿或商务酒店',
        impact: 'medium'
      });
    }

    // 交通节省建议
    if (preferences.transport?.includes('public')) {
      opportunities.push({
        category: 'transport',
        potential: Math.round(allocation.transport * 0.3),
        suggestion: '使用公共交通和步行，购买交通通票',
        impact: 'low'
      });
    }

    // 餐饮节省建议
    opportunities.push({
      category: 'food',
      potential: Math.round(allocation.food * 0.25),
      suggestion: '尝试当地街头美食和便利店，避免游客区餐厅',
      impact: 'medium'
    });

    return opportunities;
  }

  /**
   * 💎 生成挥霍建议
   */
  private generateSplurgeRecommendations(
    allocation: Record<string, number>,
    personalityProfile: any,
    destination: string
  ): any[] {
    const recommendations = [];

    // 基于个性档案的挥霍建议
    if (personalityProfile?.luxuryOrientation > 50) {
      recommendations.push({
        category: 'accommodation',
        amount: Math.round(allocation.accommodation * 0.5),
        suggestion: '升级到豪华酒店或传统日式旅馆',
        experience: '独特的住宿体验'
      });
    }

    if (personalityProfile?.culturalOpenness > 70) {
      recommendations.push({
        category: 'activities',
        amount: Math.round(allocation.activities * 2),
        suggestion: '预订私人文化体验或传统工艺课程',
        experience: '深度文化沉浸'
      });
    }

    // 目的地特色挥霍建议
    if (destination === '东京') {
      recommendations.push({
        category: 'food',
        amount: Math.round(allocation.food * 1.5),
        suggestion: '体验米其林餐厅或高端寿司店',
        experience: '顶级美食体验'
      });
    }

    return recommendations;
  }

  /**
   * 📊 生成综合建议
   */
  private generateComprehensiveRecommendations(
    traditionalValidation: BudgetValidationResult,
    optimalBudget: any,
    personalizedAllocation: Record<string, number>,
    riskAssessment: any
  ): any {
    const warningFlags = [];
    const priorityAdjustments = [];
    let confidenceScore = optimalBudget.confidence || 0.8;

    // 检查传统验证结果
    if (!traditionalValidation.isValid) {
      warningFlags.push('预算分配存在问题');
      confidenceScore -= 0.2;
    }

    // 检查风险评估
    if (riskAssessment.riskLevel === 'high') {
      warningFlags.push('预算风险较高');
      priorityAdjustments.push('增加应急预算');
      confidenceScore -= 0.1;
    }

    // 生成最终分配建议
    const finalAllocation = { ...personalizedAllocation };

    // 如果风险高，调整分配
    if (riskAssessment.riskLevel === 'high') {
      const emergencyAmount = Math.round(Object.values(finalAllocation).reduce((a, b) => a + b, 0) * 0.1);
      finalAllocation.emergency = emergencyAmount;
      
      // 从其他类别中减少相应金额
      const reductionPerCategory = emergencyAmount / (Object.keys(finalAllocation).length - 1);
      Object.keys(finalAllocation).forEach(key => {
        if (key !== 'emergency') {
          finalAllocation[key] = Math.max(0, finalAllocation[key] - reductionPerCategory);
        }
      });
    }

    return {
      finalAllocation,
      priorityAdjustments,
      warningFlags,
      confidenceScore: Math.max(0, Math.min(1, confidenceScore))
    };
  }

  // 辅助方法
  private generateMockActivities(totalBudget: number, duration: number): any[] {
    const dailyBudget = totalBudget / duration;
    const activities = [];

    for (let day = 1; day <= duration; day++) {
      activities.push({
        day,
        category: 'accommodation',
        cost: dailyBudget * 0.3,
        currency: 'USD'
      });
      activities.push({
        day,
        category: 'food',
        cost: dailyBudget * 0.25,
        currency: 'USD'
      });
      activities.push({
        day,
        category: 'activities',
        cost: dailyBudget * 0.2,
        currency: 'USD'
      });
    }

    return activities;
  }

  private generateRiskMitigationRecommendations(risks: string[]): string[] {
    const recommendations = [];
    
    risks.forEach(risk => {
      if (risk.includes('住宿')) {
        recommendations.push('考虑预订可取消的住宿选项');
      }
      if (risk.includes('活动')) {
        recommendations.push('寻找免费或低成本的当地活动');
      }
      if (risk.includes('健康')) {
        recommendations.push('购买旅游保险，准备常用药物');
      }
      if (risk.includes('应急')) {
        recommendations.push('设立专门的应急资金账户');
      }
    });

    return recommendations;
  }
}

export { EnhancedBudgetService };
