/**
 * 🍽️ 自然餐饮调度器
 * 基于活动流程的自然用餐安排，模拟真实旅行者的用餐习惯
 */

export interface MealPlan {
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  time: string;
  name: string;
  reason: string;
  location?: string;
  cost: number;
  duration: number;
  contextual: boolean;
}

export interface DayContext {
  dayNumber: number;
  destination: string;
  weather: string;
  mainActivities: any[];
}

export class NaturalMealScheduler {
  
  /**
   * 🍽️ 为一天安排自然的用餐计划
   */
  static scheduleNaturalMeals(activities: any[], dayContext: DayContext): MealPlan[] {
    console.log(`🍽️ 开始为Day ${dayContext.dayNumber}安排自然用餐`);
    
    const mealPlans: MealPlan[] = [];
    const nonMealActivities = activities.filter(a => a.type !== 'meal');
    
    // 🌅 安排早餐
    const breakfast = this.planBreakfast(nonMealActivities, dayContext);
    if (breakfast) mealPlans.push(breakfast);
    
    // 🌞 安排午餐
    const lunch = this.planLunch(nonMealActivities, dayContext);
    if (lunch) mealPlans.push(lunch);
    
    // 🌆 安排晚餐
    const dinner = this.planDinner(nonMealActivities, dayContext);
    if (dinner) mealPlans.push(dinner);
    
    // 🍪 可能的下午茶/小食
    const snack = this.planOptionalSnack(nonMealActivities, dayContext);
    if (snack) mealPlans.push(snack);
    
    console.log(`✅ 安排了${mealPlans.length}餐: ${mealPlans.map(m => m.type).join(', ')}`);
    
    return mealPlans;
  }
  
  /**
   * 🌅 规划早餐
   */
  private static planBreakfast(activities: any[], context: DayContext): MealPlan | null {
    const firstActivity = activities.find(a => a.startTime);
    if (!firstActivity) return null;
    
    const firstStartMinutes = this.timeToMinutes(firstActivity.startTime);
    
    // 早餐时间：第一个活动前45-90分钟，但不早于7:00，不晚于9:30
    const breakfastMinutes = Math.max(
      7 * 60,  // 最早7:00
      Math.min(
        9.5 * 60, // 最晚9:30
        firstStartMinutes - (45 + Math.random() * 45) // 前45-90分钟
      )
    );
    
    const breakfastTime = this.minutesToTime(breakfastMinutes);
    const breakfastName = this.generateContextualMealName('breakfast', context, firstActivity);
    const location = this.inferMealLocation('breakfast', firstActivity, context);
    
    return {
      type: 'breakfast',
      time: breakfastTime,
      name: breakfastName,
      reason: `在${firstActivity.name}之前享用早餐，为一天的行程储备能量`,
      location,
      cost: this.estimateMealCost('breakfast', location),
      duration: 45 + Math.random() * 30, // 45-75分钟
      contextual: true
    };
  }
  
  /**
   * 🌞 规划午餐
   */
  private static planLunch(activities: any[], context: DayContext): MealPlan | null {
    // 找到上午最后一个活动
    const morningActivities = activities.filter(a => {
      const time = this.timeToMinutes(a.endTime || a.startTime);
      return time < 14 * 60; // 下午2点前
    });
    
    if (morningActivities.length === 0) return null;
    
    const lastMorningActivity = morningActivities[morningActivities.length - 1];
    const lastMorningEndMinutes = this.timeToMinutes(lastMorningActivity.endTime || lastMorningActivity.startTime);
    
    // 午餐时间：上午最后活动结束后15-45分钟
    const lunchMinutes = lastMorningEndMinutes + 15 + Math.random() * 30;
    const lunchTime = this.minutesToTime(lunchMinutes);
    
    const lunchName = this.generateContextualMealName('lunch', context, lastMorningActivity);
    const location = this.inferMealLocation('lunch', lastMorningActivity, context);
    
    return {
      type: 'lunch',
      time: lunchTime,
      name: lunchName,
      reason: `游览${lastMorningActivity.name}后的自然用餐时间`,
      location,
      cost: this.estimateMealCost('lunch', location),
      duration: 60 + Math.random() * 45, // 60-105分钟
      contextual: true
    };
  }
  
  /**
   * 🌆 规划晚餐
   */
  private static planDinner(activities: any[], context: DayContext): MealPlan | null {
    // 找到下午最后一个活动
    const afternoonActivities = activities.filter(a => {
      const time = this.timeToMinutes(a.endTime || a.startTime);
      return time >= 14 * 60; // 下午2点后
    });
    
    let dinnerMinutes: number;
    let dinnerReason: string;
    let referenceActivity: any;
    
    if (afternoonActivities.length > 0) {
      const lastAfternoonActivity = afternoonActivities[afternoonActivities.length - 1];
      const lastAfternoonEndMinutes = this.timeToMinutes(lastAfternoonActivity.endTime || lastAfternoonActivity.startTime);
      
      // 晚餐时间：下午最后活动结束后30-60分钟
      dinnerMinutes = lastAfternoonEndMinutes + 30 + Math.random() * 30;
      dinnerReason = `结束${lastAfternoonActivity.name}后的完美晚餐时光`;
      referenceActivity = lastAfternoonActivity;
    } else {
      // 如果没有下午活动，默认晚上7点左右
      dinnerMinutes = 19 * 60 + Math.random() * 60; // 19:00-20:00
      dinnerReason = '结束一天行程的精选晚餐';
      referenceActivity = activities[activities.length - 1];
    }
    
    const dinnerTime = this.minutesToTime(dinnerMinutes);
    const dinnerName = this.generateContextualMealName('dinner', context, referenceActivity);
    const location = this.inferMealLocation('dinner', referenceActivity, context);
    
    return {
      type: 'dinner',
      time: dinnerTime,
      name: dinnerName,
      reason: dinnerReason,
      location,
      cost: this.estimateMealCost('dinner', location),
      duration: 75 + Math.random() * 45, // 75-120分钟
      contextual: true
    };
  }
  
  /**
   * 🍪 规划可选小食
   */
  private static planOptionalSnack(activities: any[], context: DayContext): MealPlan | null {
    // 只在活动较多的日子安排下午茶
    if (activities.length < 4) return null;
    
    // 随机决定是否安排（50%概率）
    if (Math.random() < 0.5) return null;
    
    // 下午3-4点之间
    const snackMinutes = 15 * 60 + Math.random() * 60; // 15:00-16:00
    const snackTime = this.minutesToTime(snackMinutes);
    
    const snackOptions = [
      '当地特色小食',
      '街边咖啡时光',
      '传统茶点体验',
      '人气甜品店'
    ];
    
    const snackName = snackOptions[Math.floor(Math.random() * snackOptions.length)];
    
    return {
      type: 'snack',
      time: snackTime,
      name: snackName,
      reason: '下午时光，适合品尝当地小食',
      location: '附近推荐',
      cost: 15 + Math.random() * 25, // 15-40 MYR
      duration: 30 + Math.random() * 30, // 30-60分钟
      contextual: true
    };
  }
  
  /**
   * 🏷️ 生成情境化餐饮名称
   */
  private static generateContextualMealName(mealType: string, context: DayContext, nearbyActivity?: any): string {
    const destination = context.destination || '当地';
    const activityName = nearbyActivity?.name || '';
    
    const mealOptions = {
      breakfast: [
        `${destination}传统早餐`,
        '酒店精致早餐',
        '当地人气早点',
        '街边特色早餐',
        '咖啡厅轻食早餐'
      ],
      lunch: [
        `${activityName}附近人气午餐`,
        `${destination}特色料理`,
        '当地推荐定食',
        '街头美食体验',
        '传统料理午餐'
      ],
      dinner: [
        `${destination}精选晚餐`,
        '夜市美食探索',
        '当地特色晚餐',
        '高评价餐厅',
        '地道料理体验'
      ]
    };
    
    const options = mealOptions[mealType] || mealOptions.lunch;
    let selectedName = options[Math.floor(Math.random() * options.length)];
    
    // 清理名称中的空白引用
    selectedName = selectedName.replace(/undefined附近|undefined特色/g, '当地');
    
    return selectedName;
  }
  
  /**
   * 📍 推断用餐地点
   */
  private static inferMealLocation(mealType: string, nearbyActivity: any, context: DayContext): string {
    if (!nearbyActivity) return '市中心';
    
    const activityLocation = nearbyActivity.location?.district || 
                           nearbyActivity.location?.name || 
                           nearbyActivity.name;
    
    if (mealType === 'breakfast') {
      return '酒店或附近';
    }
    
    return `${activityLocation}附近`;
  }
  
  /**
   * 💰 估算餐饮费用
   */
  private static estimateMealCost(mealType: string, location: string): number {
    const baseCosts = {
      breakfast: { min: 20, max: 60, avg: 35 },
      lunch: { min: 30, max: 80, avg: 50 },
      dinner: { min: 50, max: 120, avg: 75 },
      snack: { min: 15, max: 40, avg: 25 }
    };
    
    const cost = baseCosts[mealType] || baseCosts.lunch;
    
    // 根据地点调整价格
    let modifier = 1;
    if (location?.includes('酒店')) modifier = 1.2;
    else if (location?.includes('高级')) modifier = 1.4;
    else if (location?.includes('街边')) modifier = 0.8;
    
    const finalCost = cost.avg * modifier * (0.8 + Math.random() * 0.4); // ±20%变化
    
    return Math.round(finalCost);
  }
  
  /**
   * 🔢 时间转换辅助方法
   */
  private static timeToMinutes(time: string): number {
    if (!time) return 0;
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
  
  private static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}
