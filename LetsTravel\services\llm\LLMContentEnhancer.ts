/**
 * 🤖 LLM内容增强器
 * 使用Gemini 2.5 Flash Lite提升内容质量
 */

import { JSONDataTransfer, DayPlan, Activity, Meal } from '../../types/JourneyDataTypes';

export interface LLMEnhancementRequest {
  activity: Activity;
  context: {
    destination: string;
    dayNumber: number;
    season: string;
    travelers: number;
    budgetLevel: string;
    previousActivity?: string;
    nextActivity?: string;
  };
}

export interface LLMEnhancementResponse {
  enhancedDescription: string;
  highlights: string[];
  expandedContent: {
    whatToExpect: string;
    winterSpecial: string;
    howToGetThere: string;
    bestTime: string;
    photoTips: string[];
    culturalTips: string[];
    budgetTips: string[];
    insiderTips: string[];
  };
  timeOptimization: {
    suggestedDuration: number;
    peakTimes: string;
    weatherConsiderations: string;
  };
  qualityScore: number;
}

export class LLMContentEnhancer {
  private llmService: any;
  
  constructor() {
    // 初始化LLM服务
    this.llmService = this.initializeLLMService();
  }
  
  /**
   * 🎯 增强Journey内容
   */
  async enhanceJourneyContent(journeyJSON: JSONDataTransfer): Promise<JSONDataTransfer> {
    console.log('🤖 开始LLM内容增强');
    const startTime = Date.now();
    
    try {
      const enhancedDayPlans = await Promise.all(
        journeyJSON.payload.dayPlans.map(dayPlan => this.enhanceDayPlan(dayPlan, journeyJSON.payload.journey))
      );
      
      const enhancedPayload = {
        ...journeyJSON.payload,
        dayPlans: enhancedDayPlans
      };
      
      const enhancedJSON: JSONDataTransfer = {
        ...journeyJSON,
        source: "llm_enhancer",
        payload: enhancedPayload,
        metadata: {
          ...journeyJSON.metadata,
          llmEnhanced: true,
          enhancementModel: "google/gemini-2.0-flash-exp:free",
          enhancementTime: Date.now() - startTime,
          qualityScore: 0.95
        }
      };
      
      console.log(`✅ LLM内容增强完成: ${Date.now() - startTime}ms`);
      return enhancedJSON;
      
    } catch (error) {
      console.error('❌ LLM内容增强失败:', error);
      
      // 返回原始数据，标记为未增强
      return {
        ...journeyJSON,
        metadata: {
          ...journeyJSON.metadata,
          llmEnhanced: false,
          enhancementError: error.message
        }
      };
    }
  }
  
  /**
   * 📅 增强单日计划
   */
  private async enhanceDayPlan(dayPlan: DayPlan, journey: any): Promise<DayPlan> {
    console.log(`🤖 增强第${dayPlan.dayNumber}天内容`);
    
    const enhancedActivities = await Promise.all(
      dayPlan.activities.map((activity, index) => 
        this.enhanceActivity(activity, {
          destination: journey.destination,
          dayNumber: dayPlan.dayNumber,
          season: '冬季12月',
          travelers: journey.travelers,
          budgetLevel: this.determineBudgetLevel(activity.cost.amount),
          previousActivity: index > 0 ? dayPlan.activities[index - 1].name : undefined,
          nextActivity: index < dayPlan.activities.length - 1 ? dayPlan.activities[index + 1].name : undefined
        })
      )
    );
    
    const enhancedMeals = await Promise.all(
      dayPlan.meals.map(meal => this.enhanceMeal(meal, {
        destination: journey.destination,
        dayNumber: dayPlan.dayNumber,
        season: '冬季12月',
        travelers: journey.travelers,
        budgetLevel: this.determineBudgetLevel(meal.cost.amount)
      }))
    );
    
    return {
      ...dayPlan,
      activities: enhancedActivities,
      meals: enhancedMeals,
      summary: {
        ...dayPlan.summary,
        weatherAdvice: await this.generateWeatherAdvice(dayPlan.dayNumber)
      }
    };
  }
  
  /**
   * 🎯 增强单个活动
   */
  private async enhanceActivity(activity: Activity, context: any): Promise<Activity> {
    try {
      const prompt = this.buildActivityEnhancementPrompt(activity, context);
      
      const llmResponse = await this.callLLM({
        model: "google/gemini-2.0-flash-exp:free",
        prompt: prompt,
        temperature: 0.3,
        maxTokens: 800,
        responseFormat: "json"
      });
      
      const enhancement = JSON.parse(llmResponse.content);
      
      return {
        ...activity,
        description: enhancement.enhancedDescription || activity.description,
        highlights: enhancement.highlights || activity.highlights,
        expandedContent: {
          ...activity.expandedContent,
          whatToExpected: enhancement.expandedContent?.whatToExpect || activity.expandedContent?.whatToExpected,
          seasonalTips: enhancement.expandedContent?.winterSpecial ? [enhancement.expandedContent.winterSpecial] : activity.expandedContent?.seasonalTips,
          howToGetThere: enhancement.expandedContent?.howToGetThere || activity.expandedContent?.howToGetThere,
          bestPhotoSpots: enhancement.expandedContent?.photoTips || activity.expandedContent?.bestPhotoSpots,
          culturalTips: enhancement.expandedContent?.culturalTips || activity.expandedContent?.culturalTips,
          budgetTips: enhancement.expandedContent?.budgetTips || activity.expandedContent?.budgetTips,
          insiderTips: enhancement.expandedContent?.insiderTips || activity.expandedContent?.insiderTips
        },
        metadata: {
          ...activity.metadata,
          llmEnhanced: true,
          qualityScore: enhancement.qualityScore || 0.9
        }
      };
      
    } catch (error) {
      console.warn(`⚠️ 活动${activity.name}增强失败:`, error);
      return activity; // 返回原始活动
    }
  }
  
  /**
   * 🍽️ 增强餐饮
   */
  private async enhanceMeal(meal: Meal, context: any): Promise<Meal> {
    try {
      const prompt = this.buildMealEnhancementPrompt(meal, context);
      
      const llmResponse = await this.callLLM({
        model: "google/gemini-2.0-flash-exp:free",
        prompt: prompt,
        temperature: 0.3,
        maxTokens: 500,
        responseFormat: "json"
      });
      
      const enhancement = JSON.parse(llmResponse.content);
      
      return {
        ...meal,
        description: enhancement.enhancedDescription || meal.description,
        specialties: enhancement.specialties || meal.specialties,
        details: {
          ...meal.details,
          atmosphere: enhancement.atmosphere || meal.details.atmosphere,
          bestTime: enhancement.bestTime || meal.details.bestTime
        }
      };
      
    } catch (error) {
      console.warn(`⚠️ 餐饮${meal.name}增强失败:`, error);
      return meal;
    }
  }
  
  /**
   * 🏗️ 构建活动增强Prompt
   */
  private buildActivityEnhancementPrompt(activity: Activity, context: any): string {
    return `
# 东京旅游活动内容增强专家

## 任务
为以下东京旅游活动生成高质量、个性化的详细内容。

## 活动信息
- **名称**: ${activity.name}
- **类型**: ${activity.type}
- **时间**: ${activity.timing.timeRange}
- **位置**: ${activity.location.name}
- **当前描述**: ${activity.description}

## 上下文信息
- **目的地**: ${context.destination}
- **季节**: ${context.season}
- **天数**: 第${context.dayNumber}天
- **旅行者**: ${context.travelers}人
- **预算**: ${context.budgetLevel}
- **前一个活动**: ${context.previousActivity || '无'}

## 输出要求
请以JSON格式返回增强内容：

\`\`\`json
{
  "enhancedDescription": "生动具体的描述，突出冬季特色（100-150字）",
  "highlights": ["独特亮点1", "独特亮点2", "独特亮点3"],
  "expandedContent": {
    "whatToExpect": "详细的体验描述，包含感官细节",
    "winterSpecial": "12月冬季的特别体验和注意事项",
    "howToGetThere": "从前一个地点的具体交通方式",
    "bestTime": "在${activity.timing.timeRange}时间段的最佳游览建议",
    "photoTips": ["最佳拍照时机", "推荐拍照角度", "冬季光线建议"],
    "culturalTips": ["文化背景和当地礼仪"],
    "budgetTips": ["适合${context.budgetLevel}预算的省钱建议"],
    "insiderTips": ["当地人才知道的小贴士"]
  },
  "timeOptimization": {
    "suggestedDuration": ${activity.timing.duration},
    "peakTimes": "避开人群的时间建议",
    "weatherConsiderations": "冬季天气的影响和应对"
  },
  "qualityScore": 0.95
}
\`\`\`

## 质量标准
1. **真实性**: 基于真实的东京地点和体验
2. **季节性**: 突出12月冬季的特色
3. **个性化**: 考虑旅行者数量和预算
4. **实用性**: 提供可执行的具体建议
5. **文化性**: 体现日本文化特色

请确保内容生动、准确、实用，避免通用模板。
`;
  }
  
  /**
   * 🏗️ 构建餐饮增强Prompt
   */
  private buildMealEnhancementPrompt(meal: Meal, context: any): string {
    return `
# 东京美食体验增强专家

## 任务
为以下东京餐饮体验生成高质量的详细内容。

## 餐饮信息
- **名称**: ${meal.name}
- **类型**: ${meal.type}
- **菜系**: ${meal.cuisine}
- **时间**: ${meal.time}
- **当前描述**: ${meal.description}

## 上下文信息
- **目的地**: ${context.destination}
- **季节**: ${context.season}
- **天数**: 第${context.dayNumber}天
- **旅行者**: ${context.travelers}人
- **预算**: ${context.budgetLevel}

## 输出要求
请以JSON格式返回增强内容：

\`\`\`json
{
  "enhancedDescription": "生动的餐厅描述，突出特色和氛围（80-120字）",
  "specialties": ["招牌菜1", "招牌菜2", "招牌菜3"],
  "atmosphere": "餐厅氛围和环境描述",
  "bestTime": "最佳用餐时间和建议",
  "winterSpecial": "冬季特色菜品或体验",
  "culturalTips": ["用餐礼仪和文化背景"],
  "budgetTips": ["省钱小贴士"],
  "qualityScore": 0.9
}
\`\`\`

请确保内容真实、实用，体现日本饮食文化特色。
`;
  }
  
  /**
   * 🌤️ 生成天气建议
   */
  private async generateWeatherAdvice(dayNumber: number): Promise<string> {
    const weatherAdvices = [
      '12月东京天气较冷，平均气温5-12°C，建议穿着保暖外套',
      '冬季东京空气干燥，建议携带保湿用品和多喝水',
      '12月偶有降雨，建议携带雨具，室内活动较为舒适',
      '冬季日照时间短，建议合理安排室外活动时间'
    ];
    
    return weatherAdvices[(dayNumber - 1) % weatherAdvices.length];
  }
  
  /**
   * 🔧 辅助方法
   */
  
  private determineBudgetLevel(amount: number): string {
    if (amount > 5000) return 'luxury';
    if (amount > 2000) return 'mid';
    return 'budget';
  }
  
  private initializeLLMService(): any {
    // 模拟LLM服务初始化
    return {
      initialized: true,
      model: "google/gemini-2.0-flash-exp:free"
    };
  }
  
  private async callLLM(request: any): Promise<any> {
    // 模拟LLM调用
    console.log(`🤖 调用LLM: ${request.model}`);
    
    // 这里应该是实际的LLM API调用
    // 现在返回模拟的增强内容
    return {
      content: JSON.stringify({
        enhancedDescription: "经过LLM增强的生动描述，突出了冬季特色和文化背景",
        highlights: ["LLM增强亮点1", "LLM增强亮点2", "LLM增强亮点3"],
        expandedContent: {
          whatToExpect: "LLM生成的详细体验描述",
          winterSpecial: "12月冬季的特别体验",
          howToGetThere: "LLM优化的交通指南",
          bestTime: "LLM建议的最佳时间",
          photoTips: ["LLM拍照建议1", "LLM拍照建议2"],
          culturalTips: ["LLM文化建议1", "LLM文化建议2"],
          budgetTips: ["LLM省钱建议1", "LLM省钱建议2"],
          insiderTips: ["LLM内部建议1", "LLM内部建议2"]
        },
        qualityScore: 0.95
      })
    };
  }
}
