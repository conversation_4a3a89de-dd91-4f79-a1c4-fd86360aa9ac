/**
 * 🔍 验证Master Solver集成
 */

console.log('🔍 验证Master Solver集成状态');
console.log('='.repeat(50));

// 1. 检查Master Solver文件是否存在
try {
  const fs = require('fs');
  const path = require('path');
  
  const masterSolverPath = path.join(__dirname, '../services/ultra-think/UltraThinkMasterSolver.ts');
  if (fs.existsSync(masterSolverPath)) {
    console.log('✅ Master Solver文件存在');
  } else {
    console.log('❌ Master Solver文件不存在');
  }
  
  // 2. 检查UltraThinkActivityGenerator是否导入了Master Solver
  const generatorPath = path.join(__dirname, '../services/activity/UltraThinkActivityGenerator.ts');
  if (fs.existsSync(generatorPath)) {
    const generatorContent = fs.readFileSync(generatorPath, 'utf8');
    if (generatorContent.includes('UltraThinkMasterSolver')) {
      console.log('✅ UltraThinkActivityGenerator已导入Master Solver');
    } else {
      console.log('❌ UltraThinkActivityGenerator未导入Master Solver');
    }
    
    if (generatorContent.includes('Master Solver')) {
      console.log('✅ UltraThinkActivityGenerator包含Master Solver逻辑');
    } else {
      console.log('❌ UltraThinkActivityGenerator缺少Master Solver逻辑');
    }
  }
  
  // 3. 检查配置是否使用Gemini模型
  const configPath = path.join(__dirname, '../config/UltraThinkConfig.ts');
  if (fs.existsSync(configPath)) {
    const configContent = fs.readFileSync(configPath, 'utf8');
    if (configContent.includes('google/gemini-2.0-flash-exp:free')) {
      console.log('✅ 配置已切换到Gemini 2.5 Flash Lite');
    } else {
      console.log('❌ 配置未切换到Gemini模型');
    }
  }
  
  // 4. 检查前端是否有强制选项
  const screenPath = path.join(__dirname, '../screens/journey/AIJourneyPlanningScreen.tsx');
  if (fs.existsSync(screenPath)) {
    const screenContent = fs.readFileSync(screenPath, 'utf8');
    if (screenContent.includes('enableMasterSolver')) {
      console.log('✅ 前端已添加Master Solver强制选项');
    } else {
      console.log('❌ 前端缺少Master Solver强制选项');
    }
  }
  
  // 5. 检查桥接器是否支持Master Solver选项
  const bridgePath = path.join(__dirname, '../services/UltraThinkBridge.ts');
  if (fs.existsSync(bridgePath)) {
    const bridgeContent = fs.readFileSync(bridgePath, 'utf8');
    if (bridgeContent.includes('enableMasterSolver')) {
      console.log('✅ 桥接器已支持Master Solver选项');
    } else {
      console.log('❌ 桥接器缺少Master Solver选项');
    }
  }
  
  console.log('\n📊 集成状态总结:');
  console.log('如果所有项目都显示✅，则Master Solver应该正常工作');
  console.log('如果有❌项目，请检查相应的文件和配置');
  
} catch (error) {
  console.error('❌ 验证过程中出错:', error);
}

// 6. 尝试导入和测试Master Solver
console.log('\n🧪 尝试导入Master Solver...');
try {
  const { UltraThinkMasterSolver } = require('../services/ultra-think/UltraThinkMasterSolver');
  console.log('✅ Master Solver导入成功');
  console.log('🔍 Master Solver类型:', typeof UltraThinkMasterSolver);
  console.log('🔍 solveMasterProblems方法:', typeof UltraThinkMasterSolver.solveMasterProblems);
} catch (error) {
  console.error('❌ Master Solver导入失败:', error.message);
}

console.log('\n✅ 验证完成');
