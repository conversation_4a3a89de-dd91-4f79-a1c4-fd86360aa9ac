/**
 * 🎯 活动类型图标映射
 * 为不同类型的活动提供对应的图标，让用户更清楚活动类型
 */

export interface ActivityIconConfig {
  icon: string;
  color: string;
  backgroundColor: string;
}

export class ActivityIcons {
  private static readonly iconMap: Record<string, ActivityIconConfig> = {
    // 文化景点类
    'cultural': {
      icon: '🏛️',
      color: '#8B4513',
      backgroundColor: '#F5E6D3'
    },
    'temple': {
      icon: '⛩️',
      color: '#DC143C',
      backgroundColor: '#FFE4E1'
    },
    'museum': {
      icon: '🏛️',
      color: '#4682B4',
      backgroundColor: '#E6F3FF'
    },
    'shrine': {
      icon: '⛩️',
      color: '#FF6347',
      backgroundColor: '#FFF0F0'
    },

    // 美食类
    'food': {
      icon: '🍽️',
      color: '#FF8C00',
      backgroundColor: '#FFF8DC'
    },
    'restaurant': {
      icon: '🍽️',
      color: '#FF6347',
      backgroundColor: '#FFF0F0'
    },
    'market': {
      icon: '🏪',
      color: '#32CD32',
      backgroundColor: '#F0FFF0'
    },
    'cafe': {
      icon: '☕',
      color: '#8B4513',
      backgroundColor: '#F5E6D3'
    },

    // 购物类
    'shopping': {
      icon: '🛍️',
      color: '#FF1493',
      backgroundColor: '#FFE4F1'
    },
    'mall': {
      icon: '🏬',
      color: '#4169E1',
      backgroundColor: '#E6EFFF'
    },
    'store': {
      icon: '🏪',
      color: '#32CD32',
      backgroundColor: '#F0FFF0'
    },

    // 交通类
    'transport': {
      icon: '🚇',
      color: '#4682B4',
      backgroundColor: '#E6F3FF'
    },
    'train': {
      icon: '🚄',
      color: '#4682B4',
      backgroundColor: '#E6F3FF'
    },
    'subway': {
      icon: '🚇',
      color: '#696969',
      backgroundColor: '#F5F5F5'
    },
    'bus': {
      icon: '🚌',
      color: '#228B22',
      backgroundColor: '#F0FFF0'
    },
    'taxi': {
      icon: '🚕',
      color: '#FFD700',
      backgroundColor: '#FFFACD'
    },
    'airport': {
      icon: '✈️',
      color: '#4169E1',
      backgroundColor: '#E6EFFF'
    },

    // 住宿类
    'accommodation': {
      icon: '🏨',
      color: '#8A2BE2',
      backgroundColor: '#F3E8FF'
    },
    'hotel': {
      icon: '🏨',
      color: '#8A2BE2',
      backgroundColor: '#F3E8FF'
    },
    'checkin': {
      icon: '🛎️',
      color: '#DAA520',
      backgroundColor: '#FFFACD'
    },

    // 娱乐类
    'entertainment': {
      icon: '🎭',
      color: '#FF69B4',
      backgroundColor: '#FFE4F1'
    },
    'park': {
      icon: '🌳',
      color: '#228B22',
      backgroundColor: '#F0FFF0'
    },
    'tower': {
      icon: '🗼',
      color: '#FF6347',
      backgroundColor: '#FFF0F0'
    },
    'observation': {
      icon: '🔭',
      color: '#4682B4',
      backgroundColor: '#E6F3FF'
    },

    // 自然景观类
    'nature': {
      icon: '🌸',
      color: '#FF69B4',
      backgroundColor: '#FFE4F1'
    },
    'mountain': {
      icon: '⛰️',
      color: '#8B4513',
      backgroundColor: '#F5E6D3'
    },
    'lake': {
      icon: '🏞️',
      color: '#4682B4',
      backgroundColor: '#E6F3FF'
    },
    'garden': {
      icon: '🌺',
      color: '#FF1493',
      backgroundColor: '#FFE4F1'
    },

    // 体验类
    'experience': {
      icon: '✨',
      color: '#FFD700',
      backgroundColor: '#FFFACD'
    },
    'workshop': {
      icon: '🎨',
      color: '#FF6347',
      backgroundColor: '#FFF0F0'
    },
    'festival': {
      icon: '🎪',
      color: '#FF69B4',
      backgroundColor: '#FFE4F1'
    },

    // 默认类型
    'default': {
      icon: '📍',
      color: '#696969',
      backgroundColor: '#F5F5F5'
    }
  };

  /**
   * 🎯 根据活动类型获取图标配置
   */
  static getIconConfig(category: string, activityName?: string): ActivityIconConfig {
    // 优先根据类别匹配
    if (category && this.iconMap[category.toLowerCase()]) {
      return this.iconMap[category.toLowerCase()];
    }

    // 根据活动名称智能匹配
    if (activityName) {
      const name = activityName.toLowerCase();

      // 🍽️ 美食类 (优先级最高，避免被其他类别覆盖)
      if (name.includes('美食') || name.includes('餐厅') || name.includes('餐') ||
          name.includes('料理') || name.includes('寿司') || name.includes('拉面') ||
          name.includes('咖啡') || name.includes('茶') || name.includes('小食') ||
          name.includes('食堂') || name.includes('酒吧') || name.includes('烧烤') ||
          name.includes('火锅') || name.includes('buffet') || name.includes('restaurant')) {
        return this.iconMap['restaurant'];
      }

      // 🏪 市场类 (区分于餐厅)
      if (name.includes('市场') || name.includes('夜市') || name.includes('集市')) {
        return this.iconMap['market'];
      }

      // ⛩️ 寺庙神社类
      if (name.includes('寺') || name.includes('神宫') || name.includes('神社')) {
        return this.iconMap['temple'];
      }

      // 🗼 塔类建筑
      if (name.includes('塔')) {
        return this.iconMap['tower'];
      }

      // 🛍️ 购物类
      if (name.includes('购物') || name.includes('银座') || name.includes('商店')) {
        return this.iconMap['shopping'];
      }

      // ✈️ 交通类
      if (name.includes('机场') || name.includes('航班')) {
        return this.iconMap['airport'];
      }
      if (name.includes('地铁') || name.includes('电车')) {
        return this.iconMap['subway'];
      }
      if (name.includes('快线') || name.includes('特快')) {
        return this.iconMap['train'];
      }

      // 🏨 住宿类
      if (name.includes('住宿') || name.includes('酒店') || name.includes('入住')) {
        return this.iconMap['hotel'];
      }

      // 🌳 公园自然类
      if (name.includes('公园') || name.includes('花园')) {
        return this.iconMap['park'];
      }

      // 🏛️ 博物馆类
      if (name.includes('博物馆') || name.includes('美术馆')) {
        return this.iconMap['museum'];
      }
    }

    // 返回默认图标
    return this.iconMap['default'];
  }

  /**
   * 🎯 获取图标字符串
   */
  static getIcon(category: string, activityName?: string): string {
    return this.getIconConfig(category, activityName).icon;
  }

  /**
   * 🎯 获取图标颜色
   */
  static getIconColor(category: string, activityName?: string): string {
    return this.getIconConfig(category, activityName).color;
  }

  /**
   * 🎯 获取背景颜色
   */
  static getBackgroundColor(category: string, activityName?: string): string {
    return this.getIconConfig(category, activityName).backgroundColor;
  }

  /**
   * 🎯 获取所有可用的图标类型
   */
  static getAllIconTypes(): string[] {
    return Object.keys(this.iconMap);
  }
}
