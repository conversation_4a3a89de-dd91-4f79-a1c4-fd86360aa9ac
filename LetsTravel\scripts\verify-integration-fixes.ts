/**
 * 🧪 集成修复验证脚本
 * 
 * 验证以下问题是否已解决：
 * 1. 时间线12:48-12:48异常时间问题
 * 2. Day1完全没有交通，Day2/3只有部分交通问题
 * 3. 一天只有一餐或没有用餐安排问题
 * 4. 免费景点显示错误费用问题
 * 5. 美食活动显示景点图标问题
 * 6. 预算重复显示问题
 * 
 * <AUTHOR> Think System
 * @version 1.0.0
 * @created 2025-01-30
 */

import { ultraThinkBridge } from '../services/UltraThinkBridge';
import { UnifiedTimeAllocationEngine } from '../services/time/UnifiedTimeAllocationEngine';
import { EnhancedTransportationGenerator } from '../services/transport/EnhancedTransportationGenerator';
import { IntelligentMealScheduler } from '../services/meal/IntelligentMealScheduler';
import { IntelligentActivityClassifier } from '../services/classification/IntelligentActivityClassifier';
import { TrulyUnifiedBudgetEngine } from '../utils/TrulyUnifiedBudgetEngine';

interface VerificationResult {
  testName: string;
  passed: boolean;
  details: string;
  issues: string[];
  recommendations: string[];
}

interface ComprehensiveVerificationReport {
  overallStatus: 'PASSED' | 'FAILED' | 'PARTIAL';
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: VerificationResult[];
  summary: string;
  nextSteps: string[];
}

export class IntegrationFixesVerifier {

  /**
   * 🧪 运行完整的集成验证
   */
  static async runComprehensiveVerification(): Promise<ComprehensiveVerificationReport> {
    console.log('🧪🧪🧪 开始集成修复验证 🧪🧪🧪');
    console.log('='.repeat(60));

    const results: VerificationResult[] = [];

    // 测试1: 验证时间线异常修复
    results.push(await this.verifyTimelineIssues());

    // 测试2: 验证交通安排完整性
    results.push(await this.verifyTransportationCompleteness());

    // 测试3: 验证用餐安排合理性
    results.push(await this.verifyMealScheduling());

    // 测试4: 验证免费景点预算显示
    results.push(await this.verifyFreePlacesBudget());

    // 测试5: 验证活动图标分类
    results.push(await this.verifyActivityIconClassification());

    // 测试6: 验证预算重复显示修复
    results.push(await this.verifyBudgetDuplicationFix());

    // 测试7: 验证新组件集成
    results.push(await this.verifyComponentIntegration());

    // 测试8: 端到端行程生成测试
    results.push(await this.verifyEndToEndJourneyGeneration());

    // 生成综合报告
    const report = this.generateComprehensiveReport(results);
    
    console.log('📊 验证完成，生成综合报告');
    this.printReport(report);

    return report;
  }

  /**
   * ⏰ 验证时间线异常修复
   */
  private static async verifyTimelineIssues(): Promise<VerificationResult> {
    console.log('⏰ 测试1: 验证时间线异常修复');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 创建测试活动
      const testActivities = [
        {
          id: 'test-1',
          name: '浅草寺参观',
          type: 'attraction',
          category: 'cultural',
          location: { name: '浅草寺', address: '东京都台东区' },
          priority: 8,
          preferenceScore: 0.9,
          isPreferenceMatch: true,
          duration: 120,
          flexibilityLevel: 'flexible' as const,
          optimalTimeSlots: ['morning', 'afternoon'] as const,
          weatherSensitive: false,
          bookingRequired: false,
          cost: 0
        },
        {
          id: 'test-2',
          name: '东京塔观景',
          type: 'attraction',
          category: 'sightseeing',
          location: { name: '东京塔', address: '东京都港区' },
          priority: 7,
          preferenceScore: 0.8,
          isPreferenceMatch: true,
          duration: 90,
          flexibilityLevel: 'flexible' as const,
          optimalTimeSlots: ['afternoon', 'evening'] as const,
          weatherSensitive: true,
          bookingRequired: true,
          cost: 1200
        }
      ];

      // 使用统一时间分配引擎
      const allocationResult = UnifiedTimeAllocationEngine.allocateIntelligentTimes(
        testActivities,
        1, // 1天
        new Date(),
        {
          pacePreference: 'moderate',
          mealImportance: 'medium'
        }
      );

      if (!allocationResult.success) {
        passed = false;
        issues.push('时间分配引擎执行失败');
      } else {
        // 检查时间异常
        for (const dayAllocation of allocationResult.allocations) {
          for (const activity of dayAllocation.activities) {
            const startTime = activity.timeSlot.startTime;
            const endTime = activity.timeSlot.endTime;
            
            // 检查是否存在12:48-12:48这样的异常时间
            if (startTime === endTime) {
              passed = false;
              issues.push(`发现异常时间: ${startTime}-${endTime} (活动: ${activity.name})`);
            }
            
            // 检查时间格式是否合理
            if (!this.isValidTimeFormat(startTime) || !this.isValidTimeFormat(endTime)) {
              passed = false;
              issues.push(`时间格式异常: ${startTime}-${endTime}`);
            }
            
            // 检查时间逻辑是否合理
            if (!this.isLogicalTimeSequence(startTime, endTime)) {
              passed = false;
              issues.push(`时间逻辑异常: ${startTime}-${endTime}`);
            }
          }
        }
        
        if (passed) {
          recommendations.push('时间分配正常，质量指标良好');
        }
      }

    } catch (error) {
      passed = false;
      issues.push(`时间线验证异常: ${error.message}`);
    }

    return {
      testName: '时间线异常修复验证',
      passed,
      details: passed ? '所有时间分配正常，无异常时间线' : '发现时间线问题',
      issues,
      recommendations
    };
  }

  /**
   * 🚗 验证交通安排完整性
   */
  private static async verifyTransportationCompleteness(): Promise<VerificationResult> {
    console.log('🚗 测试2: 验证交通安排完整性');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 创建测试活动
      const testActivities = [
        {
          id: 'day1-1',
          name: '新宿站',
          location: { name: '新宿站', address: '东京都新宿区', coordinates: { lat: 35.6896, lng: 139.7006 } },
          timing: { startTime: '09:00', endTime: '10:00', day: 1 }
        },
        {
          id: 'day1-2', 
          name: '明治神宫',
          location: { name: '明治神宫', address: '东京都涩谷区', coordinates: { lat: 35.6762, lng: 139.6993 } },
          timing: { startTime: '10:30', endTime: '12:00', day: 1 }
        },
        {
          id: 'day2-1',
          name: '浅草寺',
          location: { name: '浅草寺', address: '东京都台东区', coordinates: { lat: 35.7148, lng: 139.7967 } },
          timing: { startTime: '09:00', endTime: '10:30', day: 2 }
        }
      ];

      // 使用增强交通生成器
      const transportRequest = {
        activities: testActivities,
        destination: '东京',
        totalDays: 2,
        userPreferences: {
          preferredTransport: 'public' as const,
          budget: 5000,
          comfortLevel: 'standard'
        },
        accommodationInfo: {
          name: '东京酒店',
          address: '东京市中心',
          coordinates: { lat: 35.6762, lng: 139.6503 }
        }
      };

      const transportPlan = EnhancedTransportationGenerator.generateCompleteTransportPlan(transportRequest);

      if (!transportPlan.success) {
        passed = false;
        issues.push('交通生成器执行失败');
      } else {
        // 检查每天是否都有交通安排
        const day1Transports = transportPlan.transports.filter(t => t.day === 1);
        const day2Transports = transportPlan.transports.filter(t => t.day === 2);

        if (day1Transports.length === 0) {
          passed = false;
          issues.push('Day1完全没有交通安排');
        }

        if (day2Transports.length === 0) {
          passed = false;
          issues.push('Day2完全没有交通安排');
        }

        // 检查是否有到达和返回交通
        const hasArrivalTransport = transportPlan.transports.some(t => t.category === 'arrival');
        const hasDepartureTransport = transportPlan.transports.some(t => t.category === 'departure');

        if (!hasArrivalTransport) {
          issues.push('缺少到达交通安排');
        }

        if (!hasDepartureTransport) {
          issues.push('缺少返回交通安排');
        }

        if (passed) {
          recommendations.push(`交通安排完整，共${transportPlan.transports.length}个交通活动`);
        }
      }

    } catch (error) {
      passed = false;
      issues.push(`交通验证异常: ${error.message}`);
    }

    return {
      testName: '交通安排完整性验证',
      passed,
      details: passed ? '交通安排完整，覆盖所有天数' : '交通安排存在缺失',
      issues,
      recommendations
    };
  }

  /**
   * 🍽️ 验证用餐安排合理性
   */
  private static async verifyMealScheduling(): Promise<VerificationResult> {
    console.log('🍽️ 测试3: 验证用餐安排合理性');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 创建测试请求
      const mealRequest = {
        activities: [
          {
            id: 'activity-1',
            timing: { startTime: '09:00', endTime: '11:00', day: 1 }
          },
          {
            id: 'activity-2', 
            timing: { startTime: '14:00', endTime: '16:00', day: 1 }
          }
        ],
        destination: '东京',
        totalDays: 2,
        userPreferences: {
          dietaryRestrictions: [],
          cuisinePreferences: ['日式料理'],
          budgetLevel: 'moderate' as const,
          mealImportance: 'medium'
        },
        accommodationInfo: {
          name: '东京酒店',
          address: '东京市中心',
          coordinates: { lat: 35.6762, lng: 139.6503 },
          hasBreakfast: false
        }
      };

      const mealResult = IntelligentMealScheduler.scheduleDailyMeals(mealRequest);

      if (!mealResult.success) {
        passed = false;
        issues.push('用餐调度器执行失败');
      } else {
        // 检查每天的用餐安排
        for (let day = 1; day <= 2; day++) {
          const dayMeals = mealResult.meals.filter(m => m.day === day);
          
          if (dayMeals.length === 0) {
            passed = false;
            issues.push(`Day${day}没有任何用餐安排`);
            continue;
          }

          if (dayMeals.length === 1) {
            issues.push(`Day${day}只有一餐安排，建议增加`);
          }

          // 检查是否有早中晚餐的合理分布
          const breakfast = dayMeals.find(m => m.category === 'breakfast');
          const lunch = dayMeals.find(m => m.category === 'lunch');
          const dinner = dayMeals.find(m => m.category === 'dinner');

          if (!breakfast && !lunch && !dinner) {
            passed = false;
            issues.push(`Day${day}用餐类型分类异常`);
          }
        }

        if (passed) {
          recommendations.push(`用餐安排合理，共${mealResult.meals.length}个用餐活动`);
        }
      }

    } catch (error) {
      passed = false;
      issues.push(`用餐验证异常: ${error.message}`);
    }

    return {
      testName: '用餐安排合理性验证',
      passed,
      details: passed ? '用餐安排合理，覆盖早中晚餐' : '用餐安排存在问题',
      issues,
      recommendations
    };
  }

  /**
   * 💰 验证免费景点预算显示
   */
  private static async verifyFreePlacesBudget(): Promise<VerificationResult> {
    console.log('💰 测试4: 验证免费景点预算显示');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 测试免费景点
      const freePlaces = [
        {
          id: 'free-1',
          name: '浅草寺',
          cost: { amount: 0, currency: 'JPY' },
          location: { name: '浅草寺' },
          description: '东京著名的免费寺庙'
        },
        {
          id: 'free-2',
          name: '涩谷十字路口',
          cost: { amount: 0, currency: 'JPY' },
          location: { name: '涩谷十字路口' },
          description: '世界著名的免费观光点'
        }
      ];

      for (const place of freePlaces) {
        const budgetInfo = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(place);
        
        if (!budgetInfo.isFree) {
          passed = false;
          issues.push(`${place.name}应该被识别为免费，但显示为收费`);
        }
        
        if (budgetInfo.displayAmount !== 0) {
          passed = false;
          issues.push(`${place.name}显示金额应为0，实际为${budgetInfo.displayAmount}`);
        }
      }

      // 测试收费景点
      const paidPlace = {
        id: 'paid-1',
        name: '东京塔',
        cost: { amount: 1200, currency: 'JPY' },
        location: { name: '东京塔' },
        description: '东京著名的收费观景台'
      };

      const paidBudgetInfo = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(paidPlace);
      
      if (paidBudgetInfo.isFree) {
        passed = false;
        issues.push(`${paidPlace.name}应该被识别为收费，但显示为免费`);
      }

      if (passed) {
        recommendations.push('免费景点识别准确，预算显示正确');
      }

    } catch (error) {
      passed = false;
      issues.push(`免费景点验证异常: ${error.message}`);
    }

    return {
      testName: '免费景点预算显示验证',
      passed,
      details: passed ? '免费景点正确识别和显示' : '免费景点识别存在问题',
      issues,
      recommendations
    };
  }

  /**
   * 🏷️ 验证活动图标分类
   */
  private static async verifyActivityIconClassification(): Promise<VerificationResult> {
    console.log('🏷️ 测试5: 验证活动图标分类');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 测试美食活动
      const mealActivity = {
        id: 'meal-1',
        name: '一兰拉面',
        name_zh: '一兰拉面',
        type: 'meal',
        category: 'dining',
        description: '著名的日式拉面店',
        location: { name: '一兰拉面新宿店' }
      };

      const mealClassification = IntelligentActivityClassifier.classifyActivity(mealActivity);
      
      if (mealClassification.primaryType !== 'meal') {
        passed = false;
        issues.push(`一兰拉面应该分类为meal，实际为${mealClassification.primaryType}`);
      }
      
      if (mealClassification.iconType !== 'restaurant') {
        passed = false;
        issues.push(`一兰拉面图标应该为restaurant，实际为${mealClassification.iconType}`);
      }

      // 测试景点活动
      const attractionActivity = {
        id: 'attraction-1',
        name: '东京塔',
        name_zh: '东京塔',
        type: 'attraction',
        category: 'sightseeing',
        description: '东京著名地标建筑',
        location: { name: '东京塔' }
      };

      const attractionClassification = IntelligentActivityClassifier.classifyActivity(attractionActivity);
      
      if (attractionClassification.primaryType !== 'attraction') {
        passed = false;
        issues.push(`东京塔应该分类为attraction，实际为${attractionClassification.primaryType}`);
      }
      
      if (attractionClassification.iconType !== 'place') {
        passed = false;
        issues.push(`东京塔图标应该为place，实际为${attractionClassification.iconType}`);
      }

      if (passed) {
        recommendations.push('活动分类和图标映射准确');
      }

    } catch (error) {
      passed = false;
      issues.push(`活动分类验证异常: ${error.message}`);
    }

    return {
      testName: '活动图标分类验证',
      passed,
      details: passed ? '活动分类和图标显示正确' : '活动分类存在问题',
      issues,
      recommendations
    };
  }

  /**
   * 🔄 验证预算重复显示修复
   */
  private static async verifyBudgetDuplicationFix(): Promise<VerificationResult> {
    console.log('🔄 测试6: 验证预算重复显示修复');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 这个测试主要验证UI组件的修改是否正确
      // 由于是UI测试，我们主要检查统一预算引擎的调用
      
      const testActivity = {
        id: 'test-budget',
        name: '测试活动',
        cost: { amount: 1000, currency: 'JPY' },
        location: { name: '测试地点' }
      };

      const budgetInfo = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(testActivity);
      
      // 检查是否返回了统一的预算信息
      if (!budgetInfo.displayAmount && budgetInfo.displayAmount !== 0) {
        passed = false;
        issues.push('统一预算引擎未返回显示金额');
      }
      
      if (!budgetInfo.displayCurrency) {
        passed = false;
        issues.push('统一预算引擎未返回显示货币');
      }
      
      if (budgetInfo.source !== 'unified_budget_engine') {
        issues.push('预算来源不是统一引擎，可能存在重复计算');
      }

      if (passed) {
        recommendations.push('统一预算引擎工作正常，UI组件应使用统一数据源');
      }

    } catch (error) {
      passed = false;
      issues.push(`预算重复显示验证异常: ${error.message}`);
    }

    return {
      testName: '预算重复显示修复验证',
      passed,
      details: passed ? '预算显示使用统一数据源' : '预算显示可能存在重复',
      issues,
      recommendations
    };
  }

  /**
   * 🔧 验证新组件集成
   */
  private static async verifyComponentIntegration(): Promise<VerificationResult> {
    console.log('🔧 测试7: 验证新组件集成');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 检查各个组件是否可以正常导入和使用
      const components = [
        { name: 'UnifiedTimeAllocationEngine', component: UnifiedTimeAllocationEngine },
        { name: 'EnhancedTransportationGenerator', component: EnhancedTransportationGenerator },
        { name: 'IntelligentMealScheduler', component: IntelligentMealScheduler },
        { name: 'IntelligentActivityClassifier', component: IntelligentActivityClassifier },
        { name: 'TrulyUnifiedBudgetEngine', component: TrulyUnifiedBudgetEngine }
      ];

      for (const { name, component } of components) {
        if (!component) {
          passed = false;
          issues.push(`${name}组件导入失败`);
        } else {
          // 检查关键方法是否存在
          const methods = this.getExpectedMethods(name);
          for (const method of methods) {
            if (typeof component[method] !== 'function') {
              passed = false;
              issues.push(`${name}.${method}方法不存在或不是函数`);
            }
          }
        }
      }

      if (passed) {
        recommendations.push('所有新组件成功集成，方法可用');
      }

    } catch (error) {
      passed = false;
      issues.push(`组件集成验证异常: ${error.message}`);
    }

    return {
      testName: '新组件集成验证',
      passed,
      details: passed ? '所有新组件成功集成' : '组件集成存在问题',
      issues,
      recommendations
    };
  }

  /**
   * 🎯 验证端到端行程生成
   */
  private static async verifyEndToEndJourneyGeneration(): Promise<VerificationResult> {
    console.log('🎯 测试8: 验证端到端行程生成');
    
    const issues: string[] = [];
    const recommendations: string[] = [];
    let passed = true;

    try {
      // 创建完整的行程生成请求
      const testRequest = {
        destination: '东京',
        duration: 2,
        budget: 6000,
        currency: 'MYR',
        travelers: 2,
        startDate: new Date(),
        preferences: {
          travelStyle: ['文化探索', '美食体验'],
          accommodation: ['酒店'],
          transport: ['公共交通']
        }
      };

      console.log('📤 发送端到端测试请求...');
      const result = await ultraThinkBridge.routeRequest('activity-generation', testRequest);

      if (!result.success) {
        passed = false;
        issues.push('端到端行程生成失败');
        issues.push(`错误信息: ${result.error || '未知错误'}`);
      } else {
        const journeyData = result.data;
        
        // 检查基本数据结构
        if (!journeyData.activities || journeyData.activities.length === 0) {
          passed = false;
          issues.push('生成的行程没有活动');
        }
        
        // 检查时间线问题
        let hasTimelineIssues = false;
        for (const activity of journeyData.activities || []) {
          if (activity.timing?.startTime === activity.timing?.endTime) {
            hasTimelineIssues = true;
            issues.push(`发现时间线问题: ${activity.name} (${activity.timing.startTime}-${activity.timing.endTime})`);
          }
        }
        
        if (!hasTimelineIssues) {
          recommendations.push('时间线问题已修复');
        }
        
        // 检查是否有交通和用餐安排
        const hasTransportation = journeyData.activities?.some(a => a.type === 'transport');
        const hasMeals = journeyData.activities?.some(a => a.type === 'meal');
        
        if (!hasTransportation) {
          issues.push('生成的行程缺少交通安排');
        } else {
          recommendations.push('交通安排已包含');
        }
        
        if (!hasMeals) {
          issues.push('生成的行程缺少用餐安排');
        } else {
          recommendations.push('用餐安排已包含');
        }
      }

    } catch (error) {
      passed = false;
      issues.push(`端到端验证异常: ${error.message}`);
    }

    return {
      testName: '端到端行程生成验证',
      passed,
      details: passed ? '端到端行程生成正常，所有问题已修复' : '端到端生成存在问题',
      issues,
      recommendations
    };
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 📊 生成综合报告
   */
  private static generateComprehensiveReport(results: VerificationResult[]): ComprehensiveVerificationReport {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    let overallStatus: 'PASSED' | 'FAILED' | 'PARTIAL';
    if (passedTests === totalTests) {
      overallStatus = 'PASSED';
    } else if (passedTests === 0) {
      overallStatus = 'FAILED';
    } else {
      overallStatus = 'PARTIAL';
    }

    const summary = this.generateSummary(overallStatus, passedTests, totalTests, results);
    const nextSteps = this.generateNextSteps(results);

    return {
      overallStatus,
      totalTests,
      passedTests,
      failedTests,
      results,
      summary,
      nextSteps
    };
  }

  /**
   * 📝 生成摘要
   */
  private static generateSummary(
    status: 'PASSED' | 'FAILED' | 'PARTIAL',
    passed: number,
    total: number,
    results: VerificationResult[]
  ): string {
    const percentage = Math.round((passed / total) * 100);
    
    let summary = `集成修复验证完成: ${passed}/${total} 测试通过 (${percentage}%)\\n\\n`;
    
    if (status === 'PASSED') {
      summary += '🎉 所有核心问题已成功修复！\\n';
      summary += '- 时间线异常问题已解决\\n';
      summary += '- 交通安排完整性已改善\\n';
      summary += '- 用餐调度合理性已优化\\n';
      summary += '- 免费景点预算显示已修正\\n';
      summary += '- 活动图标分类已准确\\n';
      summary += '- 预算重复显示已消除\\n';
    } else if (status === 'PARTIAL') {
      summary += '⚠️ 部分问题已修复，仍有改进空间\\n';
      const failedTests = results.filter(r => !r.passed);
      summary += `需要关注的问题: ${failedTests.map(t => t.testName).join(', ')}\\n`;
    } else {
      summary += '❌ 修复验证失败，需要进一步调试\\n';
    }

    return summary;
  }

  /**
   * 📋 生成后续步骤
   */
  private static generateNextSteps(results: VerificationResult[]): string[] {
    const nextSteps: string[] = [];
    const failedTests = results.filter(r => !r.passed);
    
    if (failedTests.length === 0) {
      nextSteps.push('✅ 所有测试通过，可以进行生产环境部署');
      nextSteps.push('📊 建议进行性能测试和用户体验测试');
      nextSteps.push('📚 更新文档和用户指南');
    } else {
      nextSteps.push('🔧 修复失败的测试项目');
      
      for (const test of failedTests) {
        nextSteps.push(`- 修复 ${test.testName}: ${test.issues.join(', ')}`);
      }
      
      nextSteps.push('🔄 重新运行验证测试');
      nextSteps.push('📝 更新修复方案文档');
    }

    return nextSteps;
  }

  /**
   * 🖨️ 打印报告
   */
  private static printReport(report: ComprehensiveVerificationReport): void {
    console.log('\\n📊 集成修复验证报告');
    console.log('='.repeat(60));
    console.log(`总体状态: ${report.overallStatus}`);
    console.log(`测试结果: ${report.passedTests}/${report.totalTests} 通过`);
    console.log('\\n📋 详细结果:');
    
    for (const result of report.results) {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName}`);
      console.log(`   详情: ${result.details}`);
      
      if (result.issues.length > 0) {
        console.log(`   问题: ${result.issues.join(', ')}`);
      }
      
      if (result.recommendations.length > 0) {
        console.log(`   建议: ${result.recommendations.join(', ')}`);
      }
      console.log('');
    }
    
    console.log('📝 摘要:');
    console.log(report.summary);
    
    console.log('📋 后续步骤:');
    for (const step of report.nextSteps) {
      console.log(`- ${step}`);
    }
  }

  /**
   * ⏰ 验证时间格式
   */
  private static isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }

  /**
   * 🔄 验证时间逻辑
   */
  private static isLogicalTimeSequence(startTime: string, endTime: string): boolean {
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);
    
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
    
    return endMinutes > startMinutes;
  }

  /**
   * 📋 获取预期方法列表
   */
  private static getExpectedMethods(componentName: string): string[] {
    const methodMap = {
      'UnifiedTimeAllocationEngine': ['allocateIntelligentTimes'],
      'EnhancedTransportationGenerator': ['generateCompleteTransportPlan'],
      'IntelligentMealScheduler': ['scheduleDailyMeals'],
      'IntelligentActivityClassifier': ['classifyActivity'],
      'TrulyUnifiedBudgetEngine': ['getUnifiedActivityBudget']
    };
    
    return methodMap[componentName] || [];
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  IntegrationFixesVerifier.runComprehensiveVerification()
    .then(report => {
      process.exit(report.overallStatus === 'PASSED' ? 0 : 1);
    })
    .catch(error => {
      console.error('验证脚本执行失败:', error);
      process.exit(1);
    });
}