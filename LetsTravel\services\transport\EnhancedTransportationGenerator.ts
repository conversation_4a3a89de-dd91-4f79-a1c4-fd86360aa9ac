/**
 * 🚗 增强交通生成器
 * 
 * 解决以下问题：
 * 1. Day1完全没有交通安排
 * 2. Day2和Day3只有部分交通
 * 3. 交通方式描述不具体
 * 4. 缺乏完整的交通覆盖
 * 
 * <AUTHOR> Think System
 * @version 2.0.0
 * @created 2025-01-30
 */

export interface TransportationRequest {
  activities: any[];
  destination: string;
  totalDays: number;
  userPreferences?: {
    preferredTransport?: 'walking' | 'public' | 'taxi' | 'mixed';
    budget?: number;
    comfortLevel?: 'basic' | 'standard' | 'premium';
  };
  accommodationInfo?: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
}

export interface EnhancedTransportActivity {
  id: string;
  name: string;
  name_zh: string;
  type: 'transport';
  category: 'walking' | 'subway' | 'bus' | 'taxi' | 'train' | 'flight';
  description: string;
  description_zh: string;
  from: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  to: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  timing: {
    startTime: string;
    endTime: string;
    duration: number;
    day: number;
    date: string;
  };
  startTime: string;
  endTime: string;
  duration: number;
  cost: {
    amount: number;
    currency: string;
    priceLevel: 'free' | 'budget' | 'moderate' | 'expensive';
  };
  details: {
    transportMode: string;
    distance: number;
    instructions: string[];
    estimatedWaitTime?: number;
    bookingRequired: boolean;
    tips: string[];
  };
  metadata: {
    source: string;
    confidence: number;
    qualityScore: number;
    lastUpdated: string;
    generatedBy: 'enhanced_transport_generator';
  };
}

export interface TransportationPlan {
  success: boolean;
  transports: EnhancedTransportActivity[];
  summary: {
    totalTransports: number;
    totalDistance: number;
    totalCost: number;
    totalDuration: number;
    coverageByDay: Record<number, number>; // 每天的交通覆盖率
  };
  qualityMetrics: {
    completeness: number; // 交通完整性 0-1
    efficiency: number;   // 交通效率 0-1
    costEffectiveness: number; // 成本效益 0-1
  };
  warnings: string[];
  recommendations: string[];
}

/**
 * 增强交通生成器
 * 
 * 核心特性：
 * 1. 完整覆盖：确保每天都有必要的交通安排
 * 2. 智能选择：基于距离、成本、时间选择最佳交通方式
 * 3. 详细描述：提供具体的交通方式和路线说明
 * 4. 时间同步：与活动时间完美衔接
 */
export class EnhancedTransportationGenerator {
  
  // 交通方式配置
  private static readonly TRANSPORT_CONFIG = {
    walking: {
      maxDistance: 1.5, // 最大步行距离（公里）
      speed: 5,         // 步行速度（公里/小时）
      cost: 0,          // 免费
      comfort: 3,       // 舒适度 1-5
      reliability: 5    // 可靠性 1-5
    },
    subway: {
      maxDistance: 50,  // 地铁适用距离
      speed: 35,        // 平均速度（含等待时间）
      baseCost: 3,      // 基础费用
      comfort: 4,
      reliability: 5
    },
    bus: {
      maxDistance: 30,  // 公交适用距离
      speed: 25,        // 平均速度
      baseCost: 2,
      comfort: 3,
      reliability: 4
    },
    taxi: {
      maxDistance: 100, // 出租车适用距离
      speed: 30,        // 平均速度
      baseCostPerKm: 2, // 每公里费用
      startingFee: 5,   // 起步费
      comfort: 5,
      reliability: 4
    }
  };

  /**
   * 🚗 生成完整的交通计划
   */
  static generateCompleteTransportPlan(request: TransportationRequest): TransportationPlan {
    console.log('🚗 开始生成完整交通计划');
    console.log(`📊 输入: ${request.activities.length}个活动, ${request.totalDays}天`);

    try {
      // 1. 按天分组活动
      const activitiesByDay = this.groupActivitiesByDay(request.activities);
      
      // 2. 为每天生成交通
      const allTransports: EnhancedTransportActivity[] = [];
      const coverageByDay: Record<number, number> = {};
      
      for (let day = 1; day <= request.totalDays; day++) {
        const dayActivities = activitiesByDay[day] || [];
        const dayTransports = this.generateDayTransportation(
          dayActivities,
          day,
          request
        );
        
        allTransports.push(...dayTransports);
        coverageByDay[day] = this.calculateDayCoverage(dayActivities, dayTransports);
        
        console.log(`📅 Day ${day}: 生成${dayTransports.length}个交通活动, 覆盖率${(coverageByDay[day] * 100).toFixed(1)}%`);
      }
      
      // 3. 计算总结和指标
      const summary = this.generateTransportSummary(allTransports, coverageByDay);
      const qualityMetrics = this.calculateTransportQuality(allTransports, request);
      
      // 4. 生成建议和警告
      const { warnings, recommendations } = this.generateTransportInsights(
        allTransports,
        coverageByDay,
        qualityMetrics
      );
      
      const result: TransportationPlan = {
        success: true,
        transports: allTransports,
        summary,
        qualityMetrics,
        warnings,
        recommendations
      };
      
      console.log('✅ 完整交通计划生成完成');
      console.log(`📈 质量指标: 完整性${(qualityMetrics.completeness * 100).toFixed(1)}%, 效率${(qualityMetrics.efficiency * 100).toFixed(1)}%`);
      
      return result;
      
    } catch (error) {
      console.error('❌ 交通计划生成失败:', error);
      
      return {
        success: false,
        transports: [],
        summary: {
          totalTransports: 0,
          totalDistance: 0,
          totalCost: 0,
          totalDuration: 0,
          coverageByDay: {}
        },
        qualityMetrics: {
          completeness: 0,
          efficiency: 0,
          costEffectiveness: 0
        },
        warnings: [`交通计划生成失败: ${error instanceof Error ? error.message : '未知错误'}`],
        recommendations: ['请检查活动数据的完整性，或联系技术支持']
      };
    }
  }

  /**
   * 📅 按天分组活动
   */
  private static groupActivitiesByDay(activities: any[]): Record<number, any[]> {
    const groups: Record<number, any[]> = {};
    
    activities.forEach(activity => {
      const day = activity.timing?.day || activity.day || 1;
      if (!groups[day]) groups[day] = [];
      groups[day].push(activity);
    });
    
    // 按时间排序每天的活动
    Object.keys(groups).forEach(dayStr => {
      const day = parseInt(dayStr);
      groups[day].sort((a, b) => {
        const timeA = this.parseTime(a.timing?.startTime || a.startTime || '09:00');
        const timeB = this.parseTime(b.timing?.startTime || b.startTime || '09:00');
        return timeA - timeB;
      });
    });
    
    return groups;
  }

  /**
   * 🚗 为单天生成交通
   */
  private static generateDayTransportation(
    dayActivities: any[],
    day: number,
    request: TransportationRequest
  ): EnhancedTransportActivity[] {
    console.log(`🚗 为Day ${day}生成交通，活动数量: ${dayActivities.length}`);
    
    const transports: EnhancedTransportActivity[] = [];
    
    if (dayActivities.length === 0) {
      console.log(`⚠️ Day ${day}没有活动，跳过交通生成`);
      return transports;
    }
    
    // 1. 生成到达第一个活动的交通（从住宿地点）
    if (request.accommodationInfo && dayActivities.length > 0) {
      const arrivalTransport = this.generateArrivalTransport(
        request.accommodationInfo,
        dayActivities[0],
        day,
        request
      );
      
      if (arrivalTransport) {
        transports.push(arrivalTransport);
        console.log(`🏨 生成到达交通: ${arrivalTransport.name}`);
      }
    }
    
    // 2. 生成活动间的交通
    for (let i = 0; i < dayActivities.length - 1; i++) {
      const fromActivity = dayActivities[i];
      const toActivity = dayActivities[i + 1];
      
      // 检查是否需要交通（不同地点）
      if (!this.isSameLocation(fromActivity, toActivity)) {
        const interTransport = this.generateInterActivityTransport(
          fromActivity,
          toActivity,
          day,
          request,
          i + 1
        );
        
        if (interTransport) {
          transports.push(interTransport);
          console.log(`🔄 生成活动间交通: ${interTransport.name}`);
        }
      }
    }
    
    // 3. 生成返回住宿的交通（最后一个活动）
    if (request.accommodationInfo && dayActivities.length > 0) {
      const lastActivity = dayActivities[dayActivities.length - 1];
      const returnTransport = this.generateReturnTransport(
        lastActivity,
        request.accommodationInfo,
        day,
        request
      );
      
      if (returnTransport) {
        transports.push(returnTransport);
        console.log(`🏨 生成返回交通: ${returnTransport.name}`);
      }
    }
    
    return transports;
  }

  /**
   * 🏨 生成到达交通（从住宿到第一个活动）
   */
  private static generateArrivalTransport(
    accommodation: any,
    firstActivity: any,
    day: number,
    request: TransportationRequest
  ): EnhancedTransportActivity | null {
    const distance = this.calculateDistance(
      accommodation.coordinates || { lat: 0, lng: 0 },
      firstActivity.location?.coordinates || { lat: 0, lng: 0 }
    );
    
    const optimalTransport = this.selectOptimalTransportMode(
      distance,
      request.userPreferences
    );
    
    // 计算出发时间（活动开始前15分钟到达）
    const activityStartTime = this.parseTime(firstActivity.timing?.startTime || firstActivity.startTime || '09:00');
    const travelTime = this.calculateTravelTime(distance, optimalTransport.mode);
    const departureTime = activityStartTime - travelTime - 15; // 提前15分钟
    
    const startTime = this.minutesToTime(Math.max(480, departureTime)); // 最早8:00出发
    const endTime = this.minutesToTime(Math.max(480, departureTime) + travelTime);
    
    return {
      id: `transport_arrival_day${day}_${Date.now()}`,
      name: `前往${firstActivity.name || firstActivity.name_zh}`,
      name_zh: `前往${firstActivity.name || firstActivity.name_zh}`,
      type: 'transport',
      category: optimalTransport.mode,
      description: `从${accommodation.name}${optimalTransport.description}前往${firstActivity.name || firstActivity.name_zh}`,
      description_zh: `从${accommodation.name}${optimalTransport.description}前往${firstActivity.name || firstActivity.name_zh}`,
      from: {
        name: accommodation.name,
        address: accommodation.address,
        coordinates: accommodation.coordinates
      },
      to: {
        name: firstActivity.location?.name || firstActivity.name,
        address: firstActivity.location?.address || '',
        coordinates: firstActivity.location?.coordinates
      },
      timing: {
        startTime,
        endTime,
        duration: travelTime,
        day,
        date: firstActivity.timing?.date || new Date().toISOString().split('T')[0]
      },
      startTime,
      endTime,
      duration: travelTime,
      cost: {
        amount: optimalTransport.cost,
        currency: request.userPreferences?.budget ? 'CNY' : 'USD',
        priceLevel: this.determinePriceLevel(optimalTransport.cost)
      },
      details: {
        transportMode: optimalTransport.modeDescription,
        distance: Math.round(distance * 100) / 100,
        instructions: this.generateDetailedInstructions(
          accommodation,
          firstActivity.location,
          optimalTransport.mode,
          distance
        ),
        estimatedWaitTime: optimalTransport.waitTime,
        bookingRequired: optimalTransport.bookingRequired,
        tips: this.generateTransportTips(optimalTransport.mode, distance)
      },
      metadata: {
        source: 'enhanced_generator',
        confidence: 0.9,
        qualityScore: 0.85,
        lastUpdated: new Date().toISOString(),
        generatedBy: 'enhanced_transport_generator'
      }
    };
  }

  /**
   * 🔄 生成活动间交通
   */
  private static generateInterActivityTransport(
    fromActivity: any,
    toActivity: any,
    day: number,
    request: TransportationRequest,
    sequence: number
  ): EnhancedTransportActivity | null {
    const distance = this.calculateDistance(
      fromActivity.location?.coordinates || { lat: 0, lng: 0 },
      toActivity.location?.coordinates || { lat: 0, lng: 0 }
    );
    
    const optimalTransport = this.selectOptimalTransportMode(
      distance,
      request.userPreferences
    );
    
    // 计算时间（从前一个活动结束后开始）
    const fromEndTime = this.parseTime(fromActivity.timing?.endTime || fromActivity.endTime || '10:00');
    const toStartTime = this.parseTime(toActivity.timing?.startTime || toActivity.startTime || '11:00');
    const travelTime = this.calculateTravelTime(distance, optimalTransport.mode);
    
    // 确保有足够时间进行交通
    const availableTime = toStartTime - fromEndTime;
    if (availableTime < travelTime) {
      console.warn(`⚠️ Day ${day} 活动间时间不足，调整交通时间`);
    }
    
    const startTime = this.minutesToTime(fromEndTime + 5); // 活动结束5分钟后出发
    const endTime = this.minutesToTime(fromEndTime + 5 + travelTime);
    
    return {
      id: `transport_inter_day${day}_seq${sequence}_${Date.now()}`,
      name: `前往${toActivity.name || toActivity.name_zh}`,
      name_zh: `前往${toActivity.name || toActivity.name_zh}`,
      type: 'transport',
      category: optimalTransport.mode,
      description: `从${fromActivity.name || fromActivity.name_zh}${optimalTransport.description}前往${toActivity.name || toActivity.name_zh}`,
      description_zh: `从${fromActivity.name || fromActivity.name_zh}${optimalTransport.description}前往${toActivity.name || toActivity.name_zh}`,
      from: {
        name: fromActivity.location?.name || fromActivity.name,
        address: fromActivity.location?.address || '',
        coordinates: fromActivity.location?.coordinates
      },
      to: {
        name: toActivity.location?.name || toActivity.name,
        address: toActivity.location?.address || '',
        coordinates: toActivity.location?.coordinates
      },
      timing: {
        startTime,
        endTime,
        duration: travelTime,
        day,
        date: toActivity.timing?.date || new Date().toISOString().split('T')[0]
      },
      startTime,
      endTime,
      duration: travelTime,
      cost: {
        amount: optimalTransport.cost,
        currency: request.userPreferences?.budget ? 'CNY' : 'USD',
        priceLevel: this.determinePriceLevel(optimalTransport.cost)
      },
      details: {
        transportMode: optimalTransport.modeDescription,
        distance: Math.round(distance * 100) / 100,
        instructions: this.generateDetailedInstructions(
          fromActivity.location,
          toActivity.location,
          optimalTransport.mode,
          distance
        ),
        estimatedWaitTime: optimalTransport.waitTime,
        bookingRequired: optimalTransport.bookingRequired,
        tips: this.generateTransportTips(optimalTransport.mode, distance)
      },
      metadata: {
        source: 'enhanced_generator',
        confidence: 0.9,
        qualityScore: 0.85,
        lastUpdated: new Date().toISOString(),
        generatedBy: 'enhanced_transport_generator'
      }
    };
  }

  /**
   * 🏨 生成返回交通（从最后活动回住宿）
   */
  private static generateReturnTransport(
    lastActivity: any,
    accommodation: any,
    day: number,
    request: TransportationRequest
  ): EnhancedTransportActivity | null {
    const distance = this.calculateDistance(
      lastActivity.location?.coordinates || { lat: 0, lng: 0 },
      accommodation.coordinates || { lat: 0, lng: 0 }
    );
    
    const optimalTransport = this.selectOptimalTransportMode(
      distance,
      request.userPreferences
    );
    
    // 计算时间（最后活动结束后）
    const lastEndTime = this.parseTime(lastActivity.timing?.endTime || lastActivity.endTime || '18:00');
    const travelTime = this.calculateTravelTime(distance, optimalTransport.mode);
    
    const startTime = this.minutesToTime(lastEndTime + 10); // 活动结束10分钟后出发
    const endTime = this.minutesToTime(lastEndTime + 10 + travelTime);
    
    return {
      id: `transport_return_day${day}_${Date.now()}`,
      name: `返回${accommodation.name}`,
      name_zh: `返回${accommodation.name}`,
      type: 'transport',
      category: optimalTransport.mode,
      description: `从${lastActivity.name || lastActivity.name_zh}${optimalTransport.description}返回${accommodation.name}`,
      description_zh: `从${lastActivity.name || lastActivity.name_zh}${optimalTransport.description}返回${accommodation.name}`,
      from: {
        name: lastActivity.location?.name || lastActivity.name,
        address: lastActivity.location?.address || '',
        coordinates: lastActivity.location?.coordinates
      },
      to: {
        name: accommodation.name,
        address: accommodation.address,
        coordinates: accommodation.coordinates
      },
      timing: {
        startTime,
        endTime,
        duration: travelTime,
        day,
        date: lastActivity.timing?.date || new Date().toISOString().split('T')[0]
      },
      startTime,
      endTime,
      duration: travelTime,
      cost: {
        amount: optimalTransport.cost,
        currency: request.userPreferences?.budget ? 'CNY' : 'USD',
        priceLevel: this.determinePriceLevel(optimalTransport.cost)
      },
      details: {
        transportMode: optimalTransport.modeDescription,
        distance: Math.round(distance * 100) / 100,
        instructions: this.generateDetailedInstructions(
          lastActivity.location,
          accommodation,
          optimalTransport.mode,
          distance
        ),
        estimatedWaitTime: optimalTransport.waitTime,
        bookingRequired: optimalTransport.bookingRequired,
        tips: this.generateTransportTips(optimalTransport.mode, distance)
      },
      metadata: {
        source: 'enhanced_generator',
        confidence: 0.9,
        qualityScore: 0.85,
        lastUpdated: new Date().toISOString(),
        generatedBy: 'enhanced_transport_generator'
      }
    };
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 🚗 选择最优交通方式
   */
  private static selectOptimalTransportMode(
    distance: number,
    preferences?: any
  ): {
    mode: 'walking' | 'subway' | 'bus' | 'taxi';
    modeDescription: string;
    description: string;
    cost: number;
    waitTime: number;
    bookingRequired: boolean;
  } {
    const config = this.TRANSPORT_CONFIG;
    
    // 步行优先（短距离）
    if (distance <= config.walking.maxDistance) {
      return {
        mode: 'walking',
        modeDescription: '步行',
        description: '步行',
        cost: 0,
        waitTime: 0,
        bookingRequired: false
      };
    }
    
    // 基于偏好和距离选择
    const preferredMode = preferences?.preferredTransport || 'mixed';
    
    if (preferredMode === 'walking' && distance <= 2) {
      return {
        mode: 'walking',
        modeDescription: '步行',
        description: '步行',
        cost: 0,
        waitTime: 0,
        bookingRequired: false
      };
    }
    
    if (preferredMode === 'public' || preferredMode === 'mixed') {
      if (distance <= 15) {
        return {
          mode: 'subway',
          modeDescription: '地铁',
          description: '乘坐地铁',
          cost: config.subway.baseCost,
          waitTime: 5,
          bookingRequired: false
        };
      } else if (distance <= 25) {
        return {
          mode: 'bus',
          modeDescription: '公交',
          description: '乘坐公交',
          cost: config.bus.baseCost,
          waitTime: 8,
          bookingRequired: false
        };
      }
    }
    
    // 默认使用出租车
    return {
      mode: 'taxi',
      modeDescription: '出租车',
      description: '乘坐出租车',
      cost: config.taxi.startingFee + distance * config.taxi.baseCostPerKm,
      waitTime: 3,
      bookingRequired: false
    };
  }

  /**
   * ⏱️ 计算旅行时间
   */
  private static calculateTravelTime(distance: number, mode: string): number {
    const config = this.TRANSPORT_CONFIG;
    const modeConfig = config[mode] || config.walking;
    
    const baseTime = (distance / modeConfig.speed) * 60; // 转换为分钟
    const waitTime = mode === 'walking' ? 0 : (mode === 'taxi' ? 3 : 5);
    
    return Math.round(baseTime + waitTime);
  }

  /**
   * 📍 计算两点间距离（公里）
   */
  private static calculateDistance(coord1: any, coord2: any): number {
    if (!coord1 || !coord2 || !coord1.lat || !coord2.lat) {
      return 2; // 默认距离
    }
    
    const R = 6371; // 地球半径（公里）
    const dLat = this.deg2rad(coord2.lat - coord1.lat);
    const dLon = this.deg2rad(coord2.lng - coord1.lng);
    
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(coord1.lat)) * Math.cos(this.deg2rad(coord2.lat)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return Math.max(0.1, distance); // 最小距离0.1公里
  }

  private static deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }

  /**
   * 📍 检查是否为相同地点
   */
  private static isSameLocation(activity1: any, activity2: any): boolean {
    const loc1 = activity1.location;
    const loc2 = activity2.location;
    
    if (!loc1 || !loc2) return false;
    
    // 基于坐标判断
    if (loc1.coordinates && loc2.coordinates) {
      const distance = this.calculateDistance(loc1.coordinates, loc2.coordinates);
      return distance < 0.1; // 小于100米认为是同一地点
    }
    
    // 基于地址判断
    if (loc1.address && loc2.address) {
      return loc1.address === loc2.address;
    }
    
    // 基于名称判断
    return loc1.name === loc2.name;
  }

  /**
   * 🕐 时间字符串转分钟
   */
  private static parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * 🕐 分钟转时间字符串
   */
  private static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * 💰 确定价格等级
   */
  private static determinePriceLevel(cost: number): 'free' | 'budget' | 'moderate' | 'expensive' {
    if (cost === 0) return 'free';
    if (cost <= 10) return 'budget';
    if (cost <= 30) return 'moderate';
    return 'expensive';
  }

  /**
   * 📝 生成详细指引
   */
  private static generateDetailedInstructions(
    from: any,
    to: any,
    mode: string,
    distance: number
  ): string[] {
    const instructions: string[] = [];
    
    switch (mode) {
      case 'walking':
        instructions.push(`从${from.name || from.address}步行出发`);
        instructions.push(`预计步行${Math.round(distance * 1000)}米，约${Math.round(distance * 12)}分钟`);
        instructions.push(`到达${to.name || to.address}`);
        break;
        
      case 'subway':
        instructions.push(`从${from.name || from.address}前往最近的地铁站`);
        instructions.push(`乘坐地铁前往${to.name || to.address}附近站点`);
        instructions.push(`从地铁站步行到达目的地`);
        instructions.push(`预计总时间${this.calculateTravelTime(distance, mode)}分钟`);
        break;
        
      case 'bus':
        instructions.push(`从${from.name || from.address}前往最近的公交站`);
        instructions.push(`乘坐公交车前往${to.name || to.address}附近站点`);
        instructions.push(`从公交站步行到达目的地`);
        instructions.push(`预计总时间${this.calculateTravelTime(distance, mode)}分钟`);
        break;
        
      case 'taxi':
        instructions.push(`在${from.name || from.address}叫车`);
        instructions.push(`乘坐出租车直达${to.name || to.address}`);
        instructions.push(`预计车程${Math.round(distance * 2)}分钟，费用约${Math.round(5 + distance * 2)}元`);
        break;
    }
    
    return instructions;
  }

  /**
   * 💡 生成交通提示
   */
  private static generateTransportTips(mode: string, distance: number): string[] {
    const tips: string[] = [];
    
    switch (mode) {
      case 'walking':
        tips.push('建议穿着舒适的步行鞋');
        if (distance > 1) {
          tips.push('路程较长，可考虑中途休息');
        }
        tips.push('注意交通安全，遵守交通规则');
        break;
        
      case 'subway':
        tips.push('建议提前查看地铁线路图');
        tips.push('高峰时段可能较为拥挤');
        tips.push('准备好零钱或交通卡');
        break;
        
      case 'bus':
        tips.push('建议提前查看公交路线');
        tips.push('准备好零钱或交通卡');
        tips.push('注意公交车班次时间');
        break;
        
      case 'taxi':
        tips.push('可使用打车软件叫车');
        tips.push('确认司机身份和车牌号');
        tips.push('准备好现金或移动支付');
        break;
    }
    
    return tips;
  }

  /**
   * 📊 计算单天交通覆盖率
   */
  private static calculateDayCoverage(activities: any[], transports: EnhancedTransportActivity[]): number {
    if (activities.length <= 1) return 1; // 单个活动或无活动认为完全覆盖
    
    const requiredTransports = Math.max(0, activities.length - 1); // 活动间交通
    const actualTransports = transports.length;
    
    return Math.min(1, actualTransports / Math.max(1, requiredTransports));
  }

  /**
   * 📊 生成交通总结
   */
  private static generateTransportSummary(
    transports: EnhancedTransportActivity[],
    coverageByDay: Record<number, number>
  ) {
    const totalTransports = transports.length;
    const totalDistance = transports.reduce((sum, t) => sum + t.details.distance, 0);
    const totalCost = transports.reduce((sum, t) => sum + t.cost.amount, 0);
    const totalDuration = transports.reduce((sum, t) => sum + t.duration, 0);
    
    return {
      totalTransports,
      totalDistance: Math.round(totalDistance * 100) / 100,
      totalCost: Math.round(totalCost * 100) / 100,
      totalDuration,
      coverageByDay
    };
  }

  /**
   * 📈 计算交通质量指标
   */
  private static calculateTransportQuality(
    transports: EnhancedTransportActivity[],
    request: TransportationRequest
  ) {
    // 完整性：基于覆盖率
    const coverageValues = Object.values(this.groupActivitiesByDay(request.activities))
      .map((dayActivities, index) => {
        const dayTransports = transports.filter(t => t.timing.day === index + 1);
        return this.calculateDayCoverage(dayActivities, dayTransports);
      });
    
    const completeness = coverageValues.length > 0 
      ? coverageValues.reduce((sum, coverage) => sum + coverage, 0) / coverageValues.length
      : 0;
    
    // 效率：基于时间和距离的合理性
    let efficiency = 0.8; // 基础效率
    transports.forEach(transport => {
      const expectedTime = this.calculateTravelTime(transport.details.distance, transport.category);
      const actualTime = transport.duration;
      const timeRatio = expectedTime / Math.max(1, actualTime);
      
      if (timeRatio >= 0.8 && timeRatio <= 1.2) {
        efficiency += 0.02; // 时间合理
      } else {
        efficiency -= 0.01; // 时间不合理
      }
    });
    
    efficiency = Math.max(0, Math.min(1, efficiency));
    
    // 成本效益：基于成本和距离的关系
    const totalCost = transports.reduce((sum, t) => sum + t.cost.amount, 0);
    const totalDistance = transports.reduce((sum, t) => sum + t.details.distance, 0);
    const costPerKm = totalDistance > 0 ? totalCost / totalDistance : 0;
    
    let costEffectiveness = 1;
    if (costPerKm > 10) {
      costEffectiveness = 0.6; // 成本较高
    } else if (costPerKm > 5) {
      costEffectiveness = 0.8; // 成本中等
    }
    
    return {
      completeness,
      efficiency,
      costEffectiveness
    };
  }

  /**
   * 💡 生成交通洞察和建议
   */
  private static generateTransportInsights(
    transports: EnhancedTransportActivity[],
    coverageByDay: Record<number, number>,
    qualityMetrics: any
  ) {
    const warnings: string[] = [];
    const recommendations: string[] = [];
    
    // 检查覆盖率
    Object.entries(coverageByDay).forEach(([dayStr, coverage]) => {
      const day = parseInt(dayStr);
      if (coverage < 0.5) {
        warnings.push(`Day ${day}的交通覆盖率较低(${(coverage * 100).toFixed(1)}%)，可能存在交通空缺`);
      } else if (coverage < 0.8) {
        recommendations.push(`Day ${day}的交通安排可以进一步优化`);
      }
    });
    
    // 检查质量指标
    if (qualityMetrics.completeness < 0.7) {
      warnings.push('整体交通完整性不足，建议增加必要的交通安排');
    }
    
    if (qualityMetrics.efficiency < 0.6) {
      recommendations.push('交通效率有待提升，建议优化路线和交通方式选择');
    }
    
    if (qualityMetrics.costEffectiveness < 0.7) {
      recommendations.push('交通成本较高，建议考虑更经济的交通方式');
    }
    
    // 基于交通方式分布的建议
    const modeCount = transports.reduce((count, t) => {
      count[t.category] = (count[t.category] || 0) + 1;
      return count;
    }, {} as Record<string, number>);
    
    const walkingRatio = (modeCount.walking || 0) / transports.length;
    if (walkingRatio > 0.7) {
      recommendations.push('步行比例较高，适合喜欢徒步探索的旅行者');
    } else if (walkingRatio < 0.2) {
      recommendations.push('可以考虑增加一些步行路段，体验当地街道文化');
    }
    
    return { warnings, recommendations };
  }
}