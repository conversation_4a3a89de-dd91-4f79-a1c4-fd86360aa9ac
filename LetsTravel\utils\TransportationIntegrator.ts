/**
 * 🚇 交通信息集成器
 * 为活动间生成详细的交通信息，包括步行、地铁、公交等
 */

import { getTransportIcon, getTransportColor, getTransportName } from '../constants/TransportationIcons';
import { TransportDescriptionFormatter } from './TransportDescriptionFormatter';

export interface TransportInfo {
  id: string;
  method: '步行' | '地铁' | '公交' | '出租车' | '巴士';
  duration: number;
  cost: number;
  icon: string;
  description: string;
  startTime: string;
  endTime: string;
  fromActivity: string;
  toActivity: string;
  distance: number;
  route?: string;
  tips?: string[];
}

export interface TransportationPlan {
  [day: number]: TransportInfo[];
}

export interface LocationInfo {
  name: string;
  district: string;
  coordinates?: { lat: number; lng: number };
  transportHub?: boolean;
}

export class TransportationIntegrator {
  
  // 东京主要地区和交通信息
  private static readonly TOKYO_LOCATIONS: { [key: string]: LocationInfo } = {
    '浅草寺': { name: '浅草寺', district: 'asakusa', transportHub: true },
    '东京塔': { name: '东京塔', district: 'minato', transportHub: false },
    '明治神宫': { name: '明治神宫', district: 'shibuya', transportHub: true },
    '上野公园': { name: '上野公园', district: 'ueno', transportHub: true },
    '涩谷': { name: '涩谷', district: 'shibuya', transportHub: true },
    '新宿': { name: '新宿', district: 'shinjuku', transportHub: true },
    '银座': { name: '银座', district: 'chuo', transportHub: true },
    '秋叶原': { name: '秋叶原', district: 'chiyoda', transportHub: true },
    '原宿': { name: '原宿', district: 'shibuya', transportHub: true },
    '筑地': { name: '筑地', district: 'chuo', transportHub: false }
  };
  
  // 地区间距离矩阵（公里）
  private static readonly DISTRICT_DISTANCES: { [key: string]: { [key: string]: number } } = {
    'asakusa': { 'asakusa': 0.3, 'minato': 8.5, 'shibuya': 9.2, 'ueno': 2.1, 'shinjuku': 8.8, 'chuo': 6.3, 'chiyoda': 4.2 },
    'minato': { 'asakusa': 8.5, 'minato': 0.5, 'shibuya': 4.2, 'ueno': 7.8, 'shinjuku': 5.5, 'chuo': 3.1, 'chiyoda': 4.8 },
    'shibuya': { 'asakusa': 9.2, 'minato': 4.2, 'shibuya': 0.4, 'ueno': 8.1, 'shinjuku': 2.3, 'chuo': 5.8, 'chiyoda': 6.2 },
    'ueno': { 'asakusa': 2.1, 'minato': 7.8, 'shibuya': 8.1, 'ueno': 0.3, 'shinjuku': 7.5, 'chuo': 5.2, 'chiyoda': 3.1 },
    'shinjuku': { 'asakusa': 8.8, 'minato': 5.5, 'shibuya': 2.3, 'ueno': 7.5, 'shinjuku': 0.4, 'chuo': 6.8, 'chiyoda': 5.9 },
    'chuo': { 'asakusa': 6.3, 'minato': 3.1, 'shibuya': 5.8, 'ueno': 5.2, 'shinjuku': 6.8, 'chuo': 0.3, 'chiyoda': 2.8 },
    'chiyoda': { 'asakusa': 4.2, 'minato': 4.8, 'shibuya': 6.2, 'ueno': 3.1, 'shinjuku': 5.9, 'chuo': 2.8, 'chiyoda': 0.3 }
  };
  
  /**
   * 🚇 为调度好的活动生成完整交通计划
   */
  static generateTransportationPlan(scheduledActivities: { [day: number]: any[] }): TransportationPlan {
    console.log('🚇 开始生成完整交通计划');
    
    const transportPlan: TransportationPlan = {};
    
    Object.entries(scheduledActivities).forEach(([day, activities]) => {
      const dayNumber = parseInt(day);
      transportPlan[dayNumber] = [];
      
      console.log(`🚇 处理Day ${dayNumber}的交通安排，活动数量: ${activities.length}`);
      
      for (let i = 0; i < activities.length - 1; i++) {
        const currentActivity = activities[i];
        const nextActivity = activities[i + 1];
        
        const transport = this.calculateOptimalTransport(currentActivity, nextActivity, dayNumber);
        transportPlan[dayNumber].push(transport);
        
        console.log(`🚇 ${transport.method}: ${currentActivity.name} -> ${nextActivity.name} (${transport.duration}分钟, RM${transport.cost})`);
      }
      
      console.log(`✅ Day ${dayNumber} 交通计划完成: ${transportPlan[dayNumber].length}段交通`);
    });
    
    return transportPlan;
  }
  
  /**
   * 🎯 计算最优交通方式
   */
  private static calculateOptimalTransport(fromActivity: any, toActivity: any, day: number): TransportInfo {
    const fromLocation = this.getLocationInfo(fromActivity.name);
    const toLocation = this.getLocationInfo(toActivity.name);
    const distance = this.calculateDistance(fromLocation, toLocation);
    
    console.log(`📍 ${fromLocation.name}(${fromLocation.district}) -> ${toLocation.name}(${toLocation.district}), 距离: ${distance}km`);
    
    // 根据距离和时间选择最优交通方式
    const transportOptions = this.getTransportOptions(distance, fromLocation, toLocation);
    const optimalTransport = this.selectOptimalTransport(transportOptions, fromActivity, toActivity);
    
    return {
      id: `transport_day${day}_${fromActivity.id}_to_${toActivity.id}`,
      method: optimalTransport.method,
      duration: optimalTransport.duration,
      cost: optimalTransport.cost,
      icon: optimalTransport.icon,
      description: optimalTransport.description,
      startTime: fromActivity.endTime,
      endTime: this.addMinutes(fromActivity.endTime, optimalTransport.duration),
      fromActivity: fromActivity.name,
      toActivity: toActivity.name,
      distance,
      route: optimalTransport.route,
      tips: optimalTransport.tips
    };
  }
  
  /**
   * 🚶 获取交通选项
   */
  private static getTransportOptions(distance: number, from: LocationInfo, to: LocationInfo): any[] {
    const options = [];
    
    // 步行选项（距离 < 1km）
    if (distance < 1.0) {
      const walkingDuration = Math.max(5, Math.round(distance * 12)); // 12分钟/km

      // 🔧 使用统一的费用计算逻辑
      const formattedWalking = TransportDescriptionFormatter.formatTransportDescription(
        '步行', from.name, to.name, distance * 1000, walkingDuration
      );

      options.push({
        method: '步行',
        duration: walkingDuration,
        cost: formattedWalking.cost, // 确保步行费用为0
        icon: getTransportIcon('walking'),
        description: formattedWalking.description,
        route: '直接步行路线',
        tips: ['建议穿舒适的鞋子', '注意交通安全'],
        priority: distance < 0.5 ? 10 : 8
      });
    }
    
    // 地铁选项（适合大部分距离）
    if (distance > 0.8) {
      const subwayTime = this.calculateSubwayTime(distance, from, to);
      // 🔧 使用统一的费用计算逻辑
      const formattedTransport = TransportDescriptionFormatter.formatTransportDescription(
        '地铁', from.name, to.name, distance * 1000, subwayTime
      );

      options.push({
        method: '地铁',
        duration: subwayTime,
        cost: formattedTransport.cost, // 使用统一的费用计算
        icon: getTransportIcon('subway'),
        description: formattedTransport.description,
        route: this.getSubwayRoute(from, to),
        tips: ['建议购买一日券', '注意地铁运营时间'],
        priority: 9
      });
    }
    
    // 公交选项（中等距离）
    if (distance > 1.5 && distance < 8) {
      const busDuration = Math.round(distance * 8 + 10); // 8分钟/km + 等车时间

      // 🔧 使用统一的费用计算逻辑
      const formattedBus = TransportDescriptionFormatter.formatTransportDescription(
        '公交', from.name, to.name, distance * 1000, busDuration
      );

      options.push({
        method: '公交',
        duration: busDuration,
        cost: formattedBus.cost, // 使用统一的费用计算
        icon: '🚌',
        description: formattedBus.description,
        route: '市内公交线路',
        tips: ['准备零钱', '注意公交时刻表'],
        priority: 6
      });
    }
    
    // 出租车选项（较远距离或特殊情况）
    if (distance > 3) {
      options.push({
        method: '出租车',
        duration: Math.round(distance * 4 + 5), // 4分钟/km + 等车时间
        cost: Math.round(15 + distance * 8), // 起步价15 + 8/km
        icon: '🚕',
        description: `搭乘出租车前往${to.name}`,
        route: '最短路线',
        tips: ['可使用打车APP', '准备现金或信用卡'],
        priority: 4
      });
    }
    
    return options.sort((a, b) => b.priority - a.priority);
  }
  
  /**
   * 🎯 选择最优交通方式
   */
  private static selectOptimalTransport(options: any[], fromActivity: any, toActivity: any): any {
    if (options.length === 0) {
      // 默认选项
      return {
        method: '步行',
        duration: 10,
        cost: 0,
        icon: '🚶',
        description: `前往${toActivity.name}`,
        route: '步行路线',
        tips: ['注意安全']
      };
    }
    
    // 考虑时间约束
    const availableTime = this.calculateAvailableTime(fromActivity, toActivity);
    const suitableOptions = options.filter(option => option.duration <= availableTime);
    
    if (suitableOptions.length > 0) {
      return suitableOptions[0]; // 返回优先级最高的合适选项
    } else {
      return options[0]; // 返回优先级最高的选项
    }
  }
  
  /**
   * 📍 获取位置信息
   */
  private static getLocationInfo(activityName: string): LocationInfo {
    // 尝试精确匹配
    if (this.TOKYO_LOCATIONS[activityName]) {
      return this.TOKYO_LOCATIONS[activityName];
    }
    
    // 尝试模糊匹配
    const matchedKey = Object.keys(this.TOKYO_LOCATIONS).find(key => 
      activityName.includes(key) || key.includes(activityName)
    );
    
    if (matchedKey) {
      return this.TOKYO_LOCATIONS[matchedKey];
    }
    
    // 默认位置（涩谷）
    return {
      name: activityName,
      district: 'shibuya',
      transportHub: false
    };
  }
  
  /**
   * 📏 计算距离
   */
  private static calculateDistance(from: LocationInfo, to: LocationInfo): number {
    if (from.district === to.district) {
      return 0.3; // 同区域内默认300米
    }
    
    const distanceMatrix = this.DISTRICT_DISTANCES[from.district];
    if (distanceMatrix && distanceMatrix[to.district]) {
      return distanceMatrix[to.district];
    }
    
    // 默认距离
    return 5.0;
  }
  
  /**
   * 🚇 计算地铁时间
   */
  private static calculateSubwayTime(distance: number, from: LocationInfo, to: LocationInfo): number {
    let baseTime = Math.round(distance * 3); // 3分钟/km 地铁速度
    
    // 等车和换乘时间
    baseTime += 8; // 等车时间
    
    // 如果需要换乘
    if (from.district !== to.district && !from.transportHub && !to.transportHub) {
      baseTime += 5; // 换乘时间
    }
    
    return Math.max(10, baseTime);
  }
  
  /**
   * 💰 计算地铁费用 - 已废弃，使用TransportDescriptionFormatter统一计算
   * @deprecated 使用TransportDescriptionFormatter.formatTransportDescription()
   */
  private static calculateSubwayCost(distance: number): number {
    // 🔧 已统一到TransportDescriptionFormatter，此方法保留以防兼容性问题
    console.warn('⚠️ calculateSubwayCost已废弃，请使用TransportDescriptionFormatter');
    if (distance < 2) return 8;
    if (distance < 5) return 12;
    if (distance < 10) return 18;
    return 25;
  }
  
  /**
   * 🗺️ 获取地铁路线
   */
  private static getSubwayRoute(from: LocationInfo, to: LocationInfo): string {
    const routes = {
      'asakusa-shibuya': '银座线 → 表参道 → 半藏门线',
      'shibuya-asakusa': '半藏门线 → 表参道 → 银座线',
      'ueno-shibuya': 'JR山手线直达',
      'shibuya-ueno': 'JR山手线直达',
      'minato-shibuya': '日比谷线 → 六本木 → 千代田线',
      'chuo-asakusa': '丸之内线 → 银座 → 银座线'
    };
    
    const routeKey = `${from.district}-${to.district}`;
    return routes[routeKey] || '地铁线路';
  }
  
  /**
   * ⏰ 计算可用时间
   */
  private static calculateAvailableTime(fromActivity: any, toActivity: any): number {
    const fromEndMinutes = this.timeToMinutes(fromActivity.endTime);
    const toStartMinutes = this.timeToMinutes(toActivity.startTime);
    
    return Math.max(5, toStartMinutes - fromEndMinutes);
  }
  
  /**
   * 🔢 时间转换辅助方法
   */
  private static addMinutes(time: string, minutes: number): string {
    const [hours, mins] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60);
    const newMins = totalMinutes % 60;
    
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  }
  
  private static timeToMinutes(time: string): number {
    if (!time || !time.includes(':')) return 0;
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
  
  /**
   * 📊 生成交通统计
   */
  static generateTransportStatistics(transportPlan: TransportationPlan): {
    totalTransports: number;
    totalCost: number;
    totalTime: number;
    methodBreakdown: { [method: string]: number };
    recommendations: string[];
  } {
    let totalTransports = 0;
    let totalCost = 0;
    let totalTime = 0;
    const methodBreakdown: { [method: string]: number } = {};
    
    Object.values(transportPlan).forEach(dayTransports => {
      dayTransports.forEach(transport => {
        totalTransports++;
        totalCost += transport.cost;
        totalTime += transport.duration;
        methodBreakdown[transport.method] = (methodBreakdown[transport.method] || 0) + 1;
      });
    });
    
    const recommendations: string[] = [];
    
    if (methodBreakdown['地铁'] >= 3) {
      recommendations.push('🚇 建议购买地铁一日券，可节省费用');
    }
    
    if (totalCost > 100) {
      recommendations.push('💰 交通费用较高，可考虑步行或公交替代部分行程');
    }
    
    if (methodBreakdown['步行'] >= 2) {
      recommendations.push('🚶 行程包含多段步行，建议穿舒适的鞋子');
    }
    
    return {
      totalTransports,
      totalCost,
      totalTime,
      methodBreakdown,
      recommendations
    };
  }
}
