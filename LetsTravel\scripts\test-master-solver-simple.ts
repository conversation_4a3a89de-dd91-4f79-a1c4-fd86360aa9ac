/**
 * 🧪 简化的Master Solver测试
 */

import { UltraThinkMasterSolver } from '../services/ultra-think/UltraThinkMasterSolver';

async function testMasterSolverSimple() {
  console.log('🧪 简化Master Solver测试');
  console.log('='.repeat(40));

  try {
    const testRequest = {
      destination: '东京',
      duration: 3,
      budget: 8000,
      currency: 'MYR',
      travelers: 2,
      startDate: new Date(),
      preferences: {
        travelStyle: ['cultural'],
        accommodation: ['mid_range'],
        transport: ['public']
      }
    };

    console.log('📤 测试请求:', testRequest);

    const result = await UltraThinkMasterSolver.solveMasterProblems(testRequest);

    console.log('\n📊 测试结果:');
    console.log('✅ 成功:', result.success);
    console.log('🎯 活动数量:', result.activities.length);
    console.log('💰 总预算:', result.budget?.total || 'N/A');
    console.log('📈 质量分:', result.qualityScore);

    if (result.success && result.activities.length > 0) {
      console.log('\n🎉 Master Solver工作正常！');
      
      // 显示前3个活动
      console.log('\n📋 前3个活动:');
      result.activities.slice(0, 3).forEach((activity, index) => {
        console.log(`${index + 1}. ${activity.name} (${activity.type})`);
        console.log(`   时间: ${activity.timing?.startTime}-${activity.timing?.endTime}`);
        console.log(`   费用: ${activity.cost?.amount} ${activity.cost?.currency}`);
      });
    } else {
      console.log('❌ Master Solver失败');
      console.log('问题:', result.issues);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testMasterSolverSimple()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试失败:', error);
      process.exit(1);
    });
}

export { testMasterSolverSimple };
