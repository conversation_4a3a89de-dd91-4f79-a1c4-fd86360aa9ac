/**
 * 💰 真正统一的预算引擎
 * 消除所有预算计算不一致问题，提供单一真实来源的预算计算
 */

export interface TrulyUnifiedBudget {
  total: number;
  byDay: { [day: number]: number };
  byCategory: { [category: string]: number };
  breakdown: BudgetBreakdownItem[];
  validation: BudgetValidation;
  transparency: BudgetTransparency;
}

export interface BudgetBreakdownItem {
  id: string;
  name: string;
  day: number;
  cost: number;
  category: string;
  source: 'activity' | 'transport' | 'meal' | 'generated';
  reasoning: string;
  calculationMethod: string;
  preferenceInfluence?: string;
}

export interface BudgetValidation {
  isConsistent: boolean;
  warnings: string[];
  sources: string[];
  checksPerformed: string[];
  totalValidation: {
    calculated: number;
    verified: number;
    difference: number;
  };
}

export interface BudgetTransparency {
  calculationSource: string;
  lastUpdated: string;
  methodology: string;
  assumptions: string[];
  dataQuality: 'high' | 'medium' | 'low';
}

export class TrulyUnifiedBudgetEngine {
  
  // 统一的费用规则 - 单一真实来源
  private static readonly UNIFIED_COST_RULES = {
    // 文化活动
    cultural: { base: 30, max: 100, preferenceMultiplier: 1.2, currency: 'MYR' },
    cultural_experience: { base: 80, max: 150, preferenceMultiplier: 1.3, currency: 'MYR' },
    cultural_site: { base: 25, max: 80, preferenceMultiplier: 1.1, currency: 'MYR' },
    cultural_workshop: { base: 120, max: 250, preferenceMultiplier: 1.4, currency: 'MYR' },
    
    // 美食活动
    meal: { base: 40, max: 120, preferenceMultiplier: 1.2, currency: 'MYR' },
    food_tour: { base: 120, max: 200, preferenceMultiplier: 1.3, currency: 'MYR' },
    cooking_class: { base: 150, max: 280, preferenceMultiplier: 1.4, currency: 'MYR' },
    fine_dining: { base: 200, max: 400, preferenceMultiplier: 1.2, currency: 'MYR' },
    
    // 其他活动
    attraction: { base: 25, max: 80, preferenceMultiplier: 1.1, currency: 'MYR' },
    shopping: { base: 60, max: 220, preferenceMultiplier: 1.0, currency: 'MYR' },
    transport: { base: 8, max: 35, preferenceMultiplier: 1.0, currency: 'MYR' }
  };
  
  /**
   * 💰 真正统一的预算计算 - 消除所有不一致
   */
  static calculateTrulyUnifiedBudget(
    scheduledActivities: { [day: number]: any[] },
    transportPlan?: { [day: number]: any[] }
  ): TrulyUnifiedBudget {
    
    console.log('💰 开始真正统一的预算计算');
    console.log('📊 输入数据验证:', {
      scheduledDays: Object.keys(scheduledActivities).length,
      totalActivities: Object.values(scheduledActivities).flat().length,
      hasTransportPlan: !!transportPlan
    });
    
    const budget: TrulyUnifiedBudget = {
      total: 0,
      byDay: {},
      byCategory: {},
      breakdown: [],
      validation: {
        isConsistent: true,
        warnings: [],
        sources: ['TrulyUnifiedBudgetEngine'],
        checksPerformed: [],
        totalValidation: { calculated: 0, verified: 0, difference: 0 }
      },
      transparency: {
        calculationSource: 'TrulyUnifiedBudgetEngine v2.0',
        lastUpdated: new Date().toISOString(),
        methodology: '基于活动类型和偏好的统一费用规则',
        assumptions: [
          '费用以马来西亚林吉特(MYR)计算',
          '偏好匹配活动费用有相应加成',
          '交通费用基于实际距离和方式计算',
          '餐饮费用考虑地点和品质'
        ],
        dataQuality: 'high'
      }
    };
    
    // 1. 计算活动费用
    let activityCostTotal = 0;
    Object.entries(scheduledActivities).forEach(([day, activities]) => {
      const dayNumber = parseInt(day);
      let dayTotal = 0;
      
      console.log(`💰 计算Day ${dayNumber}活动费用，活动数量: ${activities.length}`);
      
      activities.forEach((activity, index) => {
        const cost = this.calculateSingleActivityCost(activity);
        const reasoning = this.generateDetailedCostReasoning(activity, cost);
        
        dayTotal += cost;
        activityCostTotal += cost;
        
        // 更新分类预算
        const category = this.getCostCategory(activity);
        budget.byCategory[category] = (budget.byCategory[category] || 0) + cost;
        
        budget.breakdown.push({
          id: `activity_${dayNumber}_${index}`,
          name: activity.name,
          day: dayNumber,
          cost,
          category,
          source: 'activity',
          reasoning,
          calculationMethod: 'UNIFIED_COST_RULES',
          preferenceInfluence: activity.isPreferenceMatch ? activity.preferenceReason : undefined
        });
        
        console.log(`💰 ${activity.name}: RM${cost} (${reasoning})`);
      });
      
      budget.byDay[dayNumber] = dayTotal;
      console.log(`✅ Day ${dayNumber} 活动费用小计: RM${dayTotal}`);
    });
    
    // 2. 计算交通费用
    let transportCostTotal = 0;
    if (transportPlan) {
      Object.entries(transportPlan).forEach(([day, transports]) => {
        const dayNumber = parseInt(day);

        // 🔧 安全检查：确保transports是数组
        if (!transports || !Array.isArray(transports)) {
          console.log(`🚇 计算Day ${dayNumber}交通费用，交通段数: 0 (数据为空或格式错误)`);
          return;
        }

        console.log(`🚇 计算Day ${dayNumber}交通费用，交通段数: ${transports.length}`);

        transports.forEach((transport, index) => {
          const cost = transport.cost || 0;
          transportCostTotal += cost;
          budget.byDay[dayNumber] = (budget.byDay[dayNumber] || 0) + cost;
          
          // 更新交通分类预算
          budget.byCategory['transport'] = (budget.byCategory['transport'] || 0) + cost;
          
          budget.breakdown.push({
            id: `transport_${dayNumber}_${index}`,
            name: transport.description,
            day: dayNumber,
            cost,
            category: 'transport',
            source: 'transport',
            reasoning: `${transport.method}交通费用，距离${transport.distance}km`,
            calculationMethod: 'TRANSPORT_DISTANCE_BASED'
          });
          
          console.log(`🚇 ${transport.description}: RM${cost}`);
        });
      });
    }
    
    // 3. 计算总预算
    budget.total = activityCostTotal + transportCostTotal;
    
    // 4. 执行预算验证
    budget.validation = this.performComprehensiveBudgetValidation(budget);
    
    // 5. 记录透明度信息
    budget.transparency.dataQuality = this.assessDataQuality(budget);
    
    console.log(`✅ 真正统一预算计算完成:`);
    console.log(`   💰 总预算: RM${budget.total}`);
    console.log(`   🏛️ 活动费用: RM${activityCostTotal}`);
    console.log(`   🚇 交通费用: RM${transportCostTotal}`);
    console.log(`   📊 每日分布:`, Object.entries(budget.byDay).map(([day, amount]) => `Day${day}: RM${amount}`));
    
    if (!budget.validation.isConsistent) {
      console.warn('⚠️ 预算验证发现问题:', budget.validation.warnings);
    } else {
      console.log('✅ 预算验证通过，数据一致性良好');
    }
    
    return budget;
  }
  
  /**
   * 💵 计算单个活动费用 - 统一算法
   */
  private static calculateSingleActivityCost(activity: any): number {
    const type = activity.type || 'attraction';
    const rule = this.UNIFIED_COST_RULES[type] || this.UNIFIED_COST_RULES.attraction;
    
    console.log(`🔍 计算${activity.name}费用，类型: ${type}`);
    
    // 🆓 优先检查免费活动
    if (this.isFreeActivity(activity)) {
      console.log(`   🆓 识别为免费活动`);
      return 0;
    }
    
    // 💰 如果活动已有明确费用，使用该费用
    if (activity.cost && typeof activity.cost === 'object' && activity.cost.amount !== undefined) {
      const existingCost = parseFloat(activity.cost.amount) || 0;
      console.log(`   💳 使用活动预设费用: ${activity.cost.currency || 'CNY'}${existingCost}`);
      return Math.max(0, existingCost);
    }
    
    // 💰 如果活动有直接的cost字段（数字）
    if (typeof activity.cost === 'number' && activity.cost >= 0) {
      console.log(`   💳 使用活动直接费用: ${activity.cost}`);
      return activity.cost;
    }
    
    // 🧮 基于规则计算费用
    let cost = rule.base;
    
    // 偏好匹配加成
    if (activity.isPreferenceMatch && activity.preferenceScore > 1.0) {
      cost *= rule.preferenceMultiplier;
      console.log(`   🎯 偏好匹配加成: ${rule.preferenceMultiplier}x`);
    }
    
    // 偏好生成活动使用预设费用
    if (activity.preferenceGenerated && activity.cost) {
      cost = activity.cost;
      console.log(`   ✨ 偏好生成活动，使用预设费用: RM${cost}`);
    }
    
    // 基于名称和描述的智能费用调整
    cost = this.adjustCostByContent(activity, cost, rule);
    
    // 确保在合理范围内
    cost = Math.max(0, Math.min(cost, rule.max));
    
    const finalCost = Math.round(cost);
    console.log(`   💰 最终费用: ${finalCost > 0 ? `CNY${finalCost}` : '免费'}`);
    
    return finalCost;
  }

  /**
   * 🆓 检查是否为免费活动
   */
  private static isFreeActivity(activity: any): boolean {
    const name = (activity.name || activity.name_zh || '').toLowerCase();
    const description = (activity.description || activity.description_zh || '').toLowerCase();
    const category = (activity.category || '').toLowerCase();
    
    // 明确的免费关键词
    const freeKeywords = [
      '免费', 'free', '不收费', '无门票', '开放式',
      '公园', 'park', '广场', 'square', '街道', 'street',
      '海滩', 'beach', '湖泊', 'lake', '河流', 'river',
      '观景台', 'viewpoint', '步行', 'walking', '徒步', 'hiking',
      '市场', 'market', '商业街', 'shopping street',
      '教堂', 'church', '寺庙', 'temple', '清真寺', 'mosque'
    ];
    
    // 特定的免费景点
    const freeAttractions = [
      '浅草寺', '涩谷十字路口', '涩谷交叉路口', '明治神宫外苑',
      '东京站', '新宿公园', '上野公园', '皇居外苑',
      '天安门广场', '外滩', '西湖', '长城远观',
      '维多利亚港', '星光大道', '中环码头',
      'shibuya crossing', 'sensoji temple', 'meiji shrine',
      'victoria harbour', 'avenue of stars', 'the bund'
    ];
    
    // 检查免费关键词
    const hasFreekeyword = freeKeywords.some(keyword => 
      name.includes(keyword) || description.includes(keyword) || category.includes(keyword)
    );
    
    // 检查特定免费景点
    const isFreeAttraction = freeAttractions.some(attraction => 
      name.includes(attraction.toLowerCase()) || description.includes(attraction.toLowerCase())
    );
    
    // 检查活动类型（某些类型通常免费）
    const freeTypes = ['walking', 'viewpoint', 'park', 'square', 'street'];
    const isFreeType = freeTypes.includes(activity.type) || freeTypes.includes(category);
    
    return hasFreekeyword || isFreeAttraction || isFreeType;
  }

  /**
   * 🧮 基于内容智能调整费用
   */
  private static adjustCostByContent(activity: any, baseCost: number, rule: any): number {
    const name = (activity.name || activity.name_zh || '').toLowerCase();
    const description = (activity.description || activity.description_zh || '').toLowerCase();
    const content = `${name} ${description}`;
    
    let cost = baseCost;
    
    // 高端体验关键词
    const premiumKeywords = ['高级', '精选', '豪华', '米其林', '五星', '顶级', '私人', '专属'];
    const hasPremiumKeyword = premiumKeywords.some(keyword => content.includes(keyword));
    
    if (hasPremiumKeyword) {
      cost = Math.min(cost * 1.8, rule.max);
      console.log(`   ⭐ 高端体验加成: 1.8x`);
    }
    
    // 特殊体验关键词
    const specialKeywords = ['体验', '互动', '表演', '演出', '工作坊', '课程'];
    const hasSpecialKeyword = specialKeywords.some(keyword => content.includes(keyword));
    
    if (hasSpecialKeyword) {
      cost = Math.min(cost * 1.3, rule.max);
      console.log(`   🎭 特殊体验加成: 1.3x`);
    }
    
    // 博物馆和文化场所
    const culturalKeywords = ['博物馆', '美术馆', '展览', '文化中心', 'museum', 'gallery', 'exhibition'];
    const isCultural = culturalKeywords.some(keyword => content.includes(keyword));
    
    if (isCultural && cost === 0) {
      cost = 20; // 大多数博物馆有门票
      console.log(`   🏛️ 文化场所基础门票: CNY20`);
    }
    
    // 娱乐场所
    const entertainmentKeywords = ['游乐园', '主题公园', '水族馆', '动物园', 'theme park', 'aquarium', 'zoo'];
    const isEntertainment = entertainmentKeywords.some(keyword => content.includes(keyword));
    
    if (isEntertainment) {
      cost = Math.max(cost, 80); // 娱乐场所通常有较高门票
      console.log(`   🎢 娱乐场所最低门票: CNY80`);
    }
    
    return cost;
  }

  /**
   * 💰 获取活动的统一预算显示
   * 这是UI组件应该调用的唯一预算方法，避免重复显示
   */
  static getUnifiedActivityBudget(activity: any): {
    amount: number;
    currency: string;
    priceLevel: string;
    displayText: string;
    isFree: boolean;
    reasoning: string[];
  } {
    const cost = this.calculateSingleActivityCost(activity);
    const isFree = cost === 0;
    
    // 生成价格等级
    let priceLevel = 'budget';
    if (cost === 0) {
      priceLevel = 'free';
    } else if (cost <= 30) {
      priceLevel = 'budget';
    } else if (cost <= 80) {
      priceLevel = 'moderate';
    } else if (cost <= 150) {
      priceLevel = 'expensive';
    } else {
      priceLevel = 'premium';
    }
    
    // 生成显示文本
    const displayText = isFree ? '免费' : `¥${cost}`;
    
    // 生成费用推理
    const reasoning = this.generateCostReasoning(activity, cost);
    
    return {
      amount: cost,
      currency: 'CNY',
      priceLevel,
      displayText,
      isFree,
      reasoning
    };
  }

  /**
   * 📝 生成费用推理说明
   */
  private static generateCostReasoning(activity: any, cost: number): string[] {
    const reasoning: string[] = [];
    
    if (cost === 0) {
      if (this.isFreeActivity(activity)) {
        reasoning.push('识别为免费活动或景点');
      } else {
        reasoning.push('无需门票费用');
      }
    } else {
      const type = activity.type || 'attraction';
      reasoning.push(`基于${type}类型的标准定价`);
      
      if (activity.isPreferenceMatch) {
        reasoning.push('偏好匹配活动，价值更高');
      }
      
      const name = (activity.name || '').toLowerCase();
      if (name.includes('高级') || name.includes('精选')) {
        reasoning.push('高端体验，费用相应提升');
      }
      
      if (cost > 100) {
        reasoning.push('包含特殊体验或服务');
      }
    }
    
    return reasoning;
  }
  
  /**
   * 📝 生成详细费用推理
   */
  private static generateDetailedCostReasoning(activity: any, cost: number): string {
    const type = activity.type || 'attraction';
    const rule = this.UNIFIED_COST_RULES[type];
    
    if (cost === 0) {
      return '免费体验活动';
    }
    
    let reasoning = `${type}类活动基础费用RM${rule?.base || 30}`;
    
    if (activity.isPreferenceMatch) {
      reasoning += `，偏好匹配加成${((rule?.preferenceMultiplier || 1.1) - 1) * 100}%`;
    }
    
    if (activity.preferenceGenerated) {
      reasoning += '，偏好生成活动';
    }
    
    const name = (activity.name || '').toLowerCase();
    if (name.includes('高级') || name.includes('精选')) {
      reasoning += '，高级体验加成';
    }
    
    return reasoning;
  }
  
  /**
   * 🏷️ 获取费用分类
   */
  private static getCostCategory(activity: any): string {
    const type = activity.type || 'attraction';
    
    // 统一分类映射
    const categoryMap = {
      'cultural': 'cultural',
      'cultural_experience': 'cultural',
      'cultural_site': 'cultural',
      'cultural_workshop': 'cultural',
      'meal': 'meal',
      'food_tour': 'meal',
      'cooking_class': 'meal',
      'fine_dining': 'meal',
      'attraction': 'attraction',
      'shopping': 'shopping',
      'transport': 'transport'
    };
    
    return categoryMap[type] || 'other';
  }
  
  /**
   * ✅ 执行全面的预算验证
   */
  private static performComprehensiveBudgetValidation(budget: TrulyUnifiedBudget): BudgetValidation {
    const validation: BudgetValidation = {
      isConsistent: true,
      warnings: [],
      sources: ['TrulyUnifiedBudgetEngine'],
      checksPerformed: [],
      totalValidation: { calculated: 0, verified: 0, difference: 0 }
    };
    
    // 检查1: 每日预算总和验证
    const dailySum = Object.values(budget.byDay).reduce((sum, amount) => sum + amount, 0);
    validation.totalValidation.calculated = budget.total;
    validation.totalValidation.verified = dailySum;
    validation.totalValidation.difference = Math.abs(budget.total - dailySum);
    
    if (validation.totalValidation.difference > 0.01) {
      validation.isConsistent = false;
      validation.warnings.push(`每日预算总和(${dailySum})与总预算(${budget.total})不一致，差异${validation.totalValidation.difference}`);
    }
    validation.checksPerformed.push('每日预算总和验证');
    
    // 检查2: 分类预算验证
    const categorySum = Object.values(budget.byCategory).reduce((sum, amount) => sum + amount, 0);
    if (Math.abs(categorySum - budget.total) > 0.01) {
      validation.isConsistent = false;
      validation.warnings.push(`分类预算总和(${categorySum})与总预算(${budget.total})不一致`);
    }
    validation.checksPerformed.push('分类预算验证');
    
    // 检查3: 明细预算验证
    const breakdownSum = budget.breakdown.reduce((sum, item) => sum + item.cost, 0);
    if (Math.abs(breakdownSum - budget.total) > 0.01) {
      validation.isConsistent = false;
      validation.warnings.push(`明细预算总和(${breakdownSum})与总预算(${budget.total})不一致`);
    }
    validation.checksPerformed.push('明细预算验证');
    
    // 检查4: 预算合理性验证
    if (budget.total > 5000) {
      validation.warnings.push('总预算过高，请检查是否存在计算错误');
    } else if (budget.total < 100) {
      validation.warnings.push('总预算过低，可能缺少必要费用项目');
    }
    validation.checksPerformed.push('预算合理性验证');
    
    // 检查5: 每日预算平衡性验证
    const dailyAmounts = Object.values(budget.byDay);
    const maxDaily = Math.max(...dailyAmounts);
    const minDaily = Math.min(...dailyAmounts);
    if (maxDaily > minDaily * 3) {
      validation.warnings.push('每日预算差异较大，建议重新平衡活动分配');
    }
    validation.checksPerformed.push('每日预算平衡性验证');
    
    console.log(`✅ 预算验证完成: ${validation.checksPerformed.length}项检查，${validation.warnings.length}个警告`);
    
    return validation;
  }
  
  /**
   * 📊 评估数据质量
   */
  private static assessDataQuality(budget: TrulyUnifiedBudget): 'high' | 'medium' | 'low' {
    let qualityScore = 100;
    
    // 数据完整性检查
    const hasAllCategories = ['cultural', 'meal', 'attraction'].every(cat => budget.byCategory[cat] > 0);
    if (!hasAllCategories) qualityScore -= 20;
    
    // 预算合理性检查
    if (budget.validation.warnings.length > 0) qualityScore -= budget.validation.warnings.length * 10;
    
    // 数据一致性检查
    if (!budget.validation.isConsistent) qualityScore -= 30;
    
    if (qualityScore >= 80) return 'high';
    if (qualityScore >= 60) return 'medium';
    return 'low';
  }
  
  /**
   * 📊 获取真实预算描述
   */
  static getRealBudgetText(amount: number, currency: string = 'MYR'): string {
    if (amount === 0) return '免费';
    return `${currency}${amount}`;
  }

  /**
   * 📊 获取价格等级描述
   */
  static getPriceLevelText(amount: number): string {
    if (amount === 0) return '免费';
    if (amount < 25) return '经济型';
    if (amount < 60) return '中等价位';
    if (amount < 100) return '较高价位';
    return '高端消费';
  }

  /**
   * 📈 获取预算范围显示
   */
  static getBudgetRangeText(dayBudget: number): string {
    const variance = dayBudget * 0.1; // ±10%变化
    const min = Math.round(dayBudget - variance);
    const max = Math.round(dayBudget + variance);

    if (min === max || dayBudget === 0) return `RM${dayBudget}`;
    return `约RM${min}-${max}`;
  }

  /**
   * 📊 生成预算报告
   */
  static generateBudgetReport(budget: TrulyUnifiedBudget): string {
    const report = [
      '💰 真正统一预算报告',
      '='.repeat(40),
      `💵 总预算: RM${budget.total}`,
      `📊 数据质量: ${budget.transparency.dataQuality}`,
      `✅ 验证状态: ${budget.validation.isConsistent ? '通过' : '有警告'}`,
      '',
      '📋 每日预算分布:',
      ...Object.entries(budget.byDay).map(([day, amount]) => 
        `   Day ${day}: RM${amount}`
      ),
      '',
      '🏷️ 分类预算分布:',
      ...Object.entries(budget.byCategory).map(([category, amount]) => 
        `   ${category}: RM${amount} (${((amount / budget.total) * 100).toFixed(1)}%)`
      ),
      '',
      '🔍 验证信息:',
      `   执行检查: ${budget.validation.checksPerformed.length}项`,
      `   发现警告: ${budget.validation.warnings.length}个`,
      ...budget.validation.warnings.map(warning => `   ⚠️ ${warning}`),
      '',
      '📈 透明度信息:',
      `   计算来源: ${budget.transparency.calculationSource}`,
      `   计算方法: ${budget.transparency.methodology}`,
      `   最后更新: ${new Date(budget.transparency.lastUpdated).toLocaleString()}`
    ];
    
    return report.join('\n');
  }
}
