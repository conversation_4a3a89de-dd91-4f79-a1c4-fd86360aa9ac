/**
 * 🎯 Ultra Think专用DayCard组件
 * 直接使用UltraThinkMasterSolverV2生成的dayPlan数据，避免数据转换丢失
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';

interface UltraThinkDayCardProps {
  dayPlan: any;
  dayNumber: number;
  weather?: string;
  date?: string;
  onActivityPress?: (activity: any) => void;
}

export default function UltraThinkDayCard({
  dayPlan,
  dayNumber,
  weather = '🌤️ 晴朗 8°C',
  date,
  onActivityPress
}: UltraThinkDayCardProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  console.log(`🎯 UltraThinkDayCard Day${dayNumber} 渲染开始`);
  console.log(`📊 DayPlan数据:`, {
    activities: dayPlan.activities?.length || 0,
    meals: dayPlan.meals?.length || 0,
    transportation: dayPlan.transportation?.length || 0
  });

  // 🔄 合并所有时间线项目并按时间排序
  const createTimelineItems = () => {
    const items = [];
    
    // 添加活动
    if (dayPlan.activities) {
      dayPlan.activities.forEach(activity => {
        items.push({
          ...activity,
          itemType: 'activity',
          startTime: activity.timing?.startTime || '09:00',
          endTime: activity.timing?.endTime || '10:30',
          duration: activity.timing?.duration || 90
        });
      });
    }
    
    // 添加餐饮
    if (dayPlan.meals) {
      dayPlan.meals.forEach(meal => {
        items.push({
          ...meal,
          itemType: 'meal',
          startTime: meal.time || '12:00',
          endTime: addMinutesToTime(meal.time || '12:00', meal.duration || 90),
          duration: meal.duration || 90
        });
      });
    }
    
    // 添加交通
    if (dayPlan.transportation) {
      dayPlan.transportation.forEach(transport => {
        items.push({
          ...transport,
          itemType: 'transport',
          startTime: transport.departureTime || '09:00',
          endTime: addMinutesToTime(transport.departureTime || '09:00', transport.duration || 30),
          duration: transport.duration || 30
        });
      });
    }
    
    // 按时间排序
    return items.sort((a, b) => a.startTime.localeCompare(b.startTime));
  };

  // 🕐 时间计算辅助函数
  const addMinutesToTime = (timeStr: string, minutes: number): string => {
    const [hours, mins] = timeStr.split(':').map(Number);
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMins = totalMinutes % 60;
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  };

  // 🎨 获取时间线颜色
  const getTimelineDotColor = (item: any): string => {
    if (item.itemType === 'transport') return '#999999'; // 灰色
    if (item.itemType === 'meal' || item.type === 'restaurant') return '#FF9500'; // 橙色
    return '#007AFF'; // 蓝色
  };

  // 💰 获取费用显示
  const getCostDisplay = (item: any): string => {
    if (item.itemType === 'transport' && item.name?.includes('步行')) return 'RM0';
    
    if (item.cost) {
      if (typeof item.cost === 'object' && item.cost.amount !== undefined) {
        return `RM${item.cost.amount}`;
      }
      if (typeof item.cost === 'number') {
        return `RM${item.cost}`;
      }
    }
    
    return 'RM0';
  };

  // 📝 获取项目名称
  const getItemName = (item: any): string => {
    return item.name || item.title || '未命名项目';
  };

  // 🔄 切换展开状态
  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const timelineItems = createTimelineItems();
  const totalCost = timelineItems.reduce((sum, item) => {
    const cost = item.cost?.amount || item.cost || 0;
    return sum + (typeof cost === 'number' ? cost : 0);
  }, 0);

  return (
    <View style={styles.container}>
      {/* 📅 日期头部 */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.dayTitle}>Day {dayNumber}</Text>
          <Text style={styles.dateText}>{date || `12月${13 + dayNumber}日`}</Text>
          <Text style={styles.dayOfWeek}>周{['日', '一', '二', '三', '四', '五', '六'][dayNumber % 7]}</Text>
        </View>
        <View style={styles.headerRight}>
          <Text style={styles.weatherText}>{weather}</Text>
          <Text style={styles.budgetText}>RM{totalCost}</Text>
        </View>
      </View>

      {/* 📊 时间线 */}
      <ScrollView style={styles.timeline} showsVerticalScrollIndicator={false}>
        {timelineItems.map((item, index) => {
          const isExpanded = expandedItems.has(item.id);
          
          return (
            <TouchableOpacity
              key={item.id}
              style={styles.timelineItem}
              onPress={() => {
                toggleExpanded(item.id);
                onActivityPress?.(item);
              }}
            >
              {/* 时间线左侧 */}
              <View style={styles.timelineLeft}>
                <Text style={styles.timeText}>{item.startTime}</Text>
                <View style={[styles.timelineDot, { backgroundColor: getTimelineDotColor(item) }]} />
                {index < timelineItems.length - 1 && <View style={styles.timelineLine} />}
              </View>

              {/* 时间线右侧内容 */}
              <View style={styles.timelineRight}>
                <View style={styles.itemHeader}>
                  <Text style={styles.itemTitle}>{getItemName(item)}</Text>
                  <Text style={styles.itemCost}>{getCostDisplay(item)}</Text>
                </View>
                
                <Text style={styles.timeRange}>
                  {item.startTime} - {item.endTime}
                </Text>

                {item.location && (
                  <Text style={styles.locationText}>
                    📍 {item.location.name || item.location.district || '位置待定'}
                  </Text>
                )}

                {/* 展开内容 */}
                {isExpanded && (
                  <View style={styles.expandedContent}>
                    <Text style={styles.description}>
                      {item.description || `体验${getItemName(item)}的独特魅力`}
                    </Text>
                    
                    {item.highlights && item.highlights.length > 0 && (
                      <View style={styles.highlightsSection}>
                        <Text style={styles.sectionTitle}>✨ 亮点</Text>
                        {item.highlights.slice(0, 2).map((highlight, idx) => (
                          <Text key={idx} style={styles.highlightItem}>• {highlight}</Text>
                        ))}
                      </View>
                    )}
                    
                    {item.tips && item.tips.length > 0 && (
                      <View style={styles.tipsSection}>
                        <Text style={styles.sectionTitle}>💡 建议</Text>
                        {item.tips.slice(0, 2).map((tip, idx) => (
                          <Text key={idx} style={styles.tipItem}>• {tip}</Text>
                        ))}
                      </View>
                    )}
                  </View>
                )}
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* 📊 日程摘要 */}
      <View style={styles.summary}>
        <Text style={styles.summaryText}>
          {timelineItems.length}个活动 • 预算RM{totalCost} • {weather}
        </Text>
      </View>
    </View>
  );
}

// 🕐 时间计算辅助函数（组件外部）
const addMinutesToTime = (timeStr: string, minutes: number): string => {
  const [hours, mins] = timeStr.split(':').map(Number);
  const totalMinutes = hours * 60 + mins + minutes;
  const newHours = Math.floor(totalMinutes / 60) % 24;
  const newMins = totalMinutes % 60;
  return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  dayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  dayOfWeek: {
    fontSize: 12,
    color: '#999',
  },
  weatherText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  budgetText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  timeline: {
    maxHeight: 400,
    paddingHorizontal: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    paddingVertical: 12,
  },
  timelineLeft: {
    width: 60,
    alignItems: 'center',
    marginRight: 12,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
    marginBottom: 8,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginBottom: 4,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#e0e0e0',
    marginTop: 4,
  },
  timelineRight: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  itemCost: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
  },
  timeRange: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  expandedContent: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  description: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 12,
  },
  highlightsSection: {
    marginBottom: 8,
  },
  tipsSection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 13,
    color: '#333',
    fontWeight: '600',
    marginBottom: 4,
  },
  highlightItem: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    marginBottom: 2,
  },
  tipItem: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    marginBottom: 2,
  },
  summary: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    backgroundColor: '#f8f9fa',
  },
  summaryText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});
