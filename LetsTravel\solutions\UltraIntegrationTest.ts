/**
 * 🧪 Ultra集成测试套件 - 验证所有修复是否生效
 */

import {
  IntelligentLLMManager,
  PreciseBudgetCalculator, // 保留导入但标记为废弃
  SmartTimeFormatter,
  ActivityIconManager,
  Journey,
  Activity,
  DayPlan
} from './UltraJourneyOptimizer';
import { TrulyUnifiedBudgetEngine } from '../utils/TrulyUnifiedBudgetEngine';

import { ComprehensiveActivityGenerator } from './EnhancedActivityGenerator';
import { UnifiedAPIManager } from './APIDataSourceFixer';

export interface TestConfig {
  destination: string;
  duration: number;
  travelers: number;
  budget: number;
  currency: string;
  travelStyle: string[];
  runFullTest: boolean;
  enableAPITests: boolean;
  enableLLMTests: boolean;
}

export interface TestResult {
  testName: string;
  passed: boolean;
  score: number;
  issues: string[];
  suggestions: string[];
  executionTime: number;
  details?: any;
}

export interface UltraTestReport {
  overallScore: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  executionTime: number;
  results: TestResult[];
  summary: {
    llmIssuesFixed: boolean;
    apiIssuesFixed: boolean;
    budgetIssuesFixed: boolean;
    timeFormatFixed: boolean;
    activityCountFixed: boolean;
    uiIssuesFixed: boolean;
  };
}

/**
 * 🚀 Ultra集成测试器
 */
export class UltraIntegrationTester {
  private llmManager: IntelligentLLMManager;
  private activityGenerator: ComprehensiveActivityGenerator;
  private apiManager: UnifiedAPIManager;
  private testResults: TestResult[] = [];

  constructor(
    apiConfig: {
      serpApiKey: string;
      googleMapsApiKey: string;
      openWeatherApiKey: string;
      timeout: number;
      retryAttempts: number;
      fallbackEnabled: boolean;
    }
  ) {
    this.llmManager = new IntelligentLLMManager();
    this.activityGenerator = new ComprehensiveActivityGenerator();
    this.apiManager = new UnifiedAPIManager(apiConfig);
  }

  /**
   * 🎯 运行完整的Ultra测试套件
   */
  async runUltraTestSuite(config: TestConfig): Promise<UltraTestReport> {
    console.log('🚀 开始Ultra集成测试套件...');
    const startTime = Date.now();
    this.testResults = [];

    // 1. 🧠 LLM智能管理测试
    if (config.enableLLMTests) {
      await this.testLLMIntelligentManagement();
    }

    // 2. 🔧 API数据源修复测试
    if (config.enableAPITests) {
      await this.testAPIDataSourceFixes();
    }

    // 3. 💰 预算计算精确性测试
    await this.testBudgetCalculationAccuracy(config);

    // 4. ⏰ 时间格式化测试
    await this.testTimeFormattingFixes();

    // 5. 🎯 活动生成完整性测试
    await this.testActivityGenerationCompleteness(config);

    // 6. 🎨 UI组件优化测试
    await this.testUIComponentOptimization();

    // 7. 🔄 端到端集成测试
    if (config.runFullTest) {
      await this.testEndToEndIntegration(config);
    }

    const executionTime = Date.now() - startTime;
    return this.generateUltraTestReport(executionTime);
  }

  /**
   * 🧠 测试LLM智能管理
   */
  private async testLLMIntelligentManagement(): Promise<void> {
    const startTime = Date.now();
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    try {
      // 测试模型选择逻辑
      const plannerModel = await this.llmManager.selectOptimalModel('planner');
      const agentModel = await this.llmManager.selectOptimalModel('agent');

      if (!plannerModel || !agentModel) {
        issues.push('模型选择失败');
        score -= 30;
      }

      // 测试失败模型记录
      this.llmManager.recordModelPerformance('test-model', false);
      this.llmManager.recordModelPerformance('test-model', true);

      // 测试降级响应生成
      const fallbackResponse = this.llmManager.generateFallbackResponse('planner', {
        destination: '东京',
        duration: 3,
        budget: 5000,
        currency: 'USD'
      });

      if (!fallbackResponse || fallbackResponse.length < 100) {
        issues.push('降级响应质量不足');
        score -= 20;
      }

      if (issues.length === 0) {
        suggestions.push('LLM智能管理系统运行正常');
      }

    } catch (error) {
      issues.push(`LLM管理器错误: ${error.message}`);
      score = 0;
    }

    this.testResults.push({
      testName: 'LLM智能管理测试',
      passed: issues.length === 0,
      score,
      issues,
      suggestions,
      executionTime: Date.now() - startTime
    });
  }

  /**
   * 🔧 测试API数据源修复
   */
  private async testAPIDataSourceFixes(): Promise<void> {
    const startTime = Date.now();
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    try {
      // 健康检查
      const healthCheck = await this.apiManager.healthCheck();
      
      if (!healthCheck.overall) {
        if (!healthCheck.serp) {
          issues.push('SerpAPI连接失败');
          score -= 40;
        }
        if (!healthCheck.maps) {
          issues.push('Google Maps API连接失败');
          score -= 40;
        }
      }

      // 测试航班搜索（包含降级）
      const flights = await this.apiManager.searchFlights({
        origin: '东京',
        destination: '大阪',
        departureDate: '2025-08-01',
        passengers: 2,
        class: 'economy'
      });

      if (!flights || flights.length === 0) {
        issues.push('航班搜索无结果');
        score -= 30;
      } else if (flights[0].fallback) {
        suggestions.push('使用了航班降级数据，建议检查SerpAPI配置');
        score -= 10;
      }

      // 测试路线规划
      const directions = await this.apiManager.getDirections({
        origin: { lat: 35.6762, lng: 139.6503 },
        destination: { lat: 35.6895, lng: 139.6917 },
        mode: 'driving'
      });

      if (!directions) {
        issues.push('路线规划失败');
        score -= 20;
      } else if (directions.fallback) {
        suggestions.push('使用了路线降级数据，建议检查Google Maps API配置');
        score -= 5;
      }

    } catch (error) {
      issues.push(`API测试错误: ${error.message}`);
      score = Math.max(0, score - 50);
    }

    this.testResults.push({
      testName: 'API数据源修复测试',
      passed: score >= 70,
      score,
      issues,
      suggestions,
      executionTime: Date.now() - startTime
    });
  }

  /**
   * 💰 测试预算计算精确性
   */
  private async testBudgetCalculationAccuracy(config: TestConfig): Promise<void> {
    const startTime = Date.now();
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    try {
      // 测试预算分配
      // 使用统一预算引擎替代废弃的PreciseBudgetCalculator
      const mockActivity = {
        id: 'budget-test',
        name: '预算测试',
        cost: { amount: config.budget, currency: config.currency },
        location: { name: config.destination }
      };

      const budgetInfo = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(mockActivity);
      const budgetAllocation = {
        breakdown: {
          activities: budgetInfo.displayAmount * 0.6,
          accommodation: budgetInfo.displayAmount * 0.25,
          transportation: budgetInfo.displayAmount * 0.15
        },
        total: budgetInfo.displayAmount,
        currency: budgetInfo.displayCurrency
      };

      // 验证预算分配合理性
      const totalAllocated = Object.values(budgetAllocation.breakdown).reduce((a, b) => a + b, 0);
      const deviation = Math.abs(totalAllocated - config.budget);

      if (deviation > 1) {
        issues.push(`预算分配偏差: ${deviation} ${config.currency}`);
        score -= 20;
      }

      // 验证各项预算比例合理性
      const accommodationRatio = budgetAllocation.breakdown.accommodation / config.budget;
      if (accommodationRatio < 0.2 || accommodationRatio > 0.6) {
        issues.push('住宿预算比例不合理');
        score -= 10;
      }

      // 测试预算一致性验证
      const mockActivities: Activity[] = [
        {
          id: 'test1',
          name: 'Test Activity',
          nameZh: '测试活动',
          type: 'attraction',
          startTime: '09:00',
          endTime: '11:00',
          duration: 120,
          cost: 100,
          currency: config.currency,
          location: { lat: 0, lng: 0, address: 'Test', addressZh: '测试' },
          description: 'Test',
          descriptionZh: '测试',
          icon: '🏛️',
          priority: 3,
          energyLevel: 3,
          weatherSensitive: false,
          bookingRequired: false,
          tags: ['test']
        }
      ];

      // 使用统一预算引擎进行预算验证
      const totalActivityCost = mockActivities.reduce((total, activity) => {
        const budget = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(activity);
        return total + (budget.isFree ? 0 : budget.displayAmount);
      }, 0);

      const validation = {
        isValid: totalActivityCost <= config.budget,
        issues: totalActivityCost > config.budget ? ['总活动费用超出预算'] : []
      };

      if (!validation.isValid) {
        issues.push(...validation.issues);
        score -= validation.issues.length * 5;
      }

      if (budgetAllocation.confidence < 0.7) {
        suggestions.push('预算计算信心度较低，建议提供更多旅行偏好信息');
        score -= 5;
      }

    } catch (error) {
      issues.push(`预算计算错误: ${error.message}`);
      score = 0;
    }

    this.testResults.push({
      testName: '预算计算精确性测试',
      passed: score >= 80,
      score,
      issues,
      suggestions,
      executionTime: Date.now() - startTime
    });
  }

  /**
   * ⏰ 测试时间格式化修复
   */
  private async testTimeFormattingFixes(): Promise<void> {
    const startTime = Date.now();
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    try {
      // 测试时间格式化
      const testCases = [
        { start: '9:00', end: '11:30', expected: '09:00 - 11:30' },
        { start: '14:15', end: '16:45', expected: '14:15 - 16:45' },
        { start: '10:0', end: '12:15', expected: '10:00 - 12:15' }
      ];

      for (const testCase of testCases) {
        try {
          const result = SmartTimeFormatter.formatTimeRange(testCase.start, testCase.end);
          if (result !== testCase.expected) {
            issues.push(`时间格式化错误: ${testCase.start}-${testCase.end} 期望 ${testCase.expected}, 实际 ${result}`);
            score -= 15;
          }
        } catch (error) {
          issues.push(`时间格式化异常: ${error.message}`);
          score -= 20;
        }
      }

      // 测试时间分配
      const mockActivities = [
        { duration: 120 },
        { duration: 90 },
        { duration: 60 }
      ];

      const scheduledActivities = SmartTimeFormatter.distributeActivitiesInDay(
        mockActivities,
        '09:00',
        '18:00',
        15
      );

      if (scheduledActivities.length !== mockActivities.length) {
        issues.push('活动时间分配数量不匹配');
        score -= 20;
      }

      // 验证时间连续性
      for (let i = 1; i < scheduledActivities.length; i++) {
        const prevEnd = scheduledActivities[i - 1].endTime;
        const currentStart = scheduledActivities[i].startTime;
        
        if (prevEnd >= currentStart) {
          issues.push('活动时间重叠');
          score -= 15;
        }
      }

    } catch (error) {
      issues.push(`时间格式化测试错误: ${error.message}`);
      score = 0;
    }

    this.testResults.push({
      testName: '时间格式化修复测试',
      passed: score >= 85,
      score,
      issues,
      suggestions,
      executionTime: Date.now() - startTime
    });
  }

  /**
   * 🎯 测试活动生成完整性
   */
  private async testActivityGenerationCompleteness(config: TestConfig): Promise<void> {
    const startTime = Date.now();
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    try {
      const generationConfig = {
        destination: config.destination,
        duration: config.duration,
        travelers: config.travelers,
        budget: config.budget,
        currency: config.currency,
        travelStyle: config.travelStyle,
        minActivitiesPerDay: 4,
        maxActivitiesPerDay: 8,
        includeTransport: true,
        includeAccommodation: true,
        includeFlights: true,
        includeMeals: true
      };

      const dayPlans = await this.activityGenerator.generateDailyActivities(generationConfig);

      if (dayPlans.length !== config.duration) {
        issues.push(`日程数量不匹配: 期望${config.duration}天, 实际${dayPlans.length}天`);
        score -= 30;
      }

      // 检查每日活动完整性
      for (const dayPlan of dayPlans) {
        const activities = dayPlan.activities;
        
        if (activities.length < generationConfig.minActivitiesPerDay) {
          issues.push(`第${dayPlan.dayNumber}天活动不足: ${activities.length}个`);
          score -= 15;
        }

        // 检查活动类型多样性
        const types = new Set(activities.map(a => a.type));
        if (types.size < 3) {
          issues.push(`第${dayPlan.dayNumber}天活动类型单一: 只有${types.size}种类型`);
          score -= 10;
        }

        // 检查必要活动类型
        const hasAttraction = activities.some(a => a.type === 'attraction');
        const hasMeal = activities.some(a => a.type === 'meal');
        
        if (!hasAttraction) {
          issues.push(`第${dayPlan.dayNumber}天缺少景点活动`);
          score -= 10;
        }
        
        if (!hasMeal) {
          issues.push(`第${dayPlan.dayNumber}天缺少餐饮活动`);
          score -= 5;
        }

        // 检查中文名称
        const missingChineseNames = activities.filter(a => !a.nameZh || a.nameZh === a.name);
        if (missingChineseNames.length > 0) {
          issues.push(`第${dayPlan.dayNumber}天有${missingChineseNames.length}个活动缺少中文名称`);
          score -= missingChineseNames.length * 2;
        }
      }

      // 检查航班和住宿
      const firstDay = dayPlans[0];
      const lastDay = dayPlans[dayPlans.length - 1];
      
      if (!firstDay.activities.some(a => a.type === 'flight')) {
        suggestions.push('第一天建议包含抵达航班');
      }
      
      if (!lastDay.activities.some(a => a.type === 'flight')) {
        suggestions.push('最后一天建议包含离开航班');
      }

    } catch (error) {
      issues.push(`活动生成测试错误: ${error.message}`);
      score = 0;
    }

    this.testResults.push({
      testName: '活动生成完整性测试',
      passed: score >= 75,
      score,
      issues,
      suggestions,
      executionTime: Date.now() - startTime
    });
  }

  /**
   * 🎨 测试UI组件优化
   */
  private async testUIComponentOptimization(): Promise<void> {
    const startTime = Date.now();
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    try {
      // 测试图标管理器
      const testActivities = [
        { type: 'attraction', name: 'Tokyo Tower', nameZh: '东京塔' },
        { type: 'transport', name: 'Subway', nameZh: '地铁' },
        { type: 'meal', name: 'Sushi Restaurant', nameZh: '寿司店' },
        { type: 'accommodation', name: 'Hotel', nameZh: '酒店' }
      ];

      for (const activity of testActivities) {
        const icon = ActivityIconManager.getIconForActivity(activity);
        const color = ActivityIconManager.getColorForActivity(activity);
        
        if (!icon || icon === '📍') {
          issues.push(`${activity.type}类型活动图标不够具体`);
          score -= 5;
        }
        
        if (!color || color === '#FF6B6B') {
          suggestions.push(`${activity.type}类型可以使用更具特色的颜色`);
        }
      }

      // 测试时间格式显示
      const timeTests = [
        { start: '09:00', end: '11:30' },
        { start: '14:15', end: '16:45' }
      ];

      for (const timeTest of timeTests) {
        const formatted = SmartTimeFormatter.formatTimeRange(timeTest.start, timeTest.end);
        if (!formatted.includes(' - ')) {
          issues.push('时间范围格式不正确');
          score -= 10;
        }
      }

    } catch (error) {
      issues.push(`UI组件测试错误: ${error.message}`);
      score = 0;
    }

    this.testResults.push({
      testName: 'UI组件优化测试',
      passed: score >= 90,
      score,
      issues,
      suggestions,
      executionTime: Date.now() - startTime
    });
  }

  /**
   * 🔄 端到端集成测试
   */
  private async testEndToEndIntegration(config: TestConfig): Promise<void> {
    const startTime = Date.now();
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 100;

    try {
      console.log('🔄 运行端到端集成测试...');
      
      // 模拟完整的行程生成流程
      const generationConfig = {
        destination: config.destination,
        duration: config.duration,
        travelers: config.travelers,
        budget: config.budget,
        currency: config.currency,
        travelStyle: config.travelStyle,
        minActivitiesPerDay: 4,
        maxActivitiesPerDay: 8,
        includeTransport: true,
        includeAccommodation: true,
        includeFlights: true,
        includeMeals: true
      };

      // 生成完整行程
      const dayPlans = await this.activityGenerator.generateDailyActivities(generationConfig);
      
      // 计算总预算
      const totalCost = dayPlans.reduce((sum, day) => sum + day.totalCost, 0);
      
      // 验证预算一致性
      const budgetDeviation = Math.abs(totalCost - config.budget) / config.budget;
      if (budgetDeviation > 0.3) {
        issues.push(`预算偏差过大: ${(budgetDeviation * 100).toFixed(1)}%`);
        score -= 20;
      }

      // 验证质量评分
      const avgQualityScore = dayPlans.reduce((sum, day) => sum + day.qualityScore, 0) / dayPlans.length;
      if (avgQualityScore < 70) {
        issues.push(`平均质量评分过低: ${avgQualityScore.toFixed(1)}`);
        score -= 15;
      }

      // 验证活动完整性
      const totalActivities = dayPlans.reduce((sum, day) => sum + day.activities.length, 0);
      const expectedMinActivities = config.duration * 4;
      
      if (totalActivities < expectedMinActivities) {
        issues.push(`活动总数不足: ${totalActivities}个, 期望至少${expectedMinActivities}个`);
        score -= 25;
      }

      if (score >= 80) {
        suggestions.push('端到端集成测试通过，系统运行正常');
      }

    } catch (error) {
      issues.push(`端到端测试错误: ${error.message}`);
      score = 0;
    }

    this.testResults.push({
      testName: '端到端集成测试',
      passed: score >= 70,
      score,
      issues,
      suggestions,
      executionTime: Date.now() - startTime
    });
  }

  /**
   * 📊 生成Ultra测试报告
   */
  private generateUltraTestReport(executionTime: number): UltraTestReport {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const overallScore = this.testResults.reduce((sum, r) => sum + r.score, 0) / totalTests;

    const summary = {
      llmIssuesFixed: this.testResults.find(r => r.testName.includes('LLM'))?.passed || false,
      apiIssuesFixed: this.testResults.find(r => r.testName.includes('API'))?.passed || false,
      budgetIssuesFixed: this.testResults.find(r => r.testName.includes('预算'))?.passed || false,
      timeFormatFixed: this.testResults.find(r => r.testName.includes('时间'))?.passed || false,
      activityCountFixed: this.testResults.find(r => r.testName.includes('活动'))?.passed || false,
      uiIssuesFixed: this.testResults.find(r => r.testName.includes('UI'))?.passed || false
    };

    return {
      overallScore,
      totalTests,
      passedTests,
      failedTests,
      executionTime,
      results: this.testResults,
      summary
    };
  }
}

export { UltraIntegrationTester };
