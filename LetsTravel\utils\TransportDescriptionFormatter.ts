/**
 * 🚇 交通描述格式化器
 * 
 * 将模糊的交通描述转换为具体的地点和距离信息
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import { getTransportIcon, getTransportName } from '../constants/TransportationIcons';

// ===== 接口定义 =====

/**
 * 🎯 精确交通信息
 */
export interface PreciseTransportInfo {
  method: string;           // 交通方式
  icon: string;            // 图标
  fromLocation: string;    // 具体出发地点
  toLocation: string;      // 具体目的地点
  distance: number;        // 距离（米）
  duration: number;        // 时长（分钟）
  cost: number;           // 费用
  description: string;     // 格式化描述
  route?: string;         // 路线信息
  tips?: string[];        // 实用提示
}

/**
 * 🗺️ 地点信息
 */
export interface LocationDetails {
  name: string;           // 地点名称
  address?: string;       // 详细地址
  district?: string;      // 区域
  coordinates?: {         // 坐标
    lat: number;
    lng: number;
  };
  type?: string;         // 地点类型
}

// ===== 核心格式化器 =====

export class TransportDescriptionFormatter {
  
  /**
   * 🎯 格式化交通描述
   * 
   * 将"从传统茶道体验步行至当地历史博物馆"
   * 转换为"从浅草寺步行500米至东京国立博物馆"
   */
  static formatTransportDescription(
    method: string,
    fromActivity: string,
    toActivity: string,
    distance?: number,
    duration?: number
  ): PreciseTransportInfo {
    
    // 1. 提取真实地点名称
    const fromLocation = this.extractRealLocationName(fromActivity);
    const toLocation = this.extractRealLocationName(toActivity);
    
    // 2. 计算或估算距离和时长
    const preciseDistance = distance || this.estimateDistance(fromLocation, toLocation, method);
    const preciseDuration = duration || this.estimateDuration(preciseDistance, method);
    
    // 3. 获取交通方式信息
    const transportIcon = getTransportIcon(method);
    const transportName = getTransportName(method, 'zh');
    
    // 4. 计算费用
    const cost = this.calculateTransportCost(method, preciseDistance);
    
    // 5. 生成格式化描述
    const description = this.generatePreciseDescription(
      transportName,
      fromLocation,
      toLocation,
      preciseDistance,
      preciseDuration
    );
    
    // 6. 生成路线信息
    const route = this.generateRouteInfo(method, fromLocation, toLocation);
    
    // 7. 生成实用提示
    const tips = this.generateTransportTips(method, preciseDistance, preciseDuration);
    
    return {
      method: transportName,
      icon: transportIcon,
      fromLocation,
      toLocation,
      distance: preciseDistance,
      duration: preciseDuration,
      cost,
      description,
      route,
      tips
    };
  }
  
  /**
   * 🏷️ 提取真实地点名称
   * 
   * 从活动描述中提取具体的地点名称
   */
  private static extractRealLocationName(activityDescription: string): string {
    // 移除模糊描述词汇
    const vagueTerms = [
      '传统', '当地', '著名', '知名', '热门', '推荐', '精选',
      '体验', '探索', '参观', '游览', '品尝', '享受'
    ];
    
    let cleanName = activityDescription;
    
    // 移除模糊词汇
    vagueTerms.forEach(term => {
      cleanName = cleanName.replace(new RegExp(term, 'g'), '');
    });
    
    // 提取具体地点名称的模式匹配
    const locationPatterns = [
      // 寺庙神社
      /([^传统当地著名]*寺|[^传统当地著名]*神宫|[^传统当地著名]*神社)/,
      // 博物馆
      /([^传统当地著名]*博物馆|[^传统当地著名]*美术馆)/,
      // 塔楼建筑
      /([^传统当地著名]*塔|[^传统当地著名]*楼)/,
      // 公园花园
      /([^传统当地著名]*公园|[^传统当地著名]*花园|[^传统当地著名]*园)/,
      // 市场商店
      /([^传统当地著名]*市场|[^传统当地著名]*商店街|[^传统当地著名]*购物中心)/,
      // 车站机场
      /([^传统当地著名]*站|[^传统当地著名]*机场)/,
      // 地区名称
      /([^传统当地著名]*区|[^传统当地著名]*町|[^传统当地著名]*街)/
    ];
    
    // 尝试匹配具体地点
    for (const pattern of locationPatterns) {
      const match = cleanName.match(pattern);
      if (match && match[1]) {
        const locationName = match[1].trim();
        if (locationName.length > 1) {
          return locationName;
        }
      }
    }
    
    // 如果没有匹配到，使用预定义的真实地点映射
    const realLocationMap: Record<string, string> = {
      '茶道体验': '表参道茶室',
      '历史博物馆': '东京国立博物馆',
      '传统市场': '筑地外市场',
      '古老寺庙': '浅草寺',
      '现代艺术馆': '森美术馆',
      '购物区': '银座商业区',
      '观景台': '东京晴空塔',
      '文化中心': '东京文化会馆',
      '美食街': '新横滨拉面博物馆',
      '公园': '上野公园'
    };
    
    // 查找映射
    for (const [key, value] of Object.entries(realLocationMap)) {
      if (activityDescription.includes(key)) {
        return value;
      }
    }
    
    // 最后降级：返回清理后的名称或原始描述的前10个字符
    return cleanName.trim() || activityDescription.substring(0, 10) + '...';
  }
  
  /**
   * 📏 估算距离（米）
   */
  private static estimateDistance(from: string, to: string, method: string): number {
    // 基于地点类型的距离估算
    const distanceMap: Record<string, number> = {
      // 同区域内
      '寺-博物馆': 800,
      '市场-寺庙': 1200,
      '车站-商店': 300,
      '公园-博物馆': 600,
      
      // 跨区域
      '银座-浅草': 4500,
      '新宿-涩谷': 3200,
      '上野-东京站': 2800,
      
      // 默认距离
      'default': 1000
    };
    
    // 简单的距离估算逻辑
    if (from.includes('银座') && to.includes('浅草')) return 4500;
    if (from.includes('新宿') && to.includes('涩谷')) return 3200;
    if (from.includes('上野') && to.includes('东京')) return 2800;
    
    // 基于交通方式的默认距离
    const methodDistanceMap: Record<string, number> = {
      '步行': 600,
      'walking': 600,
      '地铁': 2500,
      'subway': 2500,
      '公交': 1800,
      'bus': 1800,
      '出租车': 3000,
      'taxi': 3000,
      '火车': 15000,
      'train': 15000
    };
    
    return methodDistanceMap[method] || 1000;
  }
  
  /**
   * ⏱️ 估算时长（分钟）
   */
  private static estimateDuration(distance: number, method: string): number {
    const speedMap: Record<string, number> = {
      '步行': 5,      // 5km/h
      'walking': 5,
      '地铁': 25,     // 25km/h (包含等待)
      'subway': 25,
      '公交': 20,     // 20km/h (包含等待)
      'bus': 20,
      '出租车': 30,   // 30km/h (城市交通)
      'taxi': 30,
      '火车': 60,     // 60km/h
      'train': 60
    };
    
    const speed = speedMap[method] || 25;
    const distanceKm = distance / 1000;
    const timeHours = distanceKm / speed;
    const timeMinutes = Math.ceil(timeHours * 60);
    
    // 最少3分钟，最多120分钟
    return Math.max(3, Math.min(120, timeMinutes));
  }
  
  /**
   * 💰 计算交通费用
   */
  private static calculateTransportCost(method: string, distance: number): number {
    const costMap: Record<string, (distance: number) => number> = {
      '步行': () => 0,
      'walking': () => 0,
      '地铁': (d) => Math.max(2, Math.min(8, Math.ceil(d / 1000) * 2)),
      'subway': (d) => Math.max(2, Math.min(8, Math.ceil(d / 1000) * 2)),
      '公交': (d) => Math.max(2, Math.min(6, Math.ceil(d / 1000) * 1.5)),
      'bus': (d) => Math.max(2, Math.min(6, Math.ceil(d / 1000) * 1.5)),
      '出租车': (d) => Math.max(8, Math.ceil(d / 1000) * 4),
      'taxi': (d) => Math.max(8, Math.ceil(d / 1000) * 4),
      '火车': (d) => Math.max(15, Math.ceil(d / 1000) * 2),
      'train': (d) => Math.max(15, Math.ceil(d / 1000) * 2)
    };

    // 🔧 修复: 未知交通方式默认为免费，而不是地铁费用
    const calculator = costMap[method] || (() => 0);
    return calculator(distance);
  }
  
  /**
   * 📝 生成精确描述
   */
  private static generatePreciseDescription(
    method: string,
    from: string,
    to: string,
    distance: number,
    duration: number
  ): string {
    const distanceText = distance < 1000 
      ? `${distance}米` 
      : `${(distance / 1000).toFixed(1)}公里`;
    
    return `从${from}${method}${distanceText}至${to} (约${duration}分钟)`;
  }
  
  /**
   * 🗺️ 生成路线信息
   */
  private static generateRouteInfo(method: string, from: string, to: string): string {
    if (method === '步行') {
      return `${from} → 步行路线 → ${to}`;
    } else if (method === '地铁') {
      return `${from} → 地铁站 → ${to}`;
    } else if (method === '公交') {
      return `${from} → 公交站 → ${to}`;
    } else {
      return `${from} → ${to}`;
    }
  }
  
  /**
   * 💡 生成实用提示
   */
  private static generateTransportTips(method: string, distance: number, duration: number): string[] {
    const tips: string[] = [];
    
    if (method === '步行') {
      tips.push('建议穿舒适的鞋子');
      if (distance > 800) tips.push('路程较长，可考虑其他交通方式');
      tips.push('注意交通安全');
    } else if (method === '地铁') {
      tips.push('建议购买一日券更划算');
      tips.push('注意地铁运营时间');
      if (duration > 30) tips.push('路程较长，建议提前规划');
    } else if (method === '公交') {
      tips.push('准备零钱或IC卡');
      tips.push('注意公交班次时间');
    } else if (method === '出租车') {
      tips.push('高峰时段可能堵车');
      tips.push('建议使用打车软件');
    }
    
    return tips;
  }
  
  /**
   * 🔄 批量格式化交通描述
   */
  static batchFormatTransportDescriptions(
    transportList: Array<{
      method: string;
      fromActivity: string;
      toActivity: string;
      distance?: number;
      duration?: number;
    }>
  ): PreciseTransportInfo[] {
    return transportList.map(transport => 
      this.formatTransportDescription(
        transport.method,
        transport.fromActivity,
        transport.toActivity,
        transport.distance,
        transport.duration
      )
    );
  }
}

// ===== 默认导出 =====
export default TransportDescriptionFormatter;
