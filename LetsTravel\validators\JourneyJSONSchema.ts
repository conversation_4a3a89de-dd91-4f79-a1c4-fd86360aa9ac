/**
 * 🔍 Journey JSON Schema Validator
 * 确保数据结构的完整性和一致性
 */

import { JSONDataTransfer } from '../types/JourneyDataTypes';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  fixedData?: any;
}

export interface ValidationError {
  type: string;
  message: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  path?: string;
  field?: string;
  activityIndex?: number;
}

export interface ValidationWarning {
  type: string;
  message: string;
  path?: string;
  activityIndex?: number;
}

export class JourneyJSONValidator {
  
  /**
   * 🔍 验证完整的Journey JSON数据
   */
  validate(data: JSONDataTransfer): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    console.log('🔍 开始JSON数据验证');
    
    try {
      // 1. 基础结构验证
      this.validateBasicStructure(data, errors);
      
      // 2. Journey信息验证
      this.validateJourneyInfo(data.payload?.journey, errors, warnings);
      
      // 3. DayPlans验证
      this.validateDayPlans(data.payload?.dayPlans, errors, warnings);
      
      // 4. 预算数据验证
      this.validateBudget(data.payload?.budget, errors, warnings);
      
      // 5. 元数据验证
      this.validateMetadata(data.metadata, errors, warnings);
      
      const isValid = errors.filter(e => e.severity === 'critical').length === 0;
      
      console.log(`✅ 验证完成: ${isValid ? '通过' : '失败'}, ${errors.length}个错误, ${warnings.length}个警告`);
      
      return {
        isValid,
        errors,
        warnings,
        fixedData: isValid ? data : this.autoFixData(data, errors, warnings)
      };
      
    } catch (error) {
      console.error('❌ 验证过程中出错:', error);
      errors.push({
        type: 'VALIDATION_ERROR',
        message: `验证过程异常: ${error.message}`,
        severity: 'critical'
      });
      
      return { isValid: false, errors, warnings };
    }
  }
  
  /**
   * 🏗️ 验证基础结构
   */
  private validateBasicStructure(data: any, errors: ValidationError[]): void {
    if (!data) {
      errors.push({
        type: 'MISSING_DATA',
        message: '数据为空',
        severity: 'critical'
      });
      return;
    }
    
    // 检查必需的顶级字段
    const requiredFields = ['version', 'timestamp', 'source', 'target', 'dataType', 'payload', 'metadata'];
    requiredFields.forEach(field => {
      if (!data[field]) {
        errors.push({
          type: 'MISSING_FIELD',
          message: `缺少必需字段: ${field}`,
          severity: 'critical',
          field
        });
      }
    });
    
    // 检查payload结构
    if (data.payload) {
      const payloadFields = ['journey', 'dayPlans', 'budget'];
      payloadFields.forEach(field => {
        if (!data.payload[field]) {
          errors.push({
            type: 'MISSING_PAYLOAD_FIELD',
            message: `payload缺少必需字段: ${field}`,
            severity: 'critical',
            field
          });
        }
      });
    }
  }
  
  /**
   * 🎯 验证Journey信息
   */
  private validateJourneyInfo(journey: any, errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!journey) return;
    
    // 必需字段检查
    const requiredFields = ['id', 'title', 'destination', 'duration', 'startDate', 'endDate'];
    requiredFields.forEach(field => {
      if (!journey[field]) {
        errors.push({
          type: 'MISSING_JOURNEY_FIELD',
          message: `Journey缺少必需字段: ${field}`,
          severity: 'high',
          field
        });
      }
    });
    
    // 数据类型验证
    if (journey.duration && (typeof journey.duration !== 'number' || journey.duration < 1)) {
      errors.push({
        type: 'INVALID_DURATION',
        message: `无效的行程天数: ${journey.duration}`,
        severity: 'high'
      });
    }
    
    if (journey.travelers && (typeof journey.travelers !== 'number' || journey.travelers < 1)) {
      errors.push({
        type: 'INVALID_TRAVELERS',
        message: `无效的旅行者数量: ${journey.travelers}`,
        severity: 'medium'
      });
    }
    
    // 日期验证
    if (journey.startDate && journey.endDate) {
      const startDate = new Date(journey.startDate);
      const endDate = new Date(journey.endDate);
      
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        errors.push({
          type: 'INVALID_DATE_FORMAT',
          message: '日期格式错误',
          severity: 'high'
        });
      } else if (endDate <= startDate) {
        errors.push({
          type: 'INVALID_DATE_RANGE',
          message: '结束日期必须晚于开始日期',
          severity: 'high'
        });
      }
    }
  }
  
  /**
   * 📅 验证DayPlans
   */
  private validateDayPlans(dayPlans: any[], errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!dayPlans || !Array.isArray(dayPlans)) {
      errors.push({
        type: 'INVALID_DAY_PLANS',
        message: 'dayPlans必须是数组',
        severity: 'critical'
      });
      return;
    }
    
    dayPlans.forEach((dayPlan, dayIndex) => {
      this.validateDayPlan(dayPlan, dayIndex, errors, warnings);
    });
  }
  
  /**
   * 📅 验证单个DayPlan
   */
  private validateDayPlan(dayPlan: any, dayIndex: number, errors: ValidationError[], warnings: ValidationWarning[]): void {
    const dayPath = `dayPlans[${dayIndex}]`;
    
    // 必需字段检查
    if (!dayPlan.dayNumber || typeof dayPlan.dayNumber !== 'number') {
      errors.push({
        type: 'INVALID_DAY_NUMBER',
        message: `第${dayIndex + 1}天的dayNumber无效`,
        severity: 'high',
        path: dayPath
      });
    }
    
    if (!dayPlan.date) {
      errors.push({
        type: 'MISSING_DATE',
        message: `第${dayIndex + 1}天缺少日期`,
        severity: 'high',
        path: dayPath
      });
    }
    
    // 验证活动
    if (dayPlan.activities && Array.isArray(dayPlan.activities)) {
      dayPlan.activities.forEach((activity: any, activityIndex: number) => {
        this.validateActivity(activity, dayIndex, activityIndex, errors, warnings);
      });
    }
    
    // 验证餐饮
    if (dayPlan.meals && Array.isArray(dayPlan.meals)) {
      dayPlan.meals.forEach((meal: any, mealIndex: number) => {
        this.validateMeal(meal, dayIndex, mealIndex, errors, warnings);
      });
    }
    
    // 验证交通
    if (dayPlan.transportation && Array.isArray(dayPlan.transportation)) {
      dayPlan.transportation.forEach((transport: any, transportIndex: number) => {
        this.validateTransportation(transport, dayIndex, transportIndex, errors, warnings);
      });
    }
  }
  
  /**
   * 🎯 验证活动
   */
  private validateActivity(activity: any, dayIndex: number, activityIndex: number, errors: ValidationError[], warnings: ValidationWarning[]): void {
    const activityPath = `dayPlans[${dayIndex}].activities[${activityIndex}]`;
    
    // 必需字段检查
    const requiredFields = ['id', 'name', 'type', 'timing', 'location', 'cost'];
    requiredFields.forEach(field => {
      if (!activity[field]) {
        errors.push({
          type: 'MISSING_ACTIVITY_FIELD',
          message: `活动缺少必需字段: ${field}`,
          severity: 'high',
          path: activityPath,
          field,
          activityIndex
        });
      }
    });
    
    // 时间格式验证
    if (activity.timing) {
      if (!this.isValidTimeFormat(activity.timing.startTime)) {
        warnings.push({
          type: 'INVALID_TIME_FORMAT',
          message: `活动开始时间格式错误: ${activity.timing.startTime}`,
          path: activityPath,
          activityIndex
        });
      }
      
      if (!this.isValidTimeFormat(activity.timing.endTime)) {
        warnings.push({
          type: 'INVALID_TIME_FORMAT',
          message: `活动结束时间格式错误: ${activity.timing.endTime}`,
          path: activityPath,
          activityIndex
        });
      }
      
      if (activity.timing.duration && (typeof activity.timing.duration !== 'number' || activity.timing.duration <= 0)) {
        warnings.push({
          type: 'INVALID_DURATION',
          message: `活动持续时间无效: ${activity.timing.duration}`,
          path: activityPath,
          activityIndex
        });
      }
    }
    
    // 费用验证
    if (activity.cost) {
      if (typeof activity.cost.amount !== 'number' || activity.cost.amount < 0) {
        errors.push({
          type: 'INVALID_COST_AMOUNT',
          message: `活动费用金额无效: ${activity.cost.amount}`,
          severity: 'medium',
          path: activityPath,
          activityIndex
        });
      }
      
      if (!activity.cost.currency) {
        warnings.push({
          type: 'MISSING_CURRENCY',
          message: '活动费用缺少货币单位',
          path: activityPath,
          activityIndex
        });
      }
    }
  }
  
  /**
   * 🍽️ 验证餐饮
   */
  private validateMeal(meal: any, dayIndex: number, mealIndex: number, errors: ValidationError[], warnings: ValidationWarning[]): void {
    const mealPath = `dayPlans[${dayIndex}].meals[${mealIndex}]`;
    
    if (!meal.name) {
      warnings.push({
        type: 'MISSING_MEAL_NAME',
        message: '餐饮缺少名称',
        path: mealPath
      });
    }
    
    if (!meal.type || !['breakfast', 'lunch', 'dinner', 'snack', 'cafe'].includes(meal.type)) {
      warnings.push({
        type: 'INVALID_MEAL_TYPE',
        message: `无效的餐饮类型: ${meal.type}`,
        path: mealPath
      });
    }
  }
  
  /**
   * 🚗 验证交通
   */
  private validateTransportation(transport: any, dayIndex: number, transportIndex: number, errors: ValidationError[], warnings: ValidationWarning[]): void {
    const transportPath = `dayPlans[${dayIndex}].transportation[${transportIndex}]`;
    
    if (!transport.type || !['walking', 'taxi', 'subway', 'bus', 'train', 'flight'].includes(transport.type)) {
      warnings.push({
        type: 'INVALID_TRANSPORT_TYPE',
        message: `无效的交通类型: ${transport.type}`,
        path: transportPath
      });
    }
    
    if (!transport.from || !transport.to) {
      errors.push({
        type: 'MISSING_TRANSPORT_LOCATIONS',
        message: '交通缺少起点或终点',
        severity: 'medium',
        path: transportPath
      });
    }
  }
  
  /**
   * 💰 验证预算
   */
  private validateBudget(budget: any, errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!budget) return;
    
    if (!budget.total || typeof budget.total.amount !== 'number') {
      errors.push({
        type: 'INVALID_BUDGET_TOTAL',
        message: '预算总额无效',
        severity: 'medium'
      });
    }
  }
  
  /**
   * 📊 验证元数据
   */
  private validateMetadata(metadata: any, errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!metadata) {
      warnings.push({
        type: 'MISSING_METADATA',
        message: '缺少元数据'
      });
      return;
    }
    
    if (typeof metadata.qualityScore !== 'number' || metadata.qualityScore < 0 || metadata.qualityScore > 1) {
      warnings.push({
        type: 'INVALID_QUALITY_SCORE',
        message: `质量分数无效: ${metadata.qualityScore}`
      });
    }
  }
  
  /**
   * ⏰ 验证时间格式
   */
  private isValidTimeFormat(timeString: string): boolean {
    if (!timeString || typeof timeString !== 'string') return false;
    return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(timeString);
  }
  
  /**
   * 🔧 自动修复数据
   */
  private autoFixData(data: any, errors: ValidationError[], warnings: ValidationWarning[]): any {
    console.log('🔧 开始自动修复数据');
    
    const fixedData = JSON.parse(JSON.stringify(data));
    
    // 修复时间格式
    warnings.forEach(warning => {
      if (warning.type === 'INVALID_TIME_FORMAT' && warning.path) {
        // 实现时间格式修复逻辑
        this.fixTimeFormat(fixedData, warning.path);
      }
    });
    
    // 修复缺失字段
    errors.forEach(error => {
      if (error.severity !== 'critical') {
        this.fixMissingField(fixedData, error);
      }
    });
    
    console.log('✅ 数据自动修复完成');
    return fixedData;
  }
  
  private fixTimeFormat(data: any, path: string): void {
    // 实现时间格式修复逻辑
    console.log(`🔧 修复时间格式: ${path}`);
  }
  
  private fixMissingField(data: any, error: ValidationError): void {
    // 实现缺失字段修复逻辑
    console.log(`🔧 修复缺失字段: ${error.field}`);
  }
}
