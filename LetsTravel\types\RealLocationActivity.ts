/**
 * 🏷️ 真实地点活动接口
 *
 * 定义包含真实地点名称、本地化名称、精确分类和验证状态的活动接口
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 核心接口定义 =====

/**
 * 🎯 真实地点活动接口
 */
export interface RealLocationActivity {
  id: string;
  type: ActivityType;

  // 真实地点信息
  realLocation: RealLocationInfo;

  // 本地化名称
  localizedNames: LocalizedNames;

  // 精确分类
  preciseCategory: PreciseCategory;

  // 验证状态
  verificationStatus: VerificationStatus;

  // 时间和费用
  timing: ActivityTiming;
  cost: ActivityCost;

  // 扩展信息
  metadata: ActivityMetadata;
}

/**
 * 🗺️ 真实地点信息
 */
export interface RealLocationInfo {
  // 官方名称
  officialName: string;           // "浅草寺"
  officialNameEn: string;         // "Sensoji Temple"
  
  // 详细地址
  address: {
    full: string;                 // "东京都台东区浅草2-3-1"
    district: string;             // "台东区"
    area: string;                 // "浅草"
    postalCode?: string;          // "111-0032"
  };
  
  // 地理坐标
  coordinates: {
    lat: number;                  // 35.7148
    lng: number;                  // 139.7967
    accuracy: 'high' | 'medium' | 'low';
  };
  
  // 地点类型
  locationType: LocationType;
  
  // 营业信息
  operatingInfo?: {
    hours: string;                // "06:00-17:00"
    closedDays?: string[];        // ["无"]
    seasonalHours?: Record<string, string>;
  };
  
  // 联系信息
  contact?: {
    phone?: string;
    website?: string;
    email?: string;
  };
}

/**
 * 🌍 本地化名称
 */
export interface LocalizedNames {
  // 中文名称
  zh: {
    primary: string;              // "浅草寺"
    alternative?: string[];       // ["浅草观音寺"]
    description: string;          // "东京最古老的寺庙"
  };
  
  // 英文名称
  en: {
    primary: string;              // "Sensoji Temple"
    alternative?: string[];       // ["Asakusa Temple"]
    description: string;          // "Tokyo's oldest temple"
  };
  
  // 日文名称（如果适用）
  ja?: {
    primary: string;              // "浅草寺"
    hiragana?: string;            // "せんそうじ"
    romaji?: string;              // "Sensoji"
  };
  
  // 其他语言
  other?: Record<string, {
    primary: string;
    description: string;
  }>;
}

/**
 * 🎯 精确分类
 */
export interface PreciseCategory {
  // 主分类
  primary: PrimaryCategoryType;
  
  // 子分类
  secondary: string[];            // ["佛教寺庙", "历史建筑", "文化遗产"]
  
  // 标签
  tags: string[];                 // ["寺庙", "历史", "文化", "建筑", "宗教"]
  
  // 特色
  features: string[];             // ["雷门", "五重塔", "仲见世通"]
  
  // 适合人群
  suitableFor: string[];          // ["家庭", "文化爱好者", "摄影师"]
  
  // 最佳时间
  bestTime: {
    season: string[];             // ["春季", "秋季"]
    timeOfDay: string[];          // ["早晨", "傍晚"]
    duration: number;             // 90 (分钟)
  };
}

/**
 * ✅ 验证状态
 */
export interface VerificationStatus {
  // 整体验证状态
  overall: 'verified' | 'partial' | 'unverified' | 'flagged';
  
  // 详细验证信息
  details: {
    locationAccuracy: VerificationLevel;    // 地点准确性
    nameAccuracy: VerificationLevel;        // 名称准确性
    categoryAccuracy: VerificationLevel;    // 分类准确性
    operatingHours: VerificationLevel;      // 营业时间准确性
    contactInfo: VerificationLevel;         // 联系信息准确性
  };
  
  // 验证来源
  sources: VerificationSource[];
  
  // 最后验证时间
  lastVerified: Date;
  
  // 验证者信息
  verifiedBy?: {
    type: 'system' | 'user' | 'admin';
    id: string;
    confidence: number;           // 0-1
  };
  
  // 问题标记
  issues?: {
    type: 'outdated' | 'incorrect' | 'missing' | 'duplicate';
    description: string;
    reportedBy?: string;
    reportedAt: Date;
  }[];
}

/**
 * ⏰ 活动时间信息
 */
export interface ActivityTiming {
  // 建议时长
  recommendedDuration: number;    // 分钟
  
  // 时间范围
  timeRange?: {
    start: string;                // "09:00"
    end: string;                  // "10:30"
  };
  
  // 最佳访问时间
  optimalTimes: {
    season: string[];
    timeOfDay: string[];
    weekday: boolean;             // 是否适合工作日
    weekend: boolean;             // 是否适合周末
  };
  
  // 时间灵活性
  flexibility: 'fixed' | 'flexible' | 'anytime';
}

/**
 * 💰 活动费用信息
 */
export interface ActivityCost {
  // 基础费用
  base: {
    amount: number;
    currency: string;
    type: 'free' | 'paid' | 'donation';
  };
  
  // 费用明细
  breakdown?: {
    admission?: number;           // 门票
    guide?: number;              // 导游
    transport?: number;          // 交通
    extras?: number;             // 额外费用
  };
  
  // 折扣信息
  discounts?: {
    type: string;                // "学生", "老人", "团体"
    amount: number;              // 折扣金额或百分比
    conditions: string;          // 条件说明
  }[];
  
  // 费用说明
  notes?: string[];
}

/**
 * 📊 活动元数据
 */
export interface ActivityMetadata {
  // 创建信息
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  
  // 数据来源
  dataSources: string[];          // ["google_places", "wikipedia", "official_website"]
  
  // 质量评分
  qualityScore: {
    overall: number;              // 0-1
    accuracy: number;             // 准确性
    completeness: number;         // 完整性
    freshness: number;            // 新鲜度
    relevance: number;            // 相关性
  };
  
  // 用户反馈
  userFeedback?: {
    rating: number;               // 1-5
    reviewCount: number;
    commonComments: string[];
  };
  
  // 系统标记
  systemFlags?: {
    isPopular: boolean;
    isRecommended: boolean;
    needsUpdate: boolean;
    hasIssues: boolean;
  };
}

// ===== 枚举类型定义 =====

/**
 * 🏷️ 活动类型
 */
export enum ActivityType {
  ATTRACTION = 'attraction',
  RESTAURANT = 'restaurant',
  SHOPPING = 'shopping',
  ENTERTAINMENT = 'entertainment',
  CULTURAL = 'cultural',
  NATURE = 'nature',
  SPORTS = 'sports',
  ACCOMMODATION = 'accommodation',
  TRANSPORT = 'transport',
  SERVICE = 'service'
}

/**
 * 🗺️ 地点类型
 */
export enum LocationType {
  TEMPLE = 'temple',
  SHRINE = 'shrine',
  MUSEUM = 'museum',
  PARK = 'park',
  MARKET = 'market',
  RESTAURANT = 'restaurant',
  SHOP = 'shop',
  HOTEL = 'hotel',
  STATION = 'station',
  AIRPORT = 'airport',
  LANDMARK = 'landmark',
  BUILDING = 'building',
  STREET = 'street',
  DISTRICT = 'district'
}

/**
 * 🎯 主分类类型
 */
export enum PrimaryCategoryType {
  RELIGIOUS = 'religious',
  HISTORICAL = 'historical',
  CULTURAL = 'cultural',
  NATURAL = 'natural',
  COMMERCIAL = 'commercial',
  RECREATIONAL = 'recreational',
  EDUCATIONAL = 'educational',
  ARCHITECTURAL = 'architectural',
  MODERN = 'modern',
  TRADITIONAL = 'traditional'
}

/**
 * ✅ 验证级别
 */
export enum VerificationLevel {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  NONE = 'none'
}

/**
 * 📋 验证来源
 */
export interface VerificationSource {
  type: 'official_website' | 'google_places' | 'wikipedia' | 'user_report' | 'system_check';
  url?: string;
  confidence: number;             // 0-1
  lastChecked: Date;
}

// ===== 工具类型 =====

/**
 * 🔍 活动搜索过滤器
 */
export interface RealLocationActivityFilter {
  type?: ActivityType[];
  locationType?: LocationType[];
  primaryCategory?: PrimaryCategoryType[];
  verificationLevel?: VerificationLevel;
  coordinates?: {
    lat: number;
    lng: number;
    radius: number;              // 米
  };
  operatingHours?: {
    isOpen: boolean;
    time?: string;               // "14:30"
  };
  cost?: {
    min?: number;
    max?: number;
    currency?: string;
  };
}

/**
 * 📊 活动统计信息
 */
export interface ActivityStatistics {
  totalActivities: number;
  verifiedActivities: number;
  byType: Record<ActivityType, number>;
  byLocation: Record<LocationType, number>;
  averageQualityScore: number;
  lastUpdated: Date;
}

// ===== 默认导出 =====
export default RealLocationActivity;
