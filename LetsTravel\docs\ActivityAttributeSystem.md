# 🏷️ 活动属性系统文档

## 📋 概述

活动属性系统是用于替换模糊标签系统（如"精选体验"、"超值选择"等）的新架构，提供真实、准确的活动属性信息。

## 🎯 核心目标

1. **移除模糊标签**：不再使用"精选体验"、"超值选择"等主观描述
2. **提供真实属性**：显示营业时间、评分、具体价格等客观信息
3. **提升用户体验**：让用户获得更准确、实用的信息
4. **支持多数据源**：兼容API数据、模板数据和用户输入

## 🏗️ 系统架构

### 核心组件

```
ActivityAttributeSystem/
├── types/
│   └── ActivityAttributes.ts          # 类型定义
├── services/
│   └── ActivityAttributeManager.ts    # 属性管理器
├── components/
│   └── ActivityAttributeDisplay.tsx   # 显示组件
└── __tests__/
    └── ActivityAttributeManager.test.ts # 单元测试
```

### 数据流

```
原始活动数据 → ActivityAttributeManager → 真实属性 → ActivityAttributeDisplay → 用户界面
```

## 🔧 使用方法

### 1. 基础使用

```typescript
import { ActivityAttributeManager } from '../services/activity/ActivityAttributeManager';
import { ActivityAttributeDisplay } from '../components/activity/ActivityAttributeDisplay';

// 获取管理器实例
const manager = ActivityAttributeManager.getInstance();

// 生成真实属性
const attributes = manager.generateRealAttributes(activity, 'template');

// 在组件中显示
<ActivityAttributeDisplay 
  attributes={attributes}
  mode="compact"
  showAttributes={[
    AttributeType.OPERATING_HOURS,
    AttributeType.RATING,
    AttributeType.PRICE_RANGE
  ]}
/>
```

### 2. 高级配置

```typescript
// 详细模式显示
<ActivityAttributeDisplay 
  attributes={attributes}
  mode="detailed"
  theme="dark"
  showIcons={true}
  showAttributes={[
    AttributeType.OPERATING_HOURS,
    AttributeType.RATING,
    AttributeType.PRICE_RANGE,
    AttributeType.AVAILABILITY,
    AttributeType.BOOKING,
    AttributeType.SEASONALITY
  ]}
/>
```

### 3. 自定义格式化

```typescript
const displayConfig = {
  mode: 'compact',
  theme: 'light',
  showAttributes: [AttributeType.PRICE_RANGE],
  locale: 'zh-CN'
};

const formatted = manager.formatAttributeDisplay(attributes, displayConfig);
console.log(formatted[AttributeType.PRICE_RANGE]); // "门票价格: MYR30-50"
```

## 📊 属性类型

### 1. 营业时间 (OperatingHours)

```typescript
{
  isCurrentlyOpen: boolean;     // 当前是否营业
  todayHours: string;          // 今日营业时间
  weeklyHours: WeeklyHours;    // 一周营业时间
  specialNotes?: string;       // 特殊说明
}
```

**显示效果**：
- 紧凑模式：`营业中 09:00-18:00`
- 详细模式：`09:00-18:00 (营业中)`

### 2. 评分信息 (RatingInfo)

```typescript
{
  overallRating: number;       // 总体评分 (1-5)
  reviewCount: number;         // 评价总数
  source: string;             // 评分来源
  subRatings?: object;        // 子评分
  trend?: string;             // 评分趋势
}
```

**显示效果**：
- 紧凑模式：`⭐ 4.3`
- 详细模式：`⭐ 4.3 (125条评价)`

### 3. 价格区间 (PriceRangeInfo)

```typescript
{
  level: PriceLevel;          // 价格等级
  range: {                    // 价格范围
    min: number;
    max: number;
    currency: string;
  };
  type: string;               // 价格类型
  description: string;        // 价格说明
}
```

**显示效果**：
- 免费：`免费`
- 有价格：`MYR30-50` 或 `门票价格: MYR30-50`

### 4. 可用性状态 (AvailabilityStatus)

```typescript
{
  status: string;             // 当前状态
  description: string;        // 状态描述
  nextAvailable?: string;     // 下次可用时间
  crowdLevel: number;         // 拥挤程度 (1-5)
  bestTimeToVisit: string[];  // 最佳访问时间
}
```

**显示效果**：
- `🟢 当前可参观`
- `🟡 游客较多`
- `🔴 闭馆时间 (明日09:00开放)`

## 🎨 显示模式

### 1. 紧凑模式 (compact)
- 适用于列表项、卡片预览
- 显示最重要的信息
- 单行显示，节省空间

### 2. 详细模式 (detailed)
- 适用于详情页面
- 显示完整的属性信息
- 包含标签和详细描述

### 3. 最小模式 (minimal)
- 适用于小尺寸组件
- 只显示最关键的2个属性
- 极简设计

## 🔄 迁移指南

### 从旧系统迁移

**旧代码**：
```typescript
// ❌ 旧的模糊标签系统
const getSubtleBudgetText = (activity) => {
  const cost = getUnifiedActivityCost(activity);
  if (cost === 0) return '免费体验';
  if (cost < 25) return '超值选择';
  if (cost < 60) return '合理消费';
  if (cost < 100) return '精选体验';
  return '优质享受';
};
```

**新代码**：
```typescript
// ✅ 新的真实属性系统
const manager = ActivityAttributeManager.getInstance();
const attributes = manager.generateRealAttributes(activity);

// 显示真实价格
const priceText = manager.formatAttributeDisplay(attributes, {
  mode: 'compact',
  showAttributes: [AttributeType.PRICE_RANGE]
})[AttributeType.PRICE_RANGE];
// 结果: "MYR30" 而不是 "精选体验"
```

### 样式更新

**旧样式**：
```typescript
subtleBudget: {
  fontSize: 11,
  color: '#8E8E93',
  fontWeight: '500',
  opacity: 0.8,
}
```

**新样式**：
```typescript
realBudget: {
  fontSize: 12,
  color: '#007AFF',
  fontWeight: '600',
  backgroundColor: '#F0F8FF',
  paddingHorizontal: 6,
  paddingVertical: 2,
  borderRadius: 4,
}
```

## 🧪 测试

### 运行测试

```bash
npm test ActivityAttributeManager.test.ts
```

### 测试覆盖

- ✅ 单例模式
- ✅ 属性生成
- ✅ 格式化显示
- ✅ 价格等级判断
- ✅ 营业时间判断
- ✅ 错误处理

## 📈 性能优化

### 1. 缓存机制
- 属性管理器使用单例模式
- 格式化结果可以缓存
- 避免重复计算

### 2. 懒加载
- 只在需要时生成属性
- 支持按需显示属性类型

### 3. 内存优化
- 使用useMemo缓存计算结果
- 避免不必要的重新渲染

## 🔮 未来扩展

### 1. 多语言支持
- 支持更多语言的属性显示
- 本地化的时间格式

### 2. 更多属性类型
- 无障碍设施信息
- 停车信息
- WiFi可用性

### 3. 智能推荐
- 基于用户偏好的属性权重
- 个性化的显示顺序

## 🐛 常见问题

### Q: 如何处理API数据缺失？
A: 系统会自动降级到模板数据或估算值，确保始终有可显示的属性。

### Q: 如何自定义显示格式？
A: 使用`formatAttributeDisplay`方法和`AttributeDisplayConfig`配置。

### Q: 如何添加新的属性类型？
A: 在`ActivityAttributes.ts`中添加新类型，在`ActivityAttributeManager.ts`中实现生成逻辑。

## 📞 支持

如有问题或建议，请联系开发团队或提交Issue。
