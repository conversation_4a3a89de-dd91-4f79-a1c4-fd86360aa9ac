/**
 * 🚀 Ultra Think 配置文件
 * Phase A: 基础设施准备 - 添加Ultra Think配置选项
 */

// ===== Ultra Think 核心配置 =====

export interface UltraThinkConfig {
  // 系统配置
  enabled: boolean;
  version: string;
  mode: 'development' | 'production' | 'testing';
  
  // 迁移配置
  migration: {
    enableGradualMigration: boolean;
    currentPhase: 'A' | 'B' | 'C' | 'D';
    fallbackTo6Agent: boolean;
    testMode: boolean;
  };
  
  // LLM管理配置
  llm: {
    primaryModel: string;
    fallbackModels: string[];
    maxRetries: number;
    timeoutMs: number;
    enablePerformanceMonitoring: boolean;
  };
  
  // API修复配置
  api: {
    enableEnhancedSerpAPI: boolean;
    enableRateLimiting: boolean;
    enableFallbackData: boolean;
    healthCheckIntervalMs: number;
  };
  
  // 预算计算配置
  budget: {
    enableSeasonalAdjustment: boolean;
    enableDestinationMultipliers: boolean;
    confidenceThreshold: number;
  };
  
  // UI优化配置
  ui: {
    enableSmartComponents: boolean;
    enableResponsiveDesign: boolean;
    enableAccessibility: boolean;
  };
  
  // 测试配置
  testing: {
    enableIntegrationTests: boolean;
    enablePerformanceTests: boolean;
    testCoverageThreshold: number;
  };
}

// ===== 默认配置 =====

export const DEFAULT_ULTRA_THINK_CONFIG: UltraThinkConfig = {
  // 系统配置
  enabled: true,
  version: '1.0.0',
  mode: 'development',
  
  // 迁移配置 - Phase D: 全面测试和优化
  migration: {
    enableGradualMigration: true,
    currentPhase: 'D',
    fallbackTo6Agent: false, // Phase D: 完全使用Ultra Think
    testMode: false          // 生产模式，完全启用Ultra Think
  },
  
  // LLM管理配置 - 🚀 强制使用Gemini 2.5 Flash Lite高性能模型
  llm: {
    primaryModel: 'google/gemini-2.0-flash-exp:free',
    fallbackModels: [
      'google/gemini-2.0-flash-exp:free',
      'google/gemini-flash-1.5-8b:free'
    ],
    maxRetries: 3,
    timeoutMs: 30000,
    enablePerformanceMonitoring: true
  },
  
  // API修复配置
  api: {
    enableEnhancedSerpAPI: true,
    enableRateLimiting: true,
    enableFallbackData: true,
    healthCheckIntervalMs: 60000  // 1分钟检查一次
  },
  
  // 预算计算配置
  budget: {
    enableSeasonalAdjustment: true,
    enableDestinationMultipliers: true,
    confidenceThreshold: 0.8
  },
  
  // UI优化配置
  ui: {
    enableSmartComponents: true,
    enableResponsiveDesign: true,
    enableAccessibility: true
  },
  
  // 测试配置
  testing: {
    enableIntegrationTests: true,
    enablePerformanceTests: true,
    testCoverageThreshold: 80
  }
};

// ===== 环境特定配置 =====

export const ULTRA_THINK_CONFIGS = {
  development: {
    ...DEFAULT_ULTRA_THINK_CONFIG,
    mode: 'development' as const,
    migration: {
      ...DEFAULT_ULTRA_THINK_CONFIG.migration,
      testMode: true,
      fallbackTo6Agent: true
    }
  },
  
  production: {
    ...DEFAULT_ULTRA_THINK_CONFIG,
    mode: 'production' as const,
    migration: {
      ...DEFAULT_ULTRA_THINK_CONFIG.migration,
      testMode: false,
      fallbackTo6Agent: false  // 生产环境可以完全使用Ultra Think
    },
    llm: {
      ...DEFAULT_ULTRA_THINK_CONFIG.llm,
      timeoutMs: 15000  // 生产环境更短的超时时间
    }
  },
  
  testing: {
    ...DEFAULT_ULTRA_THINK_CONFIG,
    mode: 'testing' as const,
    migration: {
      ...DEFAULT_ULTRA_THINK_CONFIG.migration,
      testMode: true,
      fallbackTo6Agent: false  // 测试环境专门测试Ultra Think
    },
    api: {
      ...DEFAULT_ULTRA_THINK_CONFIG.api,
      healthCheckIntervalMs: 10000  // 测试环境更频繁的检查
    }
  }
};

// ===== 配置管理器 =====

export class UltraThinkConfigManager {
  private static instance: UltraThinkConfigManager;
  private config: UltraThinkConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  static getInstance(): UltraThinkConfigManager {
    if (!UltraThinkConfigManager.instance) {
      UltraThinkConfigManager.instance = new UltraThinkConfigManager();
    }
    return UltraThinkConfigManager.instance;
  }

  private loadConfig(): UltraThinkConfig {
    const env = process.env.NODE_ENV || 'development';
    const envConfig = ULTRA_THINK_CONFIGS[env] || ULTRA_THINK_CONFIGS.development;
    
    // 从环境变量覆盖配置
    return {
      ...envConfig,
      enabled: process.env.ULTRA_THINK_ENABLED === 'true' || envConfig.enabled,
      migration: {
        ...envConfig.migration,
        currentPhase: (process.env.ULTRA_THINK_PHASE as any) || envConfig.migration.currentPhase
      }
    };
  }

  getConfig(): UltraThinkConfig {
    return this.config;
  }

  updateConfig(updates: Partial<UltraThinkConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  isEnabled(): boolean {
    return this.config.enabled;
  }

  getCurrentPhase(): 'A' | 'B' | 'C' | 'D' {
    return this.config.migration.currentPhase;
  }

  shouldFallbackTo6Agent(): boolean {
    return this.config.migration.fallbackTo6Agent;
  }

  isTestMode(): boolean {
    return this.config.migration.testMode;
  }
}

// ===== 导出配置实例 =====

export const ultraThinkConfig = UltraThinkConfigManager.getInstance();
