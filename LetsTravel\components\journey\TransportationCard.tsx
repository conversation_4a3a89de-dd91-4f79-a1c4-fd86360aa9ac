import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getTransportIcon } from '../../constants/TransportationIcons';

// 交通数据结构
export interface TransportationDetails {
  type: 'metro' | 'bus' | 'train' | 'taxi' | 'walk' | 'bike';
  route: RouteDetails;
  cost: number;
  duration: number; // minutes
  instructions: string[];
}

export interface RouteDetails {
  // 地铁信息
  metro?: {
    line: string;           // "JR山手线"
    lineColor: string;      // "#00B04F"
    lineNumber?: string;    // "JY"
    fromStation: StationInfo;
    toStation: StationInfo;
    transfers?: TransferInfo[];
    totalStops: number;
  };
  
  // 公交信息
  bus?: {
    routeNumber: string;    // "都02"
    routeName: string;      // "大门・赤羽橋～錦糸町駅前"
    operator: string;       // "都営バス"
    fromStop: BusStopInfo;
    toStop: BusStopInfo;
    frequency: string;      // "每5-10分钟一班"
  };
  
  // 火车信息
  train?: {
    line: string;           // "東海道新幹線"
    trainType: string;      // "のぞみ"
    trainNumber?: string;   // "のぞみ1号"
    fromStation: StationInfo;
    toStation: StationInfo;
    platform?: string;     // "16番線"
    carNumber?: string;     // "3号車"
    seatType?: string;      // "指定席"
  };
}

export interface StationInfo {
  name: string;           // "新宿駅"
  nameEn?: string;        // "Shinjuku Station"
  code?: string;          // "JY17"
  exits?: string[];       // ["東口", "南口", "西口"]
  facilities?: string[];  // ["エレベーター", "エスカレーター", "トイレ"]
}

export interface BusStopInfo {
  name: string;           // "新宿駅前"
  stopId?: string;        // "07-101"
  platform?: string;     // "1番のりば"
  nearbyLandmarks?: string[]; // ["新宿高島屋", "JR新宿駅東口"]
}

export interface TransferInfo {
  station: StationInfo;
  fromLine: string;
  toLIne: string;
  walkingTime: number;    // minutes
  instructions: string;   // "中央改札を出て東口方面へ"
}

interface TransportationCardProps {
  transportation: TransportationDetails;
  isExpanded?: boolean;
  onToggle?: () => void;
}

export default function TransportationCard({
  transportation,
  isExpanded = false,
  onToggle,
}: TransportationCardProps) {
  const [expanded, setExpanded] = useState(isExpanded);

  const handleToggle = () => {
    setExpanded(!expanded);
    onToggle?.();
  };

  // 使用统一的交通图标系统
  const getTransportIcon = (type: string): string => {
    return getTransportIcon(type);
  };

  const getRouteDisplayText = (transport: TransportationDetails): string => {
    if (transport.route.metro) {
      const metro = transport.route.metro;
      return `${metro.fromStation.name} → ${metro.toStation.name}`;
    }
    if (transport.route.bus) {
      const bus = transport.route.bus;
      return `${bus.fromStop.name} → ${bus.toStop.name}`;
    }
    if (transport.route.train) {
      const train = transport.route.train;
      return `${train.fromStation.name} → ${train.toStation.name}`;
    }
    return '交通路线';
  };

  const renderCompactView = () => (
    <View style={styles.compactTransport}>
      <View style={styles.transportIcon}>
        <Text style={styles.iconText}>
          {getTransportIcon(transportation.type)}
        </Text>
      </View>
      
      <View style={styles.routeInfo}>
        <Text style={styles.routeText}>
          {getRouteDisplayText(transportation)}
        </Text>
        <Text style={styles.costDuration}>
          💰 ¥{transportation.cost} • ⏱️ {transportation.duration}分钟
        </Text>
      </View>
      
      <TouchableOpacity onPress={handleToggle} style={styles.expandButton}>
        <Ionicons 
          name={expanded ? "chevron-up" : "chevron-down"} 
          size={20} 
          color="#666" 
        />
      </TouchableOpacity>
    </View>
  );

  const renderMetroDetails = (metro: RouteDetails['metro']) => {
    if (!metro) return null;
    
    return (
      <View style={styles.metroDetails}>
        <View style={styles.lineInfo}>
          <View style={[styles.lineIndicator, { backgroundColor: metro.lineColor }]} />
          <Text style={styles.lineName}>{metro.line}</Text>
          {metro.lineNumber && (
            <Text style={styles.lineNumber}>({metro.lineNumber})</Text>
          )}
        </View>
        
        <View style={styles.stationRoute}>
          <View style={styles.stationInfo}>
            <Text style={styles.stationName}>{metro.fromStation.name}</Text>
            {metro.fromStation.code && (
              <Text style={styles.stationCode}>{metro.fromStation.code}</Text>
            )}
            {metro.fromStation.exits && (
              <Text style={styles.exitInfo}>
                推荐出口: {metro.fromStation.exits.join(', ')}
              </Text>
            )}
          </View>
          
          <View style={styles.routeIndicator}>
            <Text style={styles.stopsCount}>{metro.totalStops} 站</Text>
            <View style={styles.routeLine} />
          </View>
          
          <View style={styles.stationInfo}>
            <Text style={styles.stationName}>{metro.toStation.name}</Text>
            {metro.toStation.code && (
              <Text style={styles.stationCode}>{metro.toStation.code}</Text>
            )}
            {metro.toStation.exits && (
              <Text style={styles.exitInfo}>
                推荐出口: {metro.toStation.exits.join(', ')}
              </Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  const renderExpandedView = () => (
    <View style={styles.expandedTransport}>
      {renderCompactView()}
      
      <View style={styles.detailedInfo}>
        {transportation.route.metro && renderMetroDetails(transportation.route.metro)}
        
        <View style={styles.instructions}>
          <Text style={styles.instructionsTitle}>详细路线:</Text>
          {transportation.instructions.map((instruction, index) => (
            <View key={index} style={styles.instructionStep}>
              <Text style={styles.stepNumber}>{index + 1}</Text>
              <Text style={styles.stepText}>{instruction}</Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );

  return expanded ? renderExpandedView() : renderCompactView();
}

const styles = StyleSheet.create({
  compactTransport: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginVertical: 4,
  },
  transportIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  iconText: {
    fontSize: 16,
    color: '#fff',
  },
  routeInfo: {
    flex: 1,
  },
  routeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  costDuration: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  expandButton: {
    padding: 8,
  },
  
  // 展开状态样式
  expandedTransport: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 8,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailedInfo: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  
  // 地铁样式
  metroDetails: {
    marginBottom: 16,
  },
  lineInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  lineIndicator: {
    width: 4,
    height: 20,
    borderRadius: 2,
    marginRight: 8,
  },
  lineName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  lineNumber: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  stationRoute: {
    marginVertical: 12,
  },
  stationInfo: {
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginVertical: 4,
  },
  stationName: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
  },
  stationCode: {
    fontSize: 12,
    color: '#667eea',
    marginTop: 2,
  },
  exitInfo: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  routeIndicator: {
    alignItems: 'center',
    marginVertical: 8,
  },
  stopsCount: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: '#ddd',
  },
  
  // 指引样式
  instructions: {
    marginTop: 16,
  },
  instructionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  instructionStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  stepNumber: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#667eea',
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 20,
    marginRight: 8,
  },
  stepText: {
    flex: 1,
    fontSize: 13,
    color: '#333',
    lineHeight: 18,
  },
});
