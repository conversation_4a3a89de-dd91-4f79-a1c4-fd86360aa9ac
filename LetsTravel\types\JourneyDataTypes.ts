/**
 * 🎯 Ultra Think Journey Data Types V2.0
 * 重构后的核心数据结构定义
 */

// 📊 JSON数据传递标准接口
export interface JSONDataTransfer {
  version: string;           // "2.0"
  timestamp: string;         // ISO 8601
  source: string;           // "master_solver" | "llm_enhancer" | "frontend"
  target: string;           // "frontend" | "storage" | "api"
  dataType: string;         // "journey" | "activity" | "budget"
  payload: JourneyPayload;  // 实际数据
  metadata: TransferMetadata;
}

export interface TransferMetadata {
  processingTime: number;
  qualityScore: number;
  validationStatus: "valid" | "warning" | "error";
  errors?: string[];
  warnings?: string[];
  llmEnhanced?: boolean;
  enhancementModel?: string;
  autoFixed?: boolean;
  fixesApplied?: number;
}

// 🎯 核心行程数据结构
export interface JourneyPayload {
  journey: Journey;
  dayPlans: DayPlan[];
  accommodation: Accommodation[];
  budget: BudgetBreakdown;
  metadata: JourneyMetadata;
}

export interface Journey {
  id: string;
  title: string;
  destination: string;
  duration: number;
  startDate: string;  // ISO date string
  endDate: string;    // ISO date string
  travelers: number;
  preferences: TravelPreferences;
}

export interface TravelPreferences {
  travelStyle: string[];
  accommodation: string[];
  transport: string[];
  interests: string[];
}

// 📅 每日计划结构
export interface DayPlan {
  dayNumber: number;
  date: string;  // ISO date string
  
  // 🎯 真正的活动（景点、体验、娱乐）
  activities: Activity[];
  
  // 🍽️ 餐饮计划（独立管理）
  meals: Meal[];
  
  // 🚗 交通安排（独立管理）
  transportation: Transportation[];
  
  // 📊 日程摘要
  summary: DaySummary;
  
  // 🎯 时间线
  timeline: TimelineItem[];
}

// 🎯 活动定义（真正的旅游活动）
export interface Activity {
  id: string;
  name: string;
  nameEn?: string;
  type: ActivityType;
  category: ActivityCategory;
  
  // ⏰ 时间信息
  timing: {
    startTime: string;    // "09:00"
    endTime: string;      // "11:00"
    duration: number;     // 分钟
    timeRange: string;    // "09:00-11:00"
  };
  
  // 📍 位置信息
  location: Location;
  
  // 💰 费用信息
  cost: Cost;
  
  // 📝 内容信息
  description: string;
  highlights: string[];
  tips: string[];
  
  // 🎨 展开内容
  expandedContent?: ExpandedContent;
  
  // 📊 元数据
  metadata: ActivityMetadata;
}

export type ActivityType = 'attraction' | 'experience' | 'entertainment' | 'shopping' | 'cultural';
export type ActivityCategory = 'sightseeing' | 'cultural' | 'nature' | 'entertainment' | 'shopping' | 'experience';

// 🍽️ 餐饮定义
export interface Meal {
  id: string;
  name: string;
  type: MealType;
  cuisine: string;
  
  // ⏰ 时间信息
  time: string;  // "12:00"
  duration: number;
  
  // 📍 位置信息
  location: Location;
  
  // 💰 费用信息
  cost: Cost;
  
  // 📝 详细信息
  description: string;
  specialties: string[];
  priceRange: string;
  
  // 🎨 餐饮特色
  details: {
    atmosphere: string;
    bestTime: string;
    reservationNeeded: boolean;
    paymentMethods: string[];
  };
}

export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'cafe';

// 🚗 交通定义
export interface Transportation {
  id: string;
  type: TransportType;
  
  // 🗺️ 路线信息
  from: Location;
  to: Location;
  
  // ⏰ 时间信息
  departureTime: string;
  arrivalTime: string;
  duration: number;
  
  // 💰 费用信息
  cost: Cost;
  
  // 📝 详细信息
  details: {
    route?: string;
    platform?: string;
    instructions: string[];
    walkingTime?: number;
  };
}

export type TransportType = 'walking' | 'taxi' | 'subway' | 'bus' | 'train' | 'flight';

// 🏨 住宿定义
export interface Accommodation {
  id: string;
  name: string;
  type: AccommodationType;
  
  // 📅 入住信息
  checkIn: string;  // ISO date string
  checkOut: string; // ISO date string
  nights: number;
  
  // 📍 位置和评级
  location: Location;
  rating: number;
  
  // 💰 费用
  costPerNight: Cost;
  totalCost: Cost;
  
  // 🏨 设施和服务
  amenities: string[];
  services: string[];
  
  // 📝 详细信息
  description: string;
  highlights: string[];
}

export type AccommodationType = 'hotel' | 'hostel' | 'airbnb' | 'resort' | 'ryokan';

// 📍 通用位置接口
export interface Location {
  name: string;
  address?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  district?: string;
  landmark?: string;
}

// 💰 通用费用接口
export interface Cost {
  amount: number;
  currency: string;
  breakdown?: Record<string, number>;
  priceLevel?: 'budget' | 'mid' | 'luxury';
}

// 🎨 展开内容接口
export interface ExpandedContent {
  whatToExpected?: string;
  howToGetThere?: string;
  nearbyAttractions?: string[];
  bestPhotoSpots?: string[];
  culturalTips?: string[];
  budgetTips?: string[];
  seasonalTips?: string[];
  insiderTips?: string[];
  weatherConsiderations?: string;
}

// 📊 活动元数据
export interface ActivityMetadata {
  source: string;
  qualityScore: number;
  isRealPlace: boolean;
  lastUpdated: string;
  llmEnhanced?: boolean;
  difficultyLevel?: 'easy' | 'moderate' | 'challenging';
  suitableFor?: string[];
}

// 📊 日程摘要
export interface DaySummary {
  totalActivities: number;
  totalCost: Cost;
  estimatedWalkingTime: number;
  highlights: string[];
  energyLevel: 'low' | 'moderate' | 'high';
  weatherAdvice?: string;
}

// ⏰ 时间线项目
export interface TimelineItem {
  time: string;
  type: 'activity' | 'meal' | 'transport' | 'break';
  title: string;
  duration: number;
  location?: string;
}

// 💰 预算分解
export interface BudgetBreakdown {
  total: Cost;
  breakdown: {
    accommodation: Cost;
    activities: Cost;
    food: Cost;
    transportation: Cost;
    shopping: Cost;
    miscellaneous: Cost;
  };
  dailyAverage: Cost;
  recommendations: string[];
}

// 📊 行程元数据
export interface JourneyMetadata {
  createdAt: string;
  updatedAt: string;
  version: string;
  generationSource: string;
  qualityScore: number;
  validationStatus: string;
  tags: string[];
}
