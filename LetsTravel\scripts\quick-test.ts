/**
 * 🚀 快速测试脚本
 * 验证时间线系统是否可以正常工作
 */

import { UltraThinkMasterSolverV2 } from '../services/ultra-think/UltraThinkMasterSolverV2';

async function quickTest() {
  console.log('🚀 快速测试开始');
  console.log('='.repeat(30));
  
  try {
    // 测试Master Solver V2.0
    const testRequest = {
      destination: '东京',
      duration: 3,
      budget: 8000,
      currency: 'MYR',
      travelers: 2,
      startDate: new Date('2025-12-15'),
      preferences: {
        travelStyle: ['cultural'],
        accommodation: ['mid_range'],
        transport: ['public'],
        interests: ['sightseeing']
      }
    };
    
    console.log('🎯 测试Master Solver V2.0...');
    const result = await UltraThinkMasterSolverV2.generateJourney(testRequest);
    
    if (result.success) {
      console.log('✅ Master Solver V2.0 测试通过');
      console.log(`📊 质量分: ${result.qualityScore}`);
      console.log(`⏱️ 执行时间: ${result.executionTime}ms`);
      
      // 检查时间线数据
      const hasTimeline = result.journeyData.metadata?.dayTimelines;
      console.log(`🕐 时间线数据: ${hasTimeline ? '✅ 已生成' : '❌ 未生成'}`);
      
      if (hasTimeline) {
        const dayTimelines = result.journeyData.metadata.dayTimelines;
        console.log(`📅 时间线天数: ${dayTimelines.length}天`);
        
        dayTimelines.forEach((day, index) => {
          console.log(`  Day ${index + 1}: ${day.timeline.length}个项目, ${day.totalBudget.displayText}`);
        });
      }
      
      console.log('\n🎉 快速测试通过！系统运行正常');
      return true;
      
    } else {
      console.log('❌ Master Solver V2.0 测试失败');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 快速测试失败:', error.message);
    return false;
  }
}

// 运行测试
if (require.main === module) {
  quickTest()
    .then(success => {
      console.log(`\n📋 测试结果: ${success ? '✅ 通过' : '❌ 失败'}`);
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

export { quickTest };
