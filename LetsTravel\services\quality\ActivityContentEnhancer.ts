/**
 * 🎨 活动内容质量提升器 - Ultra Think系统性修复
 * 提升活动描述质量，使用具体名称而非通用模板，添加丰富内容
 */

import { ultraThinkLLMManager } from '../ai/UltraThinkLLMManager';
import { ultraThinkAPIManager } from '../api/UltraThinkAPIManager';

export interface ContentEnhancementRequest {
  activity: any;
  destination: string;
  activityType: string;
  context?: {
    season?: string;
    budget?: number;
    travelStyle?: string[];
  };
}

export interface EnhancedContent {
  name: string;
  name_zh: string;
  description: string;
  description_zh: string;
  highlights: string[];
  tips: string[];
  rating?: number;
  qualityScore: number;
  isRealPlace: boolean;
  source: 'api' | 'llm' | 'enhanced_template';
}

export class ActivityContentEnhancer {
  
  /**
   * 🎯 提升活动内容质量
   */
  static async enhanceActivityContent(request: ContentEnhancementRequest): Promise<EnhancedContent> {
    console.log(`🎨 开始提升活动内容质量: ${request.activity.name}`);
    
    try {
      // 1. 尝试获取真实地点数据
      const realPlaceData = await this.fetchRealPlaceData(request);
      
      if (realPlaceData) {
        console.log(`✅ 找到真实地点数据: ${realPlaceData.name}`);
        return this.createEnhancedContentFromRealData(realPlaceData, request);
      }
      
      // 2. 使用LLM生成高质量内容
      const llmEnhancedContent = await this.generateLLMEnhancedContent(request);
      
      if (llmEnhancedContent) {
        console.log(`✅ LLM生成高质量内容`);
        return llmEnhancedContent;
      }
      
      // 3. 降级到增强模板
      console.log(`⚠️ 使用增强模板内容`);
      return this.createEnhancedTemplateContent(request);
      
    } catch (error) {
      console.error('❌ 内容质量提升失败:', error);
      return this.createFallbackContent(request);
    }
  }

  /**
   * 🔍 获取真实地点数据
   */
  private static async fetchRealPlaceData(request: ContentEnhancementRequest): Promise<any | null> {
    try {
      const searchQuery = this.buildSearchQuery(request);
      
      const apiResponse = await ultraThinkAPIManager.callAPI({
        type: 'places',
        params: {
          query: searchQuery,
          location: request.destination,
          type: request.activityType
        }
      });

      if (apiResponse.success && apiResponse.data.local_results?.length > 0) {
        // 选择最相关的结果
        const bestMatch = this.selectBestMatch(apiResponse.data.local_results, request);
        return bestMatch;
      }
      
      return null;
    } catch (error) {
      console.warn('获取真实地点数据失败:', error);
      return null;
    }
  }

  /**
   * 🔧 构建搜索查询
   */
  private static buildSearchQuery(request: ContentEnhancementRequest): string {
    const { destination, activityType } = request;
    
    const queryMap: Record<string, string> = {
      'restaurant': `${destination} 最佳餐厅 推荐 美食`,
      'attraction': `${destination} 热门景点 必去 旅游`,
      'shopping': `${destination} 购物中心 商场 特产`,
      'museum': `${destination} 博物馆 文化 历史`,
      'park': `${destination} 公园 自然 休闲`,
      'temple': `${destination} 寺庙 宗教 文化`,
      'market': `${destination} 市场 当地 特色`
    };
    
    return queryMap[activityType] || `${destination} ${activityType} 推荐`;
  }

  /**
   * 🎯 选择最佳匹配
   */
  private static selectBestMatch(results: any[], request: ContentEnhancementRequest): any {
    // 按评分和评论数排序
    return results
      .filter(place => place.rating && place.rating >= 3.5)
      .sort((a, b) => {
        const scoreA = (a.rating || 0) * Math.log(a.reviews || 1);
        const scoreB = (b.rating || 0) * Math.log(b.reviews || 1);
        return scoreB - scoreA;
      })[0];
  }

  /**
   * 🏗️ 从真实数据创建增强内容
   */
  private static createEnhancedContentFromRealData(placeData: any, request: ContentEnhancementRequest): EnhancedContent {
    const highlights = this.extractHighlightsFromPlace(placeData, request.activityType);
    const tips = this.generateContextualTips(placeData, request);
    
    return {
      name: placeData.title || placeData.name,
      name_zh: placeData.title || placeData.name,
      description: this.generateRichDescription(placeData, request),
      description_zh: this.generateRichDescription(placeData, request),
      highlights,
      tips,
      rating: placeData.rating,
      qualityScore: 0.95,
      isRealPlace: true,
      source: 'api'
    };
  }

  /**
   * 🤖 生成LLM增强内容
   */
  private static async generateLLMEnhancedContent(request: ContentEnhancementRequest): Promise<EnhancedContent | null> {
    try {
      const prompt = this.buildLLMPrompt(request);
      
      const llmResponse = await ultraThinkLLMManager.callLLM({
        prompt,
        taskType: 'content_enhancement',
        context: {
          destination: request.destination,
          activityType: request.activityType,
          complexity: 'high'
        }
      });

      if (llmResponse.success) {
        return this.parseLLMResponse(llmResponse.content, request);
      }
      
      return null;
    } catch (error) {
      console.warn('LLM内容生成失败:', error);
      return null;
    }
  }

  /**
   * 📝 构建LLM提示词
   */
  private static buildLLMPrompt(request: ContentEnhancementRequest): string {
    const { destination, activityType, context } = request;
    
    return `请为${destination}的${activityType}活动生成高质量内容，要求：

1. 提供一个具体的、真实存在的地点名称（不要使用"${destination}文化中心"这样的通用名称）
2. 生成详细的活动描述（50-80字）
3. 提供5个具体的亮点特色
4. 提供3个实用的旅行建议
5. 估算一个合理的评分（3.5-5.0）

请以JSON格式返回：
{
  "name": "具体地点名称",
  "description": "详细描述",
  "highlights": ["亮点1", "亮点2", "亮点3", "亮点4", "亮点5"],
  "tips": ["建议1", "建议2", "建议3"],
  "rating": 4.2
}

目的地：${destination}
活动类型：${activityType}
${context?.season ? `季节：${context.season}` : ''}
${context?.travelStyle ? `旅行风格：${context.travelStyle.join(', ')}` : ''}`;
  }

  /**
   * 🔧 解析LLM响应
   */
  private static parseLLMResponse(content: string, request: ContentEnhancementRequest): EnhancedContent {
    try {
      const parsed = JSON.parse(content);
      
      return {
        name: parsed.name || `${request.destination}精选体验`,
        name_zh: parsed.name || `${request.destination}精选体验`,
        description: parsed.description || '精彩的旅行体验',
        description_zh: parsed.description || '精彩的旅行体验',
        highlights: parsed.highlights || ['精彩体验', '难忘回忆', '文化探索'],
        tips: parsed.tips || ['提前预订', '注意开放时间', '穿舒适鞋子'],
        rating: parsed.rating || 4.0,
        qualityScore: 0.85,
        isRealPlace: false,
        source: 'llm'
      };
    } catch (error) {
      console.warn('LLM响应解析失败:', error);
      return this.createEnhancedTemplateContent(request);
    }
  }

  /**
   * 🎨 创建增强模板内容
   */
  private static createEnhancedTemplateContent(request: ContentEnhancementRequest): EnhancedContent {
    const templates = this.getEnhancedTemplates(request.destination, request.activityType);
    const template = templates[Math.floor(Math.random() * templates.length)];
    
    return {
      name: template.name,
      name_zh: template.name_zh,
      description: template.description,
      description_zh: template.description_zh,
      highlights: template.highlights,
      tips: template.tips,
      rating: template.rating,
      qualityScore: 0.75,
      isRealPlace: false,
      source: 'enhanced_template'
    };
  }

  /**
   * 📚 获取增强模板
   */
  private static getEnhancedTemplates(destination: string, activityType: string): any[] {
    const templates: Record<string, any[]> = {
      restaurant: [
        {
          name: `${destination}老字号餐厅`,
          name_zh: `${destination}老字号餐厅`,
          description: `品尝${destination}传统美食的百年老店，传承地道口味，深受当地人喜爱`,
          description_zh: `品尝${destination}传统美食的百年老店，传承地道口味，深受当地人喜爱`,
          highlights: ['百年传承工艺', '地道传统口味', '当地人推荐', '历史文化底蕴', '性价比极高'],
          tips: ['建议提前预订', '尝试招牌菜', '询问当日特色'],
          rating: 4.3
        },
        {
          name: `${destination}海鲜市场餐厅`,
          name_zh: `${destination}海鲜市场餐厅`,
          description: `新鲜海鲜直供的市场餐厅，现选现做，保证食材新鲜度和口感`,
          description_zh: `新鲜海鲜直供的市场餐厅，现选现做，保证食材新鲜度和口感`,
          highlights: ['新鲜海鲜直供', '现选现做', '价格透明', '本地渔民推荐', '海景用餐环境'],
          tips: ['早上海鲜最新鲜', '可以讨价还价', '注意食品安全'],
          rating: 4.1
        }
      ],
      attraction: [
        {
          name: `${destination}历史文化街区`,
          name_zh: `${destination}历史文化街区`,
          description: `保存完好的历史街区，融合传统建筑与现代文化，展现${destination}独特魅力`,
          description_zh: `保存完好的历史街区，融合传统建筑与现代文化，展现${destination}独特魅力`,
          highlights: ['传统建筑群', '文化艺术展示', '手工艺品店', '街头表演', '摄影胜地'],
          tips: ['避开周末人流', '穿舒适步行鞋', '带好相机'],
          rating: 4.4
        }
      ]
    };
    
    return templates[activityType] || templates.attraction;
  }

  /**
   * 🔧 生成丰富描述
   */
  private static generateRichDescription(placeData: any, request: ContentEnhancementRequest): string {
    const rating = placeData.rating ? `${placeData.rating}星评价` : '';
    const reviews = placeData.reviews ? `${placeData.reviews}条评论` : '';
    const type = this.getActivityTypeDescription(request.activityType);
    
    return `${placeData.title}是${request.destination}备受推荐的${type}，${rating}${reviews ? '，' + reviews : ''}。${placeData.description || '提供优质的服务体验，深受游客喜爱。'}`;
  }

  /**
   * 🏷️ 获取活动类型描述
   */
  private static getActivityTypeDescription(type: string): string {
    const descriptions: Record<string, string> = {
      'restaurant': '美食餐厅',
      'attraction': '旅游景点',
      'shopping': '购物场所',
      'museum': '文化博物馆',
      'park': '休闲公园',
      'temple': '宗教寺庙'
    };
    
    return descriptions[type] || '特色场所';
  }

  /**
   * ✨ 从地点提取亮点
   */
  private static extractHighlightsFromPlace(placeData: any, activityType: string): string[] {
    const highlights = [];
    
    if (placeData.rating >= 4.5) highlights.push('高评分推荐');
    if (placeData.reviews >= 100) highlights.push('游客好评如潮');
    if (placeData.price_level === '$') highlights.push('价格亲民');
    if (placeData.price_level === '$$$$') highlights.push('高端品质体验');
    
    // 根据类型添加特定亮点
    const typeHighlights: Record<string, string[]> = {
      'restaurant': ['地道美食', '新鲜食材', '特色菜品'],
      'attraction': ['历史文化', '绝佳景观', '拍照胜地'],
      'shopping': ['品牌齐全', '购物便利', '特色商品']
    };
    
    highlights.push(...(typeHighlights[activityType] || ['独特体验']));
    
    return highlights.slice(0, 5);
  }

  /**
   * 💡 生成情境化建议
   */
  private static generateContextualTips(placeData: any, request: ContentEnhancementRequest): string[] {
    const tips = [];
    
    // 基于评分的建议
    if (placeData.rating >= 4.5) {
      tips.push('热门地点，建议提前预订');
    }
    
    // 基于活动类型的建议
    const typeTips: Record<string, string[]> = {
      'restaurant': ['询问当日特色菜', '注意用餐高峰时间'],
      'attraction': ['避开节假日人流', '穿舒适的步行鞋'],
      'shopping': ['比较价格后购买', '保留购物收据']
    };
    
    tips.push(...(typeTips[request.activityType] || ['提前了解开放时间']));
    
    return tips.slice(0, 3);
  }

  /**
   * 🆘 创建降级内容
   */
  private static createFallbackContent(request: ContentEnhancementRequest): EnhancedContent {
    return {
      name: `${request.destination}精选体验`,
      name_zh: `${request.destination}精选体验`,
      description: `探索${request.destination}的独特魅力，享受难忘的旅行体验`,
      description_zh: `探索${request.destination}的独特魅力，享受难忘的旅行体验`,
      highlights: ['精彩体验', '文化探索', '难忘回忆'],
      tips: ['提前规划', '注意安全', '尊重当地文化'],
      rating: 4.0,
      qualityScore: 0.6,
      isRealPlace: false,
      source: 'enhanced_template'
    };
  }

  /**
   * 📊 批量提升活动内容质量
   */
  static async batchEnhanceActivities(activities: any[], destination: string): Promise<any[]> {
    console.log(`🎨 批量提升${activities.length}个活动的内容质量`);
    
    const enhancedActivities = [];
    
    for (const activity of activities) {
      try {
        const enhancedContent = await this.enhanceActivityContent({
          activity,
          destination,
          activityType: activity.type || activity.category || 'attraction'
        });
        
        // 合并增强内容到原活动
        const enhancedActivity = {
          ...activity,
          name: enhancedContent.name,
          name_zh: enhancedContent.name_zh,
          description: enhancedContent.description,
          description_zh: enhancedContent.description_zh,
          details: {
            ...activity.details,
            rating: enhancedContent.rating || activity.details?.rating,
            highlights: enhancedContent.highlights,
            tips: enhancedContent.tips
          },
          metadata: {
            ...activity.metadata,
            qualityScore: enhancedContent.qualityScore,
            isRealPlace: enhancedContent.isRealPlace,
            contentSource: enhancedContent.source,
            enhanced: true,
            enhancedAt: new Date().toISOString()
          }
        };
        
        enhancedActivities.push(enhancedActivity);
        
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        console.error(`活动内容提升失败: ${activity.name}`, error);
        enhancedActivities.push(activity);
      }
    }
    
    console.log(`✅ 批量内容提升完成，成功提升${enhancedActivities.filter(a => a.metadata?.enhanced).length}个活动`);
    
    return enhancedActivities;
  }
}
