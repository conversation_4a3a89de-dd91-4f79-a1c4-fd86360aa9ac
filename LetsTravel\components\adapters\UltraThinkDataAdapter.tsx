/**
 * 🔄 Ultra Think数据适配器组件
 * 自动处理Ultra Think数据格式转换，确保前端组件兼容性
 */

import React, { useMemo } from 'react';
import { DataTransformUtils } from '../../utils/DataTransformUtils';

interface UltraThinkDataAdapterProps {
  data: any;
  targetFormat: 'dayCard' | 'activity' | 'budget' | 'journey';
  children: (adaptedData: any) => React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
}

/**
 * 🔄 Ultra Think数据适配器
 * 自动检测和转换数据格式，确保组件兼容性
 */
export const UltraThinkDataAdapter: React.FC<UltraThinkDataAdapterProps> = ({
  data,
  targetFormat,
  children,
  fallback = null,
  onError
}) => {
  const adaptedData = useMemo(() => {
    try {
      if (!data) {
        console.warn('⚠️ UltraThinkDataAdapter: 输入数据为空');
        return null;
      }

      console.log(`🔄 开始数据适配: ${targetFormat}格式`);
      
      switch (targetFormat) {
        case 'dayCard':
          return DataTransformUtils.smartDataTransform(data, 'dayCard');
          
        case 'activity':
          return DataTransformUtils.smartDataTransform(data, 'activity');
          
        case 'budget':
          return DataTransformUtils.smartDataTransform(data, 'budget');
          
        case 'journey':
          if (DataTransformUtils['isUltraThinkFormat'](data)) {
            return DataTransformUtils.adaptUltraThinkResponse(data).journeyData;
          }
          return data;
          
        default:
          console.warn(`⚠️ 不支持的目标格式: ${targetFormat}`);
          return data;
      }
    } catch (error) {
      console.error('❌ 数据适配失败:', error);
      onError?.(error as Error);
      return null;
    }
  }, [data, targetFormat, onError]);

  if (adaptedData === null) {
    return <>{fallback}</>;
  }

  return <>{children(adaptedData)}</>;
};

/**
 * 🎯 DayCard数据适配器 - 专用于DayCard组件
 */
export const DayCardDataAdapter: React.FC<{
  ultraThinkData: any;
  day: number;
  currency?: string;
  children: (dayData: any) => React.ReactNode;
}> = ({ ultraThinkData, day, currency = 'MYR', children }) => {
  const dayData = useMemo(() => {
    if (!ultraThinkData) return null;
    
    try {
      if (Array.isArray(ultraThinkData)) {
        // 输入是活动数组
        return DataTransformUtils.transformActivitiesToDayData(
          ultraThinkData,
          day,
          undefined,
          currency
        );
      } else if (ultraThinkData.activities) {
        // 输入是完整响应
        return DataTransformUtils.transformActivitiesToDayData(
          ultraThinkData.activities,
          day,
          ultraThinkData.summary?.budget?.total,
          currency
        );
      }
      
      return null;
    } catch (error) {
      console.error('❌ DayCard数据适配失败:', error);
      return null;
    }
  }, [ultraThinkData, day, currency]);

  if (!dayData) {
    return null;
  }

  return <>{children(dayData)}</>;
};

/**
 * 💰 预算数据适配器 - 专用于预算组件
 */
export const BudgetDataAdapter: React.FC<{
  ultraThinkData: any;
  children: (budgetData: any) => React.ReactNode;
}> = ({ ultraThinkData, children }) => {
  const budgetData = useMemo(() => {
    if (!ultraThinkData) return null;
    
    try {
      let activities = [];
      
      if (Array.isArray(ultraThinkData)) {
        activities = ultraThinkData;
      } else if (ultraThinkData.activities) {
        activities = ultraThinkData.activities;
      } else {
        return null;
      }
      
      return DataTransformUtils.transformActivitiesToBudgetBreakdown(activities);
    } catch (error) {
      console.error('❌ 预算数据适配失败:', error);
      return null;
    }
  }, [ultraThinkData]);

  if (!budgetData) {
    return null;
  }

  return <>{children(budgetData)}</>;
};

/**
 * 🎯 活动数据适配器 - 专用于ActivityCard组件
 */
export const ActivityDataAdapter: React.FC<{
  ultraThinkData: any;
  children: (activities: any[]) => React.ReactNode;
}> = ({ ultraThinkData, children }) => {
  const activities = useMemo(() => {
    if (!ultraThinkData) return [];
    
    try {
      if (Array.isArray(ultraThinkData)) {
        return DataTransformUtils.transformUltraThinkActivitiesToActivityList(ultraThinkData);
      } else if (ultraThinkData.activities) {
        return DataTransformUtils.transformUltraThinkActivitiesToActivityList(ultraThinkData.activities);
      }
      
      return [];
    } catch (error) {
      console.error('❌ 活动数据适配失败:', error);
      return [];
    }
  }, [ultraThinkData]);

  return <>{children(activities)}</>;
};

/**
 * 🔄 智能数据适配Hook
 * 在组件中使用的Hook版本
 */
export const useUltraThinkDataAdapter = (data: any, targetFormat: string) => {
  return useMemo(() => {
    if (!data) return null;
    
    try {
      return DataTransformUtils.smartDataTransform(data, targetFormat as any);
    } catch (error) {
      console.error('❌ Hook数据适配失败:', error);
      return null;
    }
  }, [data, targetFormat]);
};

/**
 * 🎯 数据格式检测Hook
 */
export const useDataFormatDetection = (data: any) => {
  return useMemo(() => {
    if (!data) return 'unknown';
    
    // 检测Ultra Think格式
    if (DataTransformUtils['isUltraThinkFormat'](data)) {
      return 'ultra-think';
    }
    
    // 检测传统格式
    if (data.timeSlot || (Array.isArray(data) && data[0]?.timeSlot)) {
      return 'legacy';
    }
    
    // 检测Activity格式
    if (data.startTime || (Array.isArray(data) && data[0]?.startTime)) {
      return 'activity';
    }
    
    return 'unknown';
  }, [data]);
};

export default UltraThinkDataAdapter;
