/**
 * 🔧 数据完整性验证器
 * 确保活动数据在整个数据流中保持完整性，特别是day信息
 */

export interface StandardizedActivity {
  id: string;
  name: string;
  day: number;           // ✅ 强制包含day信息
  startTime: string;
  endTime: string;
  type: string;
  cost: number;
  location?: any;
  timing?: any;
  scheduledDay?: number;
  title?: string;
  activityName?: string;
}

export class DataIntegrityValidator {
  
  /**
   * 🔍 验证并修复活动的day信息
   */
  static validateActivityDay(activity: any): number {
    // 多重检查确保day信息不丢失
    if (activity.day && typeof activity.day === 'number' && activity.day >= 1 && activity.day <= 3) {
      return activity.day;
    }
    
    if (activity.timing?.day && typeof activity.timing.day === 'number' && activity.timing.day >= 1 && activity.timing.day <= 3) {
      return activity.timing.day;
    }
    
    if (activity.scheduledDay && typeof activity.scheduledDay === 'number' && activity.scheduledDay >= 1 && activity.scheduledDay <= 3) {
      return activity.scheduledDay;
    }
    
    // 智能推断：根据活动名称推断天数
    const inferredDay = this.inferDayFromActivity(activity);
    if (inferredDay > 0) {
      return inferredDay;
    }
    
    // 最后的默认值
    console.warn('⚠️ 无法确定活动天数，默认分配到第1天:', activity.name || activity.title);
    return 1;
  }
  
  /**
   * 🧠 智能推断活动天数
   */
  static inferDayFromActivity(activity: any): number {
    const name = (activity.name || activity.title || activity.activityName || '').toLowerCase();
    
    // 基于活动名称的智能推断
    if (name.includes('第1天') || name.includes('day 1') || name.includes('第一天')) return 1;
    if (name.includes('第2天') || name.includes('day 2') || name.includes('第二天')) return 2;
    if (name.includes('第3天') || name.includes('day 3') || name.includes('第三天')) return 3;
    
    // 基于时间推断（如果有完整的时间戳）
    if (activity.scheduledTime) {
      try {
        const date = new Date(activity.scheduledTime);
        const startDate = new Date('2025-12-15');
        const dayDiff = Math.floor((date.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));
        const calculatedDay = Math.max(1, Math.min(3, dayDiff + 1));
        if (calculatedDay >= 1 && calculatedDay <= 3) {
          return calculatedDay;
        }
      } catch (error) {
        console.warn('时间推断失败:', error);
      }
    }
    
    return 0; // 表示无法推断
  }
  
  /**
   * 📊 智能活动重新分配
   * 当所有活动都在Day 1时，智能重新分配到3天
   */
  static intelligentRedistribution(activities: any[]): { [day: number]: any[] } {
    console.log('🧠 启动智能活动重新分配，总活动数:', activities.length);
    
    const redistributed: { [day: number]: any[] } = { 1: [], 2: [], 3: [] };
    
    // 按活动类型和重要性分配
    const sortedActivities = this.sortActivitiesByPriority(activities);
    
    // 平均分配，但考虑活动类型
    const activitiesPerDay = Math.ceil(activities.length / 3);
    
    sortedActivities.forEach((activity, index) => {
      let targetDay = Math.floor(index / activitiesPerDay) + 1;
      targetDay = Math.min(targetDay, 3);
      
      redistributed[targetDay].push({
        ...activity,
        day: targetDay,
        redistributed: true
      });
    });
    
    console.log('✅ 智能重新分配完成:', 
      Object.entries(redistributed).map(([day, acts]) => `Day${day}: ${acts.length}个活动`));
    
    return redistributed;
  }
  
  /**
   * 🎯 按优先级排序活动
   */
  private static sortActivitiesByPriority(activities: any[]): any[] {
    return [...activities].sort((a, b) => {
      const priorityA = this.getActivityPriority(a);
      const priorityB = this.getActivityPriority(b);
      return priorityA - priorityB;
    });
  }
  
  /**
   * 📊 获取活动优先级 (数字越小优先级越高)
   */
  private static getActivityPriority(activity: any): number {
    const name = (activity.name || activity.title || '').toLowerCase();
    const type = activity.type || '';
    
    // 住宿优先级最高
    if (name.includes('酒店') || name.includes('hotel') || type === 'accommodation') {
      return 1;
    }
    
    // 主要景点
    if (name.includes('寺') || name.includes('神宫') || name.includes('塔') || type === 'attraction') {
      return 2;
    }
    
    // 餐饮
    if (name.includes('餐') || name.includes('食') || name.includes('寿司') || type === 'meal') {
      return 3;
    }
    
    // 交通
    if (name.includes('地铁') || name.includes('前往') || type === 'transport') {
      return 4;
    }
    
    // 其他
    return 5;
  }
  
  /**
   * ✅ 验证分组结果
   */
  static validateGrouping(grouped: { [day: number]: any[] }): boolean {
    const totalDays = Object.keys(grouped).length;
    const totalActivities = Object.values(grouped).flat().length;
    
    console.log(`📊 分组验证: ${totalDays}天，共${totalActivities}个活动`);
    
    // 检查是否有合理的分布
    const hasReasonableDistribution = totalDays >= 2 && totalDays <= 3;
    const noEmptyDays = Object.values(grouped).every(dayActivities => dayActivities.length > 0);
    
    Object.entries(grouped).forEach(([day, dayActivities]) => {
      console.log(`  Day ${day}: ${dayActivities.length}个活动`);
    });
    
    if (!hasReasonableDistribution) {
      console.warn('⚠️ 活动分布不合理，可能需要重新分配');
      return false;
    }
    
    return true;
  }
  
  /**
   * 🔧 标准化活动数据
   */
  static standardizeActivity(activity: any): StandardizedActivity {
    return {
      id: activity.id || `activity_${Date.now()}_${Math.random()}`,
      name: activity.name || activity.title || activity.activityName || '未命名活动',
      day: this.validateActivityDay(activity),
      startTime: activity.startTime || activity.timing?.startTime || '09:00',
      endTime: activity.endTime || activity.timing?.endTime || '10:00',
      type: this.detectActivityType(activity),
      cost: this.normalizeCost(activity.cost),
      location: activity.location,
      timing: activity.timing,
      scheduledDay: activity.scheduledDay,
      title: activity.title,
      activityName: activity.activityName
    };
  }
  
  /**
   * 🏷️ 检测活动类型
   */
  private static detectActivityType(activity: any): string {
    const name = (activity.name || activity.title || '').toLowerCase();
    
    if (name.includes('酒店') || name.includes('hotel')) return 'accommodation';
    if (name.includes('餐') || name.includes('食') || name.includes('寿司')) return 'meal';
    if (name.includes('地铁') || name.includes('前往') || name.includes('taxi')) return 'transport';
    if (name.includes('购物') || name.includes('商店')) return 'shopping';
    
    return 'attraction'; // 默认为景点
  }
  
  /**
   * 💰 标准化费用
   */
  private static normalizeCost(cost: any): number {
    if (typeof cost === 'number') {
      return Math.max(0, cost);
    }
    
    if (cost && typeof cost === 'object' && cost.amount !== undefined) {
      return Math.max(0, cost.amount);
    }
    
    return 0;
  }
}
