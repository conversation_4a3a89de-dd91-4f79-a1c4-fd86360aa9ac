/**
 * 🗾 东京真实地点数据库
 * 高质量的真实景点、餐厅、交通数据
 */

import { Activity, Meal, Transportation, Location, Cost } from '../types/JourneyDataTypes';

export interface RealPlace {
  id: string;
  name: string;
  nameEn: string;
  type: string;
  district: string;
  coordinates: { lat: number; lng: number };
  description: string;
  highlights: string[];
  bestTime: string;
  duration: number;
  cost: Cost;
  winterSpecial: string;
  culturalTips: string;
  photoSpots: string[];
  nearbyPlaces?: string[];
}

export interface RealRestaurant {
  id: string;
  name: string;
  nameEn: string;
  type: string;
  cuisine: string;
  district: string;
  coordinates: { lat: number; lng: number };
  description: string;
  specialties: string[];
  priceRange: string;
  bestTime: string;
  winterSpecial: string;
  culturalTips: string;
  mustTry: string[];
  atmosphere: string;
}

// 🏛️ 东京真实景点数据
export const TOKYO_ATTRACTIONS: RealPlace[] = [
  {
    id: "senso_ji_temple",
    name: "浅草寺",
    nameEn: "Senso-ji Temple",
    type: "temple",
    district: "浅草",
    coordinates: { lat: 35.7148, lng: 139.7967 },
    description: "东京最古老的寺庙，拥有1400年历史，是体验传统日本文化的绝佳地点",
    highlights: ["雷门", "仲见世通", "五重塔", "观音堂", "传统商店街"],
    bestTime: "早上8:00-10:00避开人群，体验宁静的晨间祈祷",
    duration: 120,
    cost: { amount: 0, currency: "JPY", priceLevel: "budget" },
    winterSpecial: "12月有特别的新年准备活动，可以看到传统的年末大扫除和装饰",
    culturalTips: "进入正殿前需要在手水舍洗手漱口，投币祈福时轻轻鞠躬",
    photoSpots: ["雷门正面经典角度", "五重塔远景", "仲见世通人流", "夜晚灯笼"],
    nearbyPlaces: ["东京晴空塔", "隅田川", "上野公园"]
  },
  {
    id: "tokyo_tower",
    name: "东京塔",
    nameEn: "Tokyo Tower",
    type: "landmark",
    district: "港区",
    coordinates: { lat: 35.6586, lng: 139.7454 },
    description: "333米高的红白色铁塔，东京的经典地标，可俯瞰整个东京都市景观",
    highlights: ["主展望台", "特别展望台", "夜景", "富士山远眺", "塔底商店"],
    bestTime: "傍晚17:00-19:00观赏日落和夜景最佳",
    duration: 90,
    cost: { amount: 1200, currency: "JPY", priceLevel: "mid" },
    winterSpecial: "冬季晴天可远眺富士山雪景，12月有特别的圣诞灯饰",
    culturalTips: "建议提前网上购票避免排队，展望台有中文导览",
    photoSpots: ["塔底仰拍全景", "展望台夜景", "与富士山合影", "日落时分"],
    nearbyPlaces: ["六本木", "赤坂", "芝公园"]
  },
  {
    id: "meiji_shrine",
    name: "明治神宫",
    nameEn: "Meiji Shrine",
    type: "shrine",
    district: "涩谷",
    coordinates: { lat: 35.6763, lng: 139.6993 },
    description: "供奉明治天皇的神社，被100万棵树木环绕，是东京市中心的绿色净土",
    highlights: ["本殿", "清正井", "宝物殿", "御苑", "传统婚礼"],
    bestTime: "早上9:00-11:00最为宁静，可能遇到传统婚礼仪式",
    duration: 90,
    cost: { amount: 0, currency: "JPY", priceLevel: "budget" },
    winterSpecial: "新年期间是日本最重要的初诣地点，体验传统新年参拜",
    culturalTips: "通过鸟居时需要鞠躬，参拜时先投币、鞠躬、拍手、祈愿、鞠躬",
    photoSpots: ["大鸟居", "参道林荫", "本殿建筑", "传统婚礼"],
    nearbyPlaces: ["原宿", "表参道", "代代木公园"]
  },
  {
    id: "tsukiji_outer_market",
    name: "筑地外市场",
    nameEn: "Tsukiji Outer Market",
    type: "market",
    district: "中央区",
    coordinates: { lat: 35.6654, lng: 139.7707 },
    description: "虽然内市场已搬迁，但外市场依然保持着传统的海鲜和美食文化",
    highlights: ["新鲜海鲜", "传统小吃", "厨具店", "茶叶店", "刀具店"],
    bestTime: "早上6:00-10:00最新鲜，避开中午人潮",
    duration: 120,
    cost: { amount: 2000, currency: "JPY", priceLevel: "mid" },
    winterSpecial: "冬季有特别的海鲜品种，如冬季限定的螃蟹和牡蛎",
    culturalTips: "现金支付为主，排队是常态，尊重商家的拍照规定",
    photoSpots: ["繁忙的市场场景", "新鲜海鲜", "传统店铺", "美食制作过程"],
    nearbyPlaces: ["银座", "汐留", "滨离宫恩赐庭园"]
  },
  {
    id: "shibuya_crossing",
    name: "涩谷十字路口",
    nameEn: "Shibuya Crossing",
    type: "landmark",
    district: "涩谷",
    coordinates: { lat: 35.6598, lng: 139.7006 },
    description: "世界最繁忙的十字路口，每次绿灯可有3000人同时过马路",
    highlights: ["人流奇观", "八公像", "涩谷天空", "周边购物", "夜景"],
    bestTime: "傍晚18:00-20:00人流最密集，夜景最美",
    duration: 60,
    cost: { amount: 0, currency: "JPY", priceLevel: "budget" },
    winterSpecial: "12月有圣诞装饰和灯饰，营造浪漫的冬日氛围",
    culturalTips: "体验日本人的秩序感，即使人多也很有序",
    photoSpots: ["星巴克二楼俯拍", "十字路口中央", "八公像", "夜晚霓虹"],
    nearbyPlaces: ["原宿", "表参道", "代官山"]
  },
  {
    id: "ueno_park",
    name: "上野公园",
    nameEn: "Ueno Park",
    type: "park",
    district: "台东区",
    coordinates: { lat: 35.7155, lng: 139.7740 },
    description: "东京最大的公园之一，集合了多个博物馆和动物园",
    highlights: ["上野动物园", "东京国立博物馆", "国立科学博物馆", "不忍池", "樱花"],
    bestTime: "上午10:00-15:00，可以安排半天时间深度游览",
    duration: 180,
    cost: { amount: 600, currency: "JPY", priceLevel: "budget" },
    winterSpecial: "冬季虽无樱花，但有美丽的梅花和冬季鸟类观察",
    culturalTips: "博物馆通常周一闭馆，建议提前查看开放时间",
    photoSpots: ["不忍池", "博物馆建筑", "动物园熊猫", "公园景色"],
    nearbyPlaces: ["浅草", "秋叶原", "御徒町"]
  }
];

// 🍽️ 东京真实餐厅数据
export const TOKYO_RESTAURANTS: RealRestaurant[] = [
  {
    id: "daiwa_sushi",
    name: "大和寿司",
    nameEn: "Daiwa Sushi",
    type: "sushi_bar",
    cuisine: "寿司",
    district: "筑地",
    coordinates: { lat: 35.6654, lng: 139.7707 },
    description: "筑地市场最著名的寿司店之一，以新鲜的金枪鱼寿司闻名",
    specialties: ["金枪鱼寿司", "海胆", "鲑鱼子", "季节限定寿司"],
    priceRange: "¥3000-5000",
    bestTime: "早上5:00开始排队，6:00开门营业",
    winterSpecial: "冬季有特别的冬季鱼类，如鰤鱼和比目鱼",
    culturalTips: "需要早起排队，现金支付，用餐时间约30分钟",
    mustTry: ["金枪鱼三部位", "师傅推荐套餐"],
    atmosphere: "传统寿司吧台，可以观看师傅制作过程"
  },
  {
    id: "ichiran_ramen",
    name: "一兰拉面",
    nameEn: "Ichiran Ramen",
    type: "ramen_shop",
    cuisine: "拉面",
    district: "涩谷",
    coordinates: { lat: 35.6598, lng: 139.7006 },
    description: "著名的豚骨拉面连锁店，以个人隔间式用餐体验著称",
    specialties: ["豚骨拉面", "替玉", "煎饺", "特制叉烧"],
    priceRange: "¥800-1500",
    bestTime: "任何时间，24小时营业",
    winterSpecial: "冬季的热腾腾拉面是最佳的暖身选择",
    culturalTips: "使用自动售票机点餐，可以自定义面条硬度和汤的浓度",
    mustTry: ["经典豚骨拉面", "半熟蛋"],
    atmosphere: "独特的个人隔间，专注享受拉面"
  },
  {
    id: "sukiyabashi_jiro",
    name: "数寄屋桥次郎",
    nameEn: "Sukiyabashi Jiro",
    type: "high_end_sushi",
    cuisine: "高级寿司",
    district: "银座",
    coordinates: { lat: 35.6762, lng: 139.7653 },
    description: "世界闻名的寿司大师小野二郎的店铺，米其林三星餐厅",
    specialties: ["江户前寿司", "季节性海鲜", "传统技法", "完美米饭"],
    priceRange: "¥30000-50000",
    bestTime: "需要提前数月预约，午餐相对容易预约",
    winterSpecial: "冬季有最肥美的金枪鱼和特别的冬季海鲜",
    culturalTips: "严格的用餐礼仪，不允许拍照，现金支付",
    mustTry: ["师傅精选套餐"],
    atmosphere: "严肃而专业的寿司体验，世界顶级水准"
  },
  {
    id: "gonpachi_shibuya",
    name: "权八涩谷店",
    nameEn: "Gonpachi Shibuya",
    type: "izakaya",
    cuisine: "居酒屋",
    district: "涩谷",
    coordinates: { lat: 35.6598, lng: 139.7006 },
    description: "以《杀死比尔》电影场景闻名的传统居酒屋，古色古香的装修",
    specialties: ["烤鸡肉串", "天妇罗", "刺身", "日本酒", "季节料理"],
    priceRange: "¥3000-6000",
    bestTime: "晚上18:00-22:00，建议预约",
    winterSpecial: "冬季有热腾腾的火锅和温酒",
    culturalTips: "体验日本居酒屋文化，可以大声聊天，氛围轻松",
    mustTry: ["招牌烤鸡串", "季节限定料理"],
    atmosphere: "传统日式装修，木质结构，电影拍摄地"
  },
  {
    id: "tempura_daikokuya",
    name: "天妇罗大黑屋",
    nameEn: "Tempura Daikokuya",
    type: "tempura_restaurant",
    cuisine: "天妇罗",
    district: "浅草",
    coordinates: { lat: 35.7148, lng: 139.7967 },
    description: "创业于1887年的老字号天妇罗店，保持传统制作工艺",
    specialties: ["江户前天妇罗", "虾天妇罗", "蔬菜天妇罗", "天丼"],
    priceRange: "¥2000-4000",
    bestTime: "午餐时间11:30-14:00，晚餐17:00-20:00",
    winterSpecial: "冬季蔬菜天妇罗特别美味，如莲藕和南瓜",
    culturalTips: "传统的榻榻米座位，需要脱鞋",
    mustTry: ["特选天妇罗套餐", "虾天妇罗"],
    atmosphere: "传统日式装修，历史悠久，家族经营"
  }
];

// 🚗 东京交通数据
export const TOKYO_TRANSPORTATION = {
  subway: {
    lines: ["JR山手线", "东京地铁银座线", "都营浅草线", "JR中央线"],
    averageCost: { amount: 200, currency: "JPY" },
    averageTime: 15
  },
  taxi: {
    baseFare: { amount: 420, currency: "JPY" },
    perKm: { amount: 80, currency: "JPY" },
    averageTime: 20
  },
  walking: {
    averageSpeed: 4, // km/h
    cost: { amount: 0, currency: "JPY" }
  }
};

// 🏨 东京住宿数据
export const TOKYO_ACCOMMODATIONS = [
  {
    id: "park_hyatt_tokyo",
    name: "东京柏悦酒店",
    nameEn: "Park Hyatt Tokyo",
    type: "luxury_hotel",
    district: "新宿",
    coordinates: { lat: 35.6938, lng: 139.7034 },
    description: "位于新宿公园塔52层的豪华酒店，《迷失东京》拍摄地",
    priceRange: "¥80000-150000/晚",
    amenities: ["无边泳池", "spa", "米其林餐厅", "健身房", "礼宾服务"],
    winterSpecial: "冬季可从房间俯瞰雪景中的东京全景"
  },
  {
    id: "capsule_hotel_shimbashi",
    name: "新桥胶囊酒店",
    nameEn: "Capsule Hotel Shimbashi",
    type: "capsule_hotel",
    district: "新桥",
    coordinates: { lat: 35.6657, lng: 139.7581 },
    description: "体验日本独特的胶囊酒店文化，经济实惠",
    priceRange: "¥3000-5000/晚",
    amenities: ["公共浴室", "储物柜", "免费WiFi", "自动售货机"],
    winterSpecial: "冬季的公共浴室特别舒适温暖"
  }
];

/**
 * 🔍 根据类型获取真实地点数据
 */
export class TokyoPlacesDatabase {
  
  static getAttractionsByType(type: string): RealPlace[] {
    return TOKYO_ATTRACTIONS.filter(place => place.type === type);
  }
  
  static getRestaurantsByCuisine(cuisine: string): RealRestaurant[] {
    return TOKYO_RESTAURANTS.filter(restaurant => restaurant.cuisine === cuisine);
  }
  
  static getPlacesByDistrict(district: string): RealPlace[] {
    return TOKYO_ATTRACTIONS.filter(place => place.district === district);
  }
  
  static getRandomAttractions(count: number): RealPlace[] {
    const shuffled = [...TOKYO_ATTRACTIONS].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }
  
  static getRandomRestaurants(count: number): RealRestaurant[] {
    const shuffled = [...TOKYO_RESTAURANTS].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }
  
  static calculateDistance(place1: { coordinates: { lat: number; lng: number } }, place2: { coordinates: { lat: number; lng: number } }): number {
    // 简化的距离计算（实际应用中可以使用更精确的算法）
    const latDiff = place1.coordinates.lat - place2.coordinates.lat;
    const lngDiff = place1.coordinates.lng - place2.coordinates.lng;
    return Math.sqrt(latDiff * latDiff + lngDiff * lngDiff) * 111; // 大约转换为公里
  }
  
  static findNearbyPlaces(centerPlace: RealPlace, maxDistance: number = 2): RealPlace[] {
    return TOKYO_ATTRACTIONS.filter(place => {
      if (place.id === centerPlace.id) return false;
      const distance = this.calculateDistance(centerPlace, place);
      return distance <= maxDistance;
    });
  }
}
