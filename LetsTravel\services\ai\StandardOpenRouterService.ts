/**
 * 🔗 OpenRouter API标准化服务
 * 基于OpenRouter官方API文档v1.0实现的标准化服务
 * 支持完整的错误处理、降级策略和性能监控
 */

import axios, { AxiosError } from 'axios';
import { AGENT_MODEL_CONFIG } from '../../config/AIModelConfig';

// 基于OpenRouter API文档的标准接口定义
interface OpenRouterMessage {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string | Array<{ type: 'text'; text: string }>;
  name?: string;
  tool_call_id?: string;
}

interface OpenRouterRequest {
  model: string;
  messages: OpenRouterMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
  stream?: boolean;
  user?: string;
}

interface OpenRouterChoice {
  finish_reason: string | null;
  native_finish_reason: string | null;
  message: {
    role: string;
    content: string | null;
  };
  error?: {
    code: number;
    message: string;
    metadata?: Record<string, unknown>;
  };
}

interface OpenRouterResponse {
  id: string;
  choices: OpenRouterChoice[];
  created: number;
  model: string;
  object: 'chat.completion' | 'chat.completion.chunk';
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  error?: {
    code: number;
    message: string;
    metadata?: Record<string, unknown>;
  };
}

// 兼容性接口
interface LegacyOpenRouterResponse {
  content: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class StandardOpenRouterService {
  private readonly apiKey: string;
  private readonly baseUrl: string = 'https://openrouter.ai/api/v1/chat/completions';
  private readonly timeout: number = 30000; // 30秒超时
  private readonly maxRetries: number = 3;
  
  // 标准化Headers - 基于OpenRouter API文档
  private readonly standardHeaders: Record<string, string>;

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY || process.env.EXPO_PUBLIC_OPENROUTER_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('⚠️ OpenRouter API密钥未配置');
    }

    // 基于API文档的标准Headers
    this.standardHeaders = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://trekmate.app', // 用于OpenRouter排名
      'X-Title': 'Trekmate AI Assistant', // 应用标识
    };
  }

  /**
   * 🎯 标准化API调用方法 - 基于OpenRouter API文档
   */
  private async callOpenRouterAPI(request: OpenRouterRequest): Promise<OpenRouterResponse> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API密钥未配置');
    }

    let lastError: Error;
    
    // 重试机制
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`🔗 OpenRouter API调用 (尝试 ${attempt}/${this.maxRetries}): ${request.model}`);
        
        const response = await axios.post<OpenRouterResponse>(
          this.baseUrl,
          request,
          {
            headers: this.standardHeaders,
            timeout: this.timeout,
            validateStatus: (status) => status < 500, // 只有5xx才重试
          }
        );

        // 检查API级别的错误
        if (response.data.error) {
          throw new Error(`OpenRouter API错误: ${response.data.error.message} (代码: ${response.data.error.code})`);
        }

        // 检查选择中的错误
        if (response.data.choices?.[0]?.error) {
          const choiceError = response.data.choices[0].error;
          throw new Error(`模型错误: ${choiceError.message} (代码: ${choiceError.code})`);
        }

        console.log(`✅ OpenRouter API调用成功: ${response.data.model}`);
        return response.data;

      } catch (error) {
        lastError = error as Error;
        
        if (axios.isAxiosError(error)) {
          const status = error.response?.status;
          const errorData = error.response?.data;
          
          console.warn(`⚠️ OpenRouter API调用失败 (尝试 ${attempt}):`, {
            status,
            message: errorData?.error?.message || error.message,
            model: request.model
          });

          // 4xx错误不重试
          if (status && status >= 400 && status < 500) {
            break;
          }
        } else {
          console.warn(`⚠️ 网络错误 (尝试 ${attempt}):`, error.message);
        }

        // 最后一次尝试失败，不等待
        if (attempt < this.maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // 指数退避，最大5秒
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * 🎯 生成响应 - 标准化方法
   */
  async generateResponse(
    prompt: string,
    modelName?: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      fallbackModel?: string;
    }
  ): Promise<string> {
    const messages: OpenRouterMessage[] = [];
    
    // 添加系统提示
    if (options?.systemPrompt) {
      messages.push({
        role: 'system',
        content: options.systemPrompt
      });
    }
    
    // 添加用户消息
    messages.push({
      role: 'user',
      content: prompt
    });

    const request: OpenRouterRequest = {
      model: modelName || this.getDefaultModel(),
      messages,
      max_tokens: options?.maxTokens || 2000,
      temperature: options?.temperature || 0.7,
      user: 'letstravel-user' // 用户标识
    };

    // 首先尝试主要模型
    try {
      const response = await this.callOpenRouterAPI(request);
      const content = response.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('API返回空内容');
      }
      
      return content;
    } catch (error) {
      console.warn(`⚠️ 主要模型 ${modelName} 调用失败:`, error);
      
      // 尝试备用模型
      if (options?.fallbackModel && options.fallbackModel !== modelName) {
        try {
          console.log(`🔄 尝试备用模型: ${options.fallbackModel}`);
          request.model = options.fallbackModel;
          const response = await this.callOpenRouterAPI(request);
          const content = response.choices[0]?.message?.content;
          
          if (content) {
            return content;
          }
        } catch (fallbackError) {
          console.warn(`⚠️ 备用模型 ${options.fallbackModel} 也失败:`, fallbackError);
        }
      }
      
      // 最后尝试默认模型
      const defaultModel = this.getDefaultModel();
      if (defaultModel !== modelName && defaultModel !== options?.fallbackModel) {
        try {
          console.log(`🔄 尝试默认模型: ${defaultModel}`);
          request.model = defaultModel;
          const response = await this.callOpenRouterAPI(request);
          const content = response.choices[0]?.message?.content;
          
          if (content) {
            return content;
          }
        } catch (defaultError) {
          console.error(`❌ 默认模型也失败:`, defaultError);
        }
      }
      
      // 所有模型都失败，返回降级响应
      console.error('❌ 所有模型调用失败，返回降级响应');
      return this.getFallbackResponse(prompt);
    }
  }

  /**
   * 🎯 生成内容 - 返回完整响应对象
   */
  async generateContent(
    prompt: string,
    modelName?: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
    }
  ): Promise<LegacyOpenRouterResponse> {
    const messages: OpenRouterMessage[] = [];
    
    if (options?.systemPrompt) {
      messages.push({
        role: 'system',
        content: options.systemPrompt
      });
    }
    
    messages.push({
      role: 'user',
      content: prompt
    });

    const request: OpenRouterRequest = {
      model: modelName || this.getDefaultModel(),
      messages,
      max_tokens: options?.maxTokens || 2000,
      temperature: options?.temperature || 0.7,
      user: 'letstravel-user'
    };

    try {
      const response = await this.callOpenRouterAPI(request);
      
      return {
        content: response.choices[0]?.message?.content || '',
        model: response.model,
        usage: response.usage
      };
    } catch (error) {
      console.error('❌ OpenRouter服务调用失败:', error);
      throw error;
    }
  }

  /**
   * 获取默认模型
   */
  private getDefaultModel(): string {
    // 优先使用环境变量配置的Agent模型
    return process.env.OPENROUTER_AGENT_MODEL || AGENT_MODEL_CONFIG.fallback;
  }

  /**
   * 🎯 获取Planner专用模型
   */
  getPlannerModel(): string {
    return process.env.OPENROUTER_PLANNER_MODEL || 'meta-llama/llama-3.3-70b-instruct';
  }

  /**
   * 🎯 获取Agent专用模型
   */
  getAgentModel(): string {
    return process.env.OPENROUTER_AGENT_MODEL || 'google/gemma-3-12b-it';
  }

  /**
   * 🛡️ 获取智能降级响应
   */
  private getFallbackResponse(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase();

    // 马来西亚旅游相关
    if (lowerPrompt.includes('马来西亚') || lowerPrompt.includes('malaysia') || lowerPrompt.includes('吉隆坡') || lowerPrompt.includes('kuala lumpur')) {
      return this.getMalaysiaFallbackResponse();
    }

    // 航班相关
    if (lowerPrompt.includes('航班') || lowerPrompt.includes('机票') || lowerPrompt.includes('flight')) {
      return this.getFlightFallbackResponse();
    }

    // 酒店相关
    if (lowerPrompt.includes('酒店') || lowerPrompt.includes('住宿') || lowerPrompt.includes('hotel')) {
      return this.getHotelFallbackResponse();
    }

    // 景点相关
    if (lowerPrompt.includes('景点') || lowerPrompt.includes('旅游') || lowerPrompt.includes('attraction') || lowerPrompt.includes('tour')) {
      return this.getAttractionFallbackResponse();
    }

    // 天气相关
    if (lowerPrompt.includes('天气') || lowerPrompt.includes('weather')) {
      return this.getWeatherFallbackResponse();
    }

    // 通用降级响应
    return this.getGenericFallbackResponse(prompt);
  }

  private getMalaysiaFallbackResponse(): string {
    return `🇲🇾 马来西亚旅游推荐：

📍 热门目的地：
• 吉隆坡 - 双子塔、茨厂街、独立广场
• 马六甲 - 历史古城、荷兰红屋
• 槟城 - 乔治市、美食天堂
• 兰卡威 - 海滩度假、缆车

💰 预算参考：
• 经济型：200-300 MYR/天
• 舒适型：400-600 MYR/天
• 豪华型：800+ MYR/天

🎯 最佳时间：5-9月（避开雨季）

注：AI服务暂时不可用，以上为预设推荐。`;
  }

  private getFlightFallbackResponse(): string {
    return `✈️ 航班预订建议：

🔍 推荐平台：
• 马来西亚航空官网
• 亚洲航空 (AirAsia)
• 携程、去哪儿网
• Skyscanner 比价

💡 预订技巧：
• 提前30-60天预订
• 避开节假日高峰
• 考虑中转航班节省费用
• 关注航空公司促销

📅 最佳预订时间：周二、周三通常更便宜

注：AI服务暂时不可用，建议直接访问航空公司官网。`;
  }

  private getHotelFallbackResponse(): string {
    return `🏨 住宿预订建议：

🌟 推荐区域：
• 吉隆坡：KLCC、武吉免登
• 马六甲：古城区、河畔
• 槟城：乔治市、巴都丁宜
• 兰卡威：珍南海滩

💰 价格参考：
• 经济型：80-150 MYR/晚
• 中档型：200-400 MYR/晚
• 豪华型：500+ MYR/晚

🔍 预订平台：Booking.com、Agoda、携程

注：AI服务暂时不可用，建议直接在预订平台搜索。`;
  }

  private getAttractionFallbackResponse(): string {
    return `🎯 马来西亚热门景点：

🏙️ 吉隆坡：
• 双子塔 (Petronas Towers)
• 吉隆坡塔 (KL Tower)
• 独立广场 (Merdeka Square)
• 茨厂街唐人街

🏛️ 马六甲：
• 荷兰红屋
• 圣保罗教堂
• 马六甲海峡清真寺

🍜 槟城：
• 乔治市世界遗产区
• 极乐寺
• 升旗山

🏖️ 兰卡威：
• 珍南海滩
• 天空之桥
• 红树林生态游

注：AI服务暂时不可用，以上为热门景点推荐。`;
  }

  private getWeatherFallbackResponse(): string {
    return `🌤️ 马来西亚天气信息：

🌡️ 气候特点：
• 热带雨林气候，全年炎热潮湿
• 平均温度：26-32°C
• 湿度：70-90%

🌧️ 雨季：
• 西海岸：9-12月
• 东海岸：11-3月

☀️ 最佳旅游时间：
• 西海岸：5-9月
• 东海岸：4-10月

👕 穿衣建议：
• 轻便透气的夏装
• 防晒用品必备
• 雨具随身携带

注：AI服务暂时不可用，建议查看实时天气预报。`;
  }

  private getGenericFallbackResponse(prompt: string): string {
    return `🤖 AI服务暂时不可用

我们正在努力恢复AI服务。在此期间，您可以：

📱 使用其他功能：
• 浏览热门目的地
• 查看旅游攻略
• 搜索航班和酒店
• 查看天气信息

🔄 稍后重试：
• AI服务通常会在几分钟内恢复
• 您也可以刷新页面重试

📞 需要帮助？
• 查看帮助文档
• 联系客服支持

您的请求："${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}"

感谢您的耐心等待！`;
  }
}

// 单例实例
export const standardOpenRouterService = new StandardOpenRouterService();
