/**
 * 🕐 统一智能时间分配引擎
 * 
 * 这是一个全新设计的时间分配系统，旨在解决以下问题：
 * 1. 消除12:48-12:48等异常时间显示
 * 2. 提供基于活动类型的智能时间分配
 * 3. 确保时间的连续性和合理性
 * 4. 支持地理位置和用户偏好的时间优化
 * 
 * <AUTHOR> Think System
 * @version 2.0.0
 * @created 2025-01-30
 */

export interface ActivityTimeRequirement {
  id: string;
  name: string;
  type: 'attraction' | 'meal' | 'transport' | 'accommodation' | 'shopping' | 'cultural' | 'entertainment';
  category?: string;
  location?: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  priority?: number;
  preferenceScore?: number;
  isPreferenceMatch?: boolean;
  duration?: number; // 建议持续时间（分钟）
  flexibilityLevel?: 'rigid' | 'flexible' | 'very_flexible'; // 时间灵活性
  optimalTimeSlots?: ('morning' | 'afternoon' | 'evening')[];
  weatherSensitive?: boolean;
  bookingRequired?: boolean;
  cost?: number;
}

export interface TimeSlot {
  startTime: string; // HH:MM 格式
  endTime: string;   // HH:MM 格式
  duration: number;  // 分钟
  confidence: number; // 0-1，时间分配的信心度
  reasoning: string[]; // 时间分配的推理过程
}

export interface AllocatedActivity extends ActivityTimeRequirement {
  timeSlot: TimeSlot;
  day: number;
  schedulingReason: string;
  conflicts?: string[];
  optimizations?: string[];
}

export interface DayTimeAllocation {
  day: number;
  date: string;
  activities: AllocatedActivity[];
  totalDuration: number; // 分钟
  efficiency: number; // 0-1，时间利用效率
  balance: number; // 0-1，活动类型平衡度
  warnings: string[];
  suggestions: string[];
}

export interface TimeAllocationResult {
  success: boolean;
  allocations: DayTimeAllocation[];
  summary: {
    totalActivities: number;
    totalDays: number;
    averageEfficiency: number;
    overallBalance: number;
    unallocatedActivities: ActivityTimeRequirement[];
  };
  qualityMetrics: {
    timeConsistency: number; // 时间一致性
    logicalFlow: number; // 逻辑流畅性
    userSatisfaction: number; // 用户满意度预测
  };
  warnings: string[];
  recommendations: string[];
}

/**
 * 统一智能时间分配引擎
 * 
 * 核心设计原则：
 * 1. 单一职责：只负责时间分配，不处理其他业务逻辑
 * 2. 高内聚低耦合：内部逻辑紧密，外部依赖最小
 * 3. 可测试性：每个方法都可以独立测试
 * 4. 可扩展性：支持新的活动类型和时间策略
 */
export class UnifiedTimeAllocationEngine {
  
  // 时间常量配置
  private static readonly TIME_CONFIG = {
    DAILY_START: 8 * 60,      // 08:00 (480分钟)
    DAILY_END: 22 * 60,       // 22:00 (1320分钟)
    MAX_DAILY_DURATION: 12 * 60, // 最大每日活动时间12小时
    MIN_ACTIVITY_DURATION: 15,    // 最小活动时间15分钟
    MAX_ACTIVITY_DURATION: 4 * 60, // 最大活动时间4小时
    BUFFER_TIME: 15,              // 活动间缓冲时间15分钟
    MEAL_SLOTS: {
      breakfast: { start: 8 * 60, end: 10 * 60 },   // 08:00-10:00
      lunch: { start: 12 * 60, end: 14 * 60 },      // 12:00-14:00
      dinner: { start: 18 * 60, end: 20 * 60 }      // 18:00-20:00
    }
  };

  // 活动类型默认配置
  private static readonly ACTIVITY_TYPE_CONFIG = {
    attraction: { 
      defaultDuration: 90, 
      minDuration: 45, 
      maxDuration: 180,
      optimalSlots: ['morning', 'afternoon'],
      priority: 8
    },
    meal: { 
      defaultDuration: 60, 
      minDuration: 30, 
      maxDuration: 120,
      optimalSlots: ['morning', 'afternoon', 'evening'],
      priority: 9
    },
    transport: { 
      defaultDuration: 20, 
      minDuration: 10, 
      maxDuration: 60,
      optimalSlots: ['morning', 'afternoon', 'evening'],
      priority: 5
    },
    accommodation: { 
      defaultDuration: 30, 
      minDuration: 15, 
      maxDuration: 60,
      optimalSlots: ['morning', 'evening'],
      priority: 7
    },
    shopping: { 
      defaultDuration: 75, 
      minDuration: 45, 
      maxDuration: 150,
      optimalSlots: ['afternoon', 'evening'],
      priority: 6
    },
    cultural: { 
      defaultDuration: 105, 
      minDuration: 60, 
      maxDuration: 180,
      optimalSlots: ['morning', 'afternoon'],
      priority: 8
    },
    entertainment: { 
      defaultDuration: 90, 
      minDuration: 60, 
      maxDuration: 240,
      optimalSlots: ['afternoon', 'evening'],
      priority: 7
    }
  };

  /**
   * 🎯 主要入口：为活动列表分配智能时间
   * 
   * @param activities 需要分配时间的活动列表
   * @param totalDays 总天数
   * @param startDate 开始日期
   * @param userPreferences 用户偏好（可选）
   * @returns 时间分配结果
   */
  static allocateIntelligentTimes(
    activities: ActivityTimeRequirement[],
    totalDays: number,
    startDate: Date = new Date(),
    userPreferences?: {
      preferredStartTime?: number; // 偏好开始时间（分钟）
      preferredEndTime?: number;   // 偏好结束时间（分钟）
      pacePreference?: 'relaxed' | 'moderate' | 'intensive'; // 节奏偏好
      mealImportance?: 'low' | 'medium' | 'high'; // 用餐重要性
    }
  ): TimeAllocationResult {
    console.log('🕐 开始统一智能时间分配');
    console.log(`📊 输入参数: ${activities.length}个活动, ${totalDays}天`);

    try {
      // 1. 验证和预处理输入
      const validatedActivities = this.validateAndPreprocessActivities(activities);
      
      // 2. 智能活动分组和排序
      const groupedActivities = this.intelligentActivityGrouping(validatedActivities, totalDays);
      
      // 3. 为每天分配时间
      const dayAllocations: DayTimeAllocation[] = [];
      
      for (let day = 1; day <= totalDays; day++) {
        const dayActivities = groupedActivities[day] || [];
        const dayDate = new Date(startDate);
        dayDate.setDate(startDate.getDate() + day - 1);
        
        const dayAllocation = this.allocateTimeForSingleDay(
          dayActivities,
          day,
          dayDate,
          userPreferences
        );
        
        dayAllocations.push(dayAllocation);
      }
      
      // 4. 生成总结和质量指标
      const summary = this.generateAllocationSummary(dayAllocations, activities);
      const qualityMetrics = this.calculateQualityMetrics(dayAllocations);
      
      // 5. 生成建议和警告
      const { warnings, recommendations } = this.generateInsightsAndRecommendations(dayAllocations);
      
      const result: TimeAllocationResult = {
        success: true,
        allocations: dayAllocations,
        summary,
        qualityMetrics,
        warnings,
        recommendations
      };
      
      console.log('✅ 统一智能时间分配完成');
      console.log(`📈 质量指标: 一致性${(qualityMetrics.timeConsistency * 100).toFixed(1)}%, 流畅性${(qualityMetrics.logicalFlow * 100).toFixed(1)}%`);
      
      return result;
      
    } catch (error) {
      console.error('❌ 时间分配失败:', error);
      
      return {
        success: false,
        allocations: [],
        summary: {
          totalActivities: activities.length,
          totalDays,
          averageEfficiency: 0,
          overallBalance: 0,
          unallocatedActivities: activities
        },
        qualityMetrics: {
          timeConsistency: 0,
          logicalFlow: 0,
          userSatisfaction: 0
        },
        warnings: [`时间分配系统错误: ${error instanceof Error ? error.message : '未知错误'}`],
        recommendations: ['请检查输入数据的有效性，或联系技术支持']
      };
    }
  }

  /**
   * 🔍 验证和预处理活动数据
   */
  private static validateAndPreprocessActivities(
    activities: ActivityTimeRequirement[]
  ): ActivityTimeRequirement[] {
    console.log('🔍 验证和预处理活动数据');
    
    return activities
      .filter(activity => {
        // 基本验证
        if (!activity.id || !activity.name) {
          console.warn(`⚠️ 跳过无效活动: ${JSON.stringify(activity)}`);
          return false;
        }
        return true;
      })
      .map(activity => {
        // 标准化活动数据
        const typeConfig = this.ACTIVITY_TYPE_CONFIG[activity.type] || this.ACTIVITY_TYPE_CONFIG.attraction;
        
        return {
          ...activity,
          type: activity.type || 'attraction',
          priority: activity.priority || typeConfig.priority,
          duration: this.calculateOptimalDuration(activity),
          flexibilityLevel: activity.flexibilityLevel || 'flexible',
          optimalTimeSlots: activity.optimalTimeSlots || typeConfig.optimalSlots,
          preferenceScore: activity.preferenceScore || 0.5,
          isPreferenceMatch: activity.isPreferenceMatch || false
        };
      });
  }

  /**
   * 🧮 计算活动的最优持续时间
   */
  private static calculateOptimalDuration(activity: ActivityTimeRequirement): number {
    const typeConfig = this.ACTIVITY_TYPE_CONFIG[activity.type] || this.ACTIVITY_TYPE_CONFIG.attraction;
    
    // 如果活动已有持续时间，验证其合理性
    if (activity.duration && activity.duration > 0) {
      const clampedDuration = Math.max(
        typeConfig.minDuration,
        Math.min(typeConfig.maxDuration, activity.duration)
      );
      
      if (clampedDuration !== activity.duration) {
        console.log(`⚡ 调整活动${activity.name}持续时间: ${activity.duration} → ${clampedDuration}分钟`);
      }
      
      return clampedDuration;
    }
    
    // 基于活动特性计算持续时间
    let baseDuration = typeConfig.defaultDuration;
    
    // 根据偏好匹配调整
    if (activity.isPreferenceMatch) {
      baseDuration *= 1.15; // 偏好匹配的活动时间增加15%
    }
    
    // 根据优先级调整
    if (activity.priority && activity.priority > 7) {
      baseDuration *= 1.1; // 高优先级活动时间增加10%
    }
    
    // 根据预订要求调整
    if (activity.bookingRequired) {
      baseDuration *= 1.05; // 需要预订的活动时间增加5%
    }
    
    // 确保在合理范围内
    const finalDuration = Math.max(
      typeConfig.minDuration,
      Math.min(typeConfig.maxDuration, Math.round(baseDuration))
    );
    
    console.log(`🧮 计算${activity.name}最优时长: ${finalDuration}分钟 (基础${typeConfig.defaultDuration}, 调整后${baseDuration.toFixed(1)})`);
    
    return finalDuration;
  }

  /**
   * 🎯 智能活动分组：将活动合理分配到各天
   */
  private static intelligentActivityGrouping(
    activities: ActivityTimeRequirement[],
    totalDays: number
  ): Record<number, ActivityTimeRequirement[]> {
    console.log('🎯 开始智能活动分组');
    
    const groups: Record<number, ActivityTimeRequirement[]> = {};
    
    // 初始化每天的活动组
    for (let day = 1; day <= totalDays; day++) {
      groups[day] = [];
    }
    
    // 按优先级和偏好排序活动
    const sortedActivities = [...activities].sort((a, b) => {
      // 1. 优先级排序
      const priorityDiff = (b.priority || 5) - (a.priority || 5);
      if (priorityDiff !== 0) return priorityDiff;
      
      // 2. 偏好匹配排序
      const preferenceA = a.isPreferenceMatch ? 1 : 0;
      const preferenceB = b.isPreferenceMatch ? 1 : 0;
      const preferenceDiff = preferenceB - preferenceA;
      if (preferenceDiff !== 0) return preferenceDiff;
      
      // 3. 偏好分数排序
      return (b.preferenceScore || 0) - (a.preferenceScore || 0);
    });
    
    // 智能分配算法
    let currentDay = 1;
    const dayDurations: Record<number, number> = {};
    
    // 初始化每天的已用时间
    for (let day = 1; day <= totalDays; day++) {
      dayDurations[day] = 0;
    }
    
    for (const activity of sortedActivities) {
      const activityDuration = activity.duration || 90;
      let bestDay = currentDay;
      let bestScore = -1;
      
      // 为活动找到最佳的天
      for (let day = 1; day <= totalDays; day++) {
        const dayDuration = dayDurations[day];
        
        // 检查是否还有足够时间
        if (dayDuration + activityDuration + this.TIME_CONFIG.BUFFER_TIME > this.TIME_CONFIG.MAX_DAILY_DURATION) {
          continue;
        }
        
        // 计算适合度分数
        let score = 0;
        
        // 1. 时间利用率分数（避免某天过满或过空）
        const utilizationRate = (dayDuration + activityDuration) / this.TIME_CONFIG.MAX_DAILY_DURATION;
        if (utilizationRate > 0.3 && utilizationRate < 0.8) {
          score += 30; // 理想利用率
        } else if (utilizationRate <= 0.3) {
          score += 20; // 可以接受的低利用率
        } else {
          score += 10; // 较高利用率，不太理想
        }
        
        // 2. 活动类型平衡分数
        const dayActivities = groups[day];
        const typeCount = dayActivities.filter(a => a.type === activity.type).length;
        if (typeCount === 0) {
          score += 20; // 新类型，增加多样性
        } else if (typeCount === 1) {
          score += 10; // 适度重复
        } else {
          score += 5; // 过多重复
        }
        
        // 3. 连续性分数（优先填充当前天）
        if (day === currentDay) {
          score += 15;
        }
        
        if (score > bestScore) {
          bestScore = score;
          bestDay = day;
        }
      }
      
      // 分配活动到最佳天
      if (bestScore > -1) {
        groups[bestDay].push(activity);
        dayDurations[bestDay] += activityDuration + this.TIME_CONFIG.BUFFER_TIME;
        
        console.log(`📅 分配${activity.name}到Day ${bestDay} (评分${bestScore}, 累计${dayDurations[bestDay]}分钟)`);
        
        // 🔧 改进的天数切换逻辑 - 确保活动均匀分布
        if (dayDurations[bestDay] > this.TIME_CONFIG.MAX_DAILY_DURATION * 0.6) {
          // 检查是否还有剩余活动需要分配
          const remainingActivities = sortedActivities.length - (sortedActivities.indexOf(activity) + 1);
          const remainingDays = totalDays - currentDay;

          // 如果剩余活动较多且还有剩余天数，切换到下一天
          if (remainingActivities > 0 && remainingDays > 0) {
            currentDay = Math.min(currentDay + 1, totalDays);
          }
        }
      } else {
        console.warn(`⚠️ 无法为活动${activity.name}找到合适的天`);
      }
    }
    
    // 🔧 后处理：平衡活动分布
    const balancedGroups = this.balanceActivityDistribution(groups, totalDays);

    // 输出最终分组结果
    for (let day = 1; day <= totalDays; day++) {
      const finalDuration = balancedGroups[day].reduce((sum, activity) =>
        sum + (activity.duration || 90), 0);
      console.log(`📊 Day ${day}: ${balancedGroups[day].length}个活动, 预计${finalDuration}分钟`);
    }

    return balancedGroups;
  }

  /**
   * 🔧 平衡活动分布：确保每天都有合理数量的活动
   */
  private static balanceActivityDistribution(
    groups: Record<number, ActivityTimeRequirement[]>,
    totalDays: number
  ): Record<number, ActivityTimeRequirement[]> {
    console.log('🔧 开始平衡活动分布');

    const balancedGroups = { ...groups };
    const totalActivities = Object.values(groups).flat().length;
    const targetActivitiesPerDay = Math.ceil(totalActivities / totalDays);

    // 找出活动过多和过少的天
    const overloadedDays: number[] = [];
    const underloadedDays: number[] = [];

    for (let day = 1; day <= totalDays; day++) {
      const dayActivities = balancedGroups[day].length;
      if (dayActivities > targetActivitiesPerDay + 1) {
        overloadedDays.push(day);
      } else if (dayActivities < Math.max(1, targetActivitiesPerDay - 1)) {
        underloadedDays.push(day);
      }
    }

    // 重新分配活动
    for (const overloadedDay of overloadedDays) {
      const activities = balancedGroups[overloadedDay];
      const excessCount = activities.length - targetActivitiesPerDay;

      if (excessCount > 0 && underloadedDays.length > 0) {
        // 移动优先级较低的活动到活动较少的天
        const activitiesToMove = activities
          .sort((a, b) => (a.priority || 5) - (b.priority || 5))
          .slice(0, Math.min(excessCount, underloadedDays.length));

        activitiesToMove.forEach((activity, index) => {
          if (index < underloadedDays.length) {
            const targetDay = underloadedDays[index];

            // 从原天移除
            balancedGroups[overloadedDay] = balancedGroups[overloadedDay]
              .filter(a => a.id !== activity.id);

            // 添加到目标天
            balancedGroups[targetDay].push(activity);

            console.log(`🔄 移动${activity.name}从Day ${overloadedDay}到Day ${targetDay}`);
          }
        });
      }
    }

    return balancedGroups;
  }

  /**
   * 📅 为单天分配具体时间
   */
  private static allocateTimeForSingleDay(
    activities: ActivityTimeRequirement[],
    dayNumber: number,
    date: Date,
    userPreferences?: any
  ): DayTimeAllocation {
    console.log(`📅 为Day ${dayNumber}分配具体时间，活动数量: ${activities.length}`);
    
    const allocatedActivities: AllocatedActivity[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];
    
    if (activities.length === 0) {
      return {
        day: dayNumber,
        date: date.toISOString().split('T')[0],
        activities: [],
        totalDuration: 0,
        efficiency: 0,
        balance: 0,
        warnings: ['这一天没有安排活动'],
        suggestions: ['可以考虑添加一些轻松的活动或自由时间']
      };
    }
    
    // 智能排序活动（考虑时间偏好和逻辑顺序）
    const sortedActivities = this.sortActivitiesForDay(activities);
    
    // 开始时间分配
    let currentTime = userPreferences?.preferredStartTime || this.TIME_CONFIG.DAILY_START;
    let totalDuration = 0;
    
    for (let i = 0; i < sortedActivities.length; i++) {
      const activity = sortedActivities[i];
      const duration = activity.duration || 90;
      
      // 检查是否需要插入用餐时间
      const mealActivity = this.checkAndInsertMealTime(currentTime, allocatedActivities, dayNumber);
      if (mealActivity) {
        allocatedActivities.push(mealActivity);
        currentTime = this.timeToMinutes(mealActivity.timeSlot.endTime) + this.TIME_CONFIG.BUFFER_TIME;
        totalDuration += mealActivity.timeSlot.duration + this.TIME_CONFIG.BUFFER_TIME;
      }
      
      // 计算活动的开始和结束时间
      const startTime = this.minutesToTime(currentTime);
      const endTime = this.minutesToTime(currentTime + duration);
      
      // 验证时间合理性
      if (currentTime + duration > this.TIME_CONFIG.DAILY_END) {
        warnings.push(`活动${activity.name}超出每日时间限制，已调整到合理范围内`);
        const adjustedDuration = this.TIME_CONFIG.DAILY_END - currentTime;
        if (adjustedDuration < this.TIME_CONFIG.MIN_ACTIVITY_DURATION) {
          warnings.push(`活动${activity.name}无法安排在今天，建议移至其他天`);
          continue;
        }
      }
      
      // 生成时间推理
      const reasoning = this.generateTimeReasoning(activity, currentTime, duration, i, sortedActivities);
      
      // 创建分配的活动
      const allocatedActivity: AllocatedActivity = {
        ...activity,
        timeSlot: {
          startTime,
          endTime,
          duration,
          confidence: this.calculateTimeConfidence(activity, currentTime),
          reasoning
        },
        day: dayNumber,
        schedulingReason: `Day ${dayNumber} ${this.getTimeSlotName(currentTime)}时段安排`,
        optimizations: this.generateOptimizations(activity, currentTime, sortedActivities)
      };
      
      allocatedActivities.push(allocatedActivity);
      
      // 更新当前时间
      currentTime += duration + this.TIME_CONFIG.BUFFER_TIME;
      totalDuration += duration + this.TIME_CONFIG.BUFFER_TIME;
      
      console.log(`⏰ ${activity.name}: ${startTime}-${endTime} (${duration}分钟, 信心度${(allocatedActivity.timeSlot.confidence * 100).toFixed(1)}%)`);
    }
    
    // 计算效率和平衡度
    const efficiency = Math.min(1.0, totalDuration / this.TIME_CONFIG.MAX_DAILY_DURATION);
    const balance = this.calculateActivityBalance(allocatedActivities);
    
    // 生成建议
    if (efficiency < 0.4) {
      suggestions.push('今天的安排相对轻松，可以考虑增加一些自由探索时间');
    } else if (efficiency > 0.8) {
      suggestions.push('今天的安排比较紧凑，建议预留一些休息时间');
    }
    
    return {
      day: dayNumber,
      date: date.toISOString().split('T')[0],
      activities: allocatedActivities,
      totalDuration,
      efficiency,
      balance,
      warnings,
      suggestions
    };
  }

  /**
   * 🔄 智能排序单天活动
   */
  private static sortActivitiesForDay(activities: ActivityTimeRequirement[]): ActivityTimeRequirement[] {
    return [...activities].sort((a, b) => {
      // 1. 按最佳时间段排序
      const aTimeScore = this.getTimeSlotScore(a);
      const bTimeScore = this.getTimeSlotScore(b);
      if (aTimeScore !== bTimeScore) {
        return aTimeScore - bTimeScore;
      }
      
      // 2. 按活动类型逻辑顺序排序
      const aTypeOrder = this.getActivityTypeOrder(a.type);
      const bTypeOrder = this.getActivityTypeOrder(b.type);
      if (aTypeOrder !== bTypeOrder) {
        return aTypeOrder - bTypeOrder;
      }
      
      // 3. 按优先级排序
      return (b.priority || 5) - (a.priority || 5);
    });
  }

  /**
   * 🍽️ 检查并插入用餐时间
   */
  private static checkAndInsertMealTime(
    currentTime: number,
    existingActivities: AllocatedActivity[],
    dayNumber: number
  ): AllocatedActivity | null {
    const mealSlots = this.TIME_CONFIG.MEAL_SLOTS;
    
    // 检查是否需要插入早餐
    if (currentTime >= mealSlots.breakfast.start && currentTime <= mealSlots.breakfast.end) {
      const hasBreakfast = existingActivities.some(a => 
        a.type === 'meal' && a.name.includes('早餐')
      );
      
      if (!hasBreakfast && existingActivities.length === 0) {
        return this.createMealActivity('breakfast', dayNumber, currentTime);
      }
    }
    
    // 检查是否需要插入午餐
    if (currentTime >= mealSlots.lunch.start && currentTime <= mealSlots.lunch.end) {
      const hasLunch = existingActivities.some(a => 
        a.type === 'meal' && (a.name.includes('午餐') || a.name.includes('lunch'))
      );
      
      if (!hasLunch) {
        return this.createMealActivity('lunch', dayNumber, currentTime);
      }
    }
    
    // 检查是否需要插入晚餐
    if (currentTime >= mealSlots.dinner.start && currentTime <= mealSlots.dinner.end) {
      const hasDinner = existingActivities.some(a => 
        a.type === 'meal' && (a.name.includes('晚餐') || a.name.includes('dinner'))
      );
      
      if (!hasDinner && existingActivities.length > 2) {
        return this.createMealActivity('dinner', dayNumber, currentTime);
      }
    }
    
    return null;
  }

  /**
   * 🍽️ 创建用餐活动
   */
  private static createMealActivity(
    mealType: 'breakfast' | 'lunch' | 'dinner',
    dayNumber: number,
    currentTime: number
  ): AllocatedActivity {
    const mealNames = {
      breakfast: '早餐时光',
      lunch: '午餐休憩',
      dinner: '晚餐享受'
    };
    
    const mealDurations = {
      breakfast: 45,
      lunch: 60,
      dinner: 75
    };
    
    const duration = mealDurations[mealType];
    const startTime = this.minutesToTime(currentTime);
    const endTime = this.minutesToTime(currentTime + duration);
    
    return {
      id: `meal_${mealType}_day${dayNumber}`,
      name: mealNames[mealType],
      type: 'meal',
      priority: 9,
      duration,
      timeSlot: {
        startTime,
        endTime,
        duration,
        confidence: 0.95,
        reasoning: [`${mealType === 'breakfast' ? '早晨' : mealType === 'lunch' ? '中午' : '傍晚'}是${mealNames[mealType]}的理想时间`]
      },
      day: dayNumber,
      schedulingReason: `Day ${dayNumber} 自动安排的${mealNames[mealType]}`,
      cost: 0
    };
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 🕐 分钟转时间字符串 (HH:MM)
   */
  private static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * 🕐 时间字符串转分钟
   */
  private static timeToMinutes(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * 🎯 获取时间段评分（用于排序）
   */
  private static getTimeSlotScore(activity: ActivityTimeRequirement): number {
    const optimalSlots = activity.optimalTimeSlots || ['morning'];
    
    if (optimalSlots.includes('morning')) return 1;
    if (optimalSlots.includes('afternoon')) return 2;
    if (optimalSlots.includes('evening')) return 3;
    
    return 2; // 默认下午
  }

  /**
   * 🏷️ 获取活动类型顺序
   */
  private static getActivityTypeOrder(type: string): number {
    const order = {
      transport: 1,
      accommodation: 2,
      meal: 3,
      attraction: 4,
      cultural: 5,
      shopping: 6,
      entertainment: 7
    };
    
    return order[type] || 4;
  }

  /**
   * 🕐 获取时间段名称
   */
  private static getTimeSlotName(minutes: number): string {
    if (minutes < 10 * 60) return '早晨';
    if (minutes < 14 * 60) return '上午';
    if (minutes < 17 * 60) return '下午';
    if (minutes < 20 * 60) return '傍晚';
    return '晚上';
  }

  /**
   * 🧠 生成时间推理
   */
  private static generateTimeReasoning(
    activity: ActivityTimeRequirement,
    startTime: number,
    duration: number,
    index: number,
    allActivities: ActivityTimeRequirement[]
  ): string[] {
    const reasoning: string[] = [];
    const timeSlotName = this.getTimeSlotName(startTime);
    
    // 基于时间段的推理
    reasoning.push(`安排在${timeSlotName}，适合${activity.type === 'attraction' ? '观光游览' : activity.type === 'meal' ? '用餐' : '此类活动'}`);
    
    // 基于持续时间的推理
    if (duration > 120) {
      reasoning.push(`预留${Math.round(duration/60*10)/10}小时，确保充分体验`);
    } else if (duration < 60) {
      reasoning.push(`${duration}分钟的紧凑安排，高效利用时间`);
    }
    
    // 基于顺序的推理
    if (index === 0) {
      reasoning.push('作为今天的第一个活动，为一天开个好头');
    } else if (index === allActivities.length - 1) {
      reasoning.push('作为今天的最后一个活动，完美收尾');
    }
    
    // 基于偏好的推理
    if (activity.isPreferenceMatch) {
      reasoning.push('符合您的个人偏好，优先安排');
    }
    
    return reasoning;
  }

  /**
   * 📊 计算时间信心度
   */
  private static calculateTimeConfidence(activity: ActivityTimeRequirement, startTime: number): number {
    let confidence = 0.8; // 基础信心度
    
    // 基于最佳时间段调整
    const timeSlotName = this.getTimeSlotName(startTime);
    const optimalSlots = activity.optimalTimeSlots || [];
    
    if (optimalSlots.includes('morning') && startTime < 12 * 60) confidence += 0.1;
    if (optimalSlots.includes('afternoon') && startTime >= 12 * 60 && startTime < 17 * 60) confidence += 0.1;
    if (optimalSlots.includes('evening') && startTime >= 17 * 60) confidence += 0.1;
    
    // 基于活动特性调整
    if (activity.bookingRequired) confidence += 0.05;
    if (activity.flexibilityLevel === 'rigid') confidence -= 0.1;
    if (activity.flexibilityLevel === 'very_flexible') confidence += 0.05;
    
    return Math.min(1.0, Math.max(0.0, confidence));
  }

  /**
   * ⚡ 生成优化建议
   */
  private static generateOptimizations(
    activity: ActivityTimeRequirement,
    startTime: number,
    allActivities: ActivityTimeRequirement[]
  ): string[] {
    const optimizations: string[] = [];
    
    // 基于天气的优化
    if (activity.weatherSensitive) {
      optimizations.push('建议关注天气预报，如遇恶劣天气可调整时间');
    }
    
    // 基于预订的优化
    if (activity.bookingRequired) {
      optimizations.push('建议提前预订以确保顺利进行');
    }
    
    // 基于位置的优化
    if (activity.location) {
      optimizations.push('建议提前查看交通路线和周边设施');
    }
    
    return optimizations;
  }

  /**
   * ⚖️ 计算活动平衡度
   */
  private static calculateActivityBalance(activities: AllocatedActivity[]): number {
    if (activities.length === 0) return 0;
    
    const typeCounts: Record<string, number> = {};
    activities.forEach(activity => {
      typeCounts[activity.type] = (typeCounts[activity.type] || 0) + 1;
    });
    
    const types = Object.keys(typeCounts);
    const totalActivities = activities.length;
    
    // 计算类型分布的均匀度
    let balance = 0;
    types.forEach(type => {
      const ratio = typeCounts[type] / totalActivities;
      balance += ratio * (1 - ratio); // 越均匀，这个值越大
    });
    
    return Math.min(1.0, balance * 2); // 标准化到0-1
  }

  /**
   * 📊 生成分配总结
   */
  private static generateAllocationSummary(
    dayAllocations: DayTimeAllocation[],
    originalActivities: ActivityTimeRequirement[]
  ) {
    const totalAllocated = dayAllocations.reduce((sum, day) => sum + day.activities.length, 0);
    const totalDays = dayAllocations.length;
    const averageEfficiency = dayAllocations.reduce((sum, day) => sum + day.efficiency, 0) / totalDays;
    const overallBalance = dayAllocations.reduce((sum, day) => sum + day.balance, 0) / totalDays;
    
    const unallocatedActivities = originalActivities.filter(original => 
      !dayAllocations.some(day => 
        day.activities.some(allocated => allocated.id === original.id)
      )
    );
    
    return {
      totalActivities: totalAllocated,
      totalDays,
      averageEfficiency,
      overallBalance,
      unallocatedActivities
    };
  }

  /**
   * 📈 计算质量指标
   */
  private static calculateQualityMetrics(dayAllocations: DayTimeAllocation[]) {
    // 时间一致性：检查时间是否连续、合理
    let timeConsistency = 0;
    let totalTimeChecks = 0;
    
    dayAllocations.forEach(day => {
      for (let i = 0; i < day.activities.length - 1; i++) {
        const current = day.activities[i];
        const next = day.activities[i + 1];
        
        const currentEnd = this.timeToMinutes(current.timeSlot.endTime);
        const nextStart = this.timeToMinutes(next.timeSlot.startTime);
        
        // 检查时间间隔是否合理（5-30分钟）
        const gap = nextStart - currentEnd;
        if (gap >= 5 && gap <= 30) {
          timeConsistency += 1;
        } else if (gap >= 0 && gap < 60) {
          timeConsistency += 0.7; // 可接受的间隔
        } else {
          timeConsistency += 0.3; // 不理想的间隔
        }
        
        totalTimeChecks += 1;
      }
    });
    
    timeConsistency = totalTimeChecks > 0 ? timeConsistency / totalTimeChecks : 1;
    
    // 逻辑流畅性：检查活动顺序是否合理
    const logicalFlow = dayAllocations.reduce((sum, day) => sum + day.balance, 0) / dayAllocations.length;
    
    // 用户满意度预测：基于效率和平衡度
    const userSatisfaction = (
      dayAllocations.reduce((sum, day) => sum + day.efficiency, 0) / dayAllocations.length * 0.4 +
      logicalFlow * 0.3 +
      timeConsistency * 0.3
    );
    
    return {
      timeConsistency,
      logicalFlow,
      userSatisfaction
    };
  }

  /**
   * 💡 生成洞察和建议
   */
  private static generateInsightsAndRecommendations(dayAllocations: DayTimeAllocation[]) {
    const warnings: string[] = [];
    const recommendations: string[] = [];
    
    // 分析每天的情况
    dayAllocations.forEach(day => {
      warnings.push(...day.warnings);
      recommendations.push(...day.suggestions);
      
      // 效率分析
      if (day.efficiency < 0.3) {
        recommendations.push(`Day ${day.day}安排较为轻松，可以考虑增加一些自由探索时间或休闲活动`);
      } else if (day.efficiency > 0.85) {
        warnings.push(`Day ${day.day}安排较为紧凑，建议预留充足的休息和转换时间`);
      }
      
      // 平衡度分析
      if (day.balance < 0.3) {
        recommendations.push(`Day ${day.day}活动类型相对单一，可以考虑增加不同类型的活动以丰富体验`);
      }
    });
    
    // 整体建议
    const totalActivities = dayAllocations.reduce((sum, day) => sum + day.activities.length, 0);
    const averagePerDay = totalActivities / dayAllocations.length;
    
    if (averagePerDay < 3) {
      recommendations.push('整体安排相对轻松，适合深度体验和放松旅行');
    } else if (averagePerDay > 6) {
      recommendations.push('整体安排较为丰富，建议确保有足够的体力和时间');
    }
    
    return { warnings, recommendations };
  }
}