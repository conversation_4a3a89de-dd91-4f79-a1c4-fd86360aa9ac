/**
 * 🗓️ 季节日期工具 - Ultra Think系统性修复
 * 统一处理季节与日期的转换逻辑，修复冬季日期显示问题
 */

export type Season = 'spring' | 'summer' | 'autumn' | 'winter';
export type SeasonChinese = '春季' | '夏季' | '秋季' | '冬季';

export interface SeasonInfo {
  season: Season;
  chineseName: SeasonChinese;
  months: number[];
  emoji: string;
  description: string;
  characteristics: string[];
}

export interface SeasonDateResult {
  date: Date;
  season: Season;
  year: number;
  month: number;
  isCurrentYear: boolean;
  explanation: string;
}

export class SeasonDateUtils {
  
  /**
   * 🗓️ 季节信息映射
   */
  private static readonly SEASON_INFO: Record<Season, SeasonInfo> = {
    spring: {
      season: 'spring',
      chineseName: '春季',
      months: [3, 4, 5],
      emoji: '🌸',
      description: '万物复苏的季节',
      characteristics: ['温暖', '花开', '适宜出行']
    },
    summer: {
      season: 'summer',
      chineseName: '夏季',
      months: [6, 7, 8],
      emoji: '☀️',
      description: '阳光明媚的季节',
      characteristics: ['炎热', '阳光充足', '适合海边']
    },
    autumn: {
      season: 'autumn',
      chineseName: '秋季',
      months: [9, 10, 11],
      emoji: '🍂',
      description: '收获的季节',
      characteristics: ['凉爽', '秋高气爽', '适合登山']
    },
    winter: {
      season: 'winter',
      chineseName: '冬季',
      months: [12, 1, 2],
      emoji: '❄️',
      description: '雪花飞舞的季节',
      characteristics: ['寒冷', '雪景', '适合温泉']
    }
  };

  /**
   * 🔍 根据日期判断季节
   */
  static getSeasonFromDate(date: Date): Season {
    const month = date.getMonth() + 1; // 1-12
    
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  /**
   * 🔄 中文季节转英文季节
   */
  static chineseToEnglishSeason(chineseSeason: SeasonChinese): Season {
    const mapping: Record<SeasonChinese, Season> = {
      '春季': 'spring',
      '夏季': 'summer',
      '秋季': 'autumn',
      '冬季': 'winter'
    };
    return mapping[chineseSeason] || 'spring';
  }

  /**
   * 🔄 英文季节转中文季节
   */
  static englishToChineseSeason(englishSeason: Season): SeasonChinese {
    return this.SEASON_INFO[englishSeason].chineseName;
  }

  /**
   * 🗓️ 智能季节日期选择 - 核心修复函数
   * 根据当前日期和选择的季节，智能选择最合适的日期
   */
  static getSmartSeasonDate(season: SeasonChinese | Season): SeasonDateResult {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // 1-12
    
    // 转换为英文季节
    const englishSeason = typeof season === 'string' && season.includes('季') 
      ? this.chineseToEnglishSeason(season as SeasonChinese)
      : season as Season;
    
    const seasonInfo = this.SEASON_INFO[englishSeason];
    
    // 根据季节和当前时间智能选择日期
    let targetYear = currentYear;
    let targetMonth: number;
    let explanation: string;
    
    switch (englishSeason) {
      case 'spring':
        // 春季：3-5月
        if (currentMonth <= 5) {
          // 当前在春季或之前，选择当前年份
          targetMonth = 4; // 4月中旬
          explanation = `选择${currentYear}年春季（4月）`;
        } else {
          // 当前已过春季，选择下一年
          targetYear = currentYear + 1;
          targetMonth = 4;
          explanation = `当前已过春季，选择${targetYear}年春季（4月）`;
        }
        break;
        
      case 'summer':
        // 夏季：6-8月
        if (currentMonth <= 8) {
          targetMonth = 7; // 7月中旬
          explanation = `选择${currentYear}年夏季（7月）`;
        } else {
          targetYear = currentYear + 1;
          targetMonth = 7;
          explanation = `当前已过夏季，选择${targetYear}年夏季（7月）`;
        }
        break;
        
      case 'autumn':
        // 秋季：9-11月
        if (currentMonth <= 11) {
          targetMonth = 10; // 10月中旬
          explanation = `选择${currentYear}年秋季（10月）`;
        } else {
          targetYear = currentYear + 1;
          targetMonth = 10;
          explanation = `当前已过秋季，选择${targetYear}年秋季（10月）`;
        }
        break;
        
      case 'winter':
        // 冬季：12月、1-2月 - 关键修复点
        if (currentMonth >= 1 && currentMonth <= 2) {
          // 当前是1-2月（冬季中），选择当前年份的1月
          targetMonth = 1;
          explanation = `当前正值冬季，选择${currentYear}年1月`;
        } else if (currentMonth >= 3 && currentMonth <= 11) {
          // 当前是3-11月，选择当前年份的12月
          targetMonth = 12;
          explanation = `选择${currentYear}年冬季（12月）`;
        } else {
          // 当前是12月，可以选择当前12月或下一年1月
          // 为了避免混淆，选择下一年1月
          targetYear = currentYear + 1;
          targetMonth = 1;
          explanation = `当前12月，选择${targetYear}年1月`;
        }
        break;
        
      default:
        targetMonth = currentMonth;
        explanation = `使用当前月份`;
    }
    
    const resultDate = new Date(targetYear, targetMonth - 1, 15); // 月份中旬
    
    return {
      date: resultDate,
      season: englishSeason,
      year: targetYear,
      month: targetMonth,
      isCurrentYear: targetYear === currentYear,
      explanation
    };
  }

  /**
   * 📅 获取季节的所有月份
   */
  static getSeasonMonths(season: Season): number[] {
    return this.SEASON_INFO[season].months;
  }

  /**
   * 🎨 获取季节信息
   */
  static getSeasonInfo(season: Season): SeasonInfo {
    return this.SEASON_INFO[season];
  }

  /**
   * 🔍 检查日期是否在指定季节内
   */
  static isDateInSeason(date: Date, season: Season): boolean {
    const dateMonth = date.getMonth() + 1;
    const seasonMonths = this.getSeasonMonths(season);
    return seasonMonths.includes(dateMonth);
  }

  /**
   * 📊 获取季节的日期范围
   */
  static getSeasonDateRange(season: Season, year?: number): { start: Date; end: Date } {
    const targetYear = year || new Date().getFullYear();
    const months = this.getSeasonMonths(season);
    
    if (season === 'winter') {
      // 冬季跨年处理：12月到次年2月
      return {
        start: new Date(targetYear, 11, 1), // 12月1日
        end: new Date(targetYear + 1, 1, 28) // 次年2月28日
      };
    } else {
      // 其他季节在同一年内
      const startMonth = Math.min(...months) - 1; // 转为0-11
      const endMonth = Math.max(...months) - 1;
      return {
        start: new Date(targetYear, startMonth, 1),
        end: new Date(targetYear, endMonth + 1, 0) // 月末
      };
    }
  }

  /**
   * 🎯 格式化季节日期显示
   */
  static formatSeasonDate(seasonDateResult: SeasonDateResult): string {
    const { date, season, explanation } = seasonDateResult;
    const seasonInfo = this.getSeasonInfo(season);
    
    return `${seasonInfo.emoji} ${seasonInfo.chineseName} - ${date.getFullYear()}年${date.getMonth() + 1}月 (${explanation})`;
  }

  /**
   * 🔧 验证季节日期选择的合理性
   */
  static validateSeasonDate(date: Date, expectedSeason: Season): {
    isValid: boolean;
    actualSeason: Season;
    message: string;
  } {
    const actualSeason = this.getSeasonFromDate(date);
    const isValid = actualSeason === expectedSeason;
    
    return {
      isValid,
      actualSeason,
      message: isValid 
        ? `日期与${this.englishToChineseSeason(expectedSeason)}匹配`
        : `日期属于${this.englishToChineseSeason(actualSeason)}，不是${this.englishToChineseSeason(expectedSeason)}`
    };
  }
}
