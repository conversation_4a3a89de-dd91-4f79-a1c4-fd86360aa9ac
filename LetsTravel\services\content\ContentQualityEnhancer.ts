/**
 * 📝 内容质量增强器
 * 
 * 解决以下问题：
 * 1. 活动描述内容质量偏低
 * 2. 缺乏有价值的建议和提示
 * 3. 内容不够丰富和个性化
 * 4. 缺乏实用的旅行信息
 * 
 * <AUTHOR> Think System
 * @version 2.0.0
 * @created 2025-01-30
 */

export interface ContentEnhancementRequest {
  activity: {
    id: string;
    name: string;
    name_zh?: string;
    type: string;
    category?: string;
    description?: string;
    description_zh?: string;
    location?: {
      name: string;
      address: string;
      coordinates?: { lat: number; lng: number };
    };
    timing?: {
      startTime: string;
      endTime: string;
      duration: number;
      day: number;
    };
    cost?: {
      amount: number;
      currency: string;
      priceLevel: string;
    };
    details?: any;
  };
  destination: string;
  userPreferences?: {
    interests?: string[];
    travelStyle?: 'budget' | 'moderate' | 'luxury';
    groupType?: 'solo' | 'couple' | 'family' | 'friends';
  };
  contextActivities?: any[]; // 同天的其他活动，用于生成关联建议
}

export interface EnhancedContent {
  enhancedDescription: string;
  enhancedDescriptionZh: string;
  highlights: string[];
  practicalTips: string[];
  recommendations: string[];
  warnings: string[];
  bestTimeToVisit: string;
  estimatedDuration: string;
  difficultyLevel: 'easy' | 'moderate' | 'challenging';
  suitableFor: string[];
  nearbyAttractions: string[];
  culturalInsights: string[];
  photoSpots: string[];
  budgetTips: string[];
  accessibilityInfo: string[];
  weatherConsiderations: string[];
  qualityScore: number; // 0-1，内容质量评分
  enhancementReason: string[];
}

/**
 * 内容质量增强器
 * 
 * 核心特性：
 * 1. 智能内容生成：基于活动特征生成丰富内容
 * 2. 个性化建议：根据用户偏好定制建议
 * 3. 实用信息：提供有价值的旅行提示
 * 4. 文化洞察：增加文化背景和历史信息
 */
export class ContentQualityEnhancer {
  
  // 内容模板库
  private static readonly CONTENT_TEMPLATES = {
    meal: {
      descriptions: [
        '品尝{cuisine}的精髓，感受{location}的美食文化',
        '在{location}享受{cuisine}的独特魅力，让味蕾体验难忘之旅',
        '探索{location}的美食秘密，{cuisine}将为您带来惊喜',
        '沉浸在{location}的餐饮氛围中，品味正宗{cuisine}'
      ],
      highlights: [
        '新鲜食材精心烹制', '地道口味值得品尝', '环境舒适服务周到',
        '性价比高推荐尝试', '当地人气餐厅', '特色菜品不容错过'
      ],
      tips: [
        '建议提前查看菜单了解价格', '高峰时段可能需要等位',
        '可询问服务员推荐特色菜', '注意营业时间安排',
        '建议预留充足用餐时间', '可尝试当地特色饮品'
      ]
    },
    
    attraction: {
      descriptions: [
        '探索{name}的历史文化底蕴，感受{location}的独特魅力',
        '在{name}领略{location}的自然美景与人文风情',
        '{name}是{location}不可错过的标志性景点，值得深度体验',
        '漫步{name}，感受{location}深厚的历史文化积淀'
      ],
      highlights: [
        '历史悠久文化深厚', '建筑精美值得欣赏', '拍照打卡绝佳地点',
        '交通便利容易到达', '周边设施完善', '适合各年龄段游客'
      ],
      tips: [
        '建议穿着舒适的步行鞋', '携带相机记录美好时光',
        '注意开放时间和门票信息', '可提前了解历史背景',
        '建议预留充足游览时间', '注意保护文物古迹'
      ]
    },
    
    cultural: {
      descriptions: [
        '深入{name}，探索{location}的艺术文化宝藏',
        '在{name}感受{location}深厚的文化底蕴和艺术魅力',
        '{name}汇聚了{location}的文化精华，是文化爱好者的天堂',
        '通过{name}的展览，了解{location}的历史文化传承'
      ],
      highlights: [
        '珍贵藏品值得细品', '专业讲解增进理解', '互动体验寓教于乐',
        '建筑本身就是艺术', '定期更新展览内容', '文化教育意义深远'
      ],
      tips: [
        '建议租借语音导览设备', '可参加专业导览团',
        '注意拍照规定和限制', '建议预留2-3小时参观',
        '可购买相关书籍深入了解', '注意保持安静尊重他人'
      ]
    },
    
    entertainment: {
      descriptions: [
        '在{name}享受{location}的娱乐时光，释放旅途压力',
        '{name}为您提供{location}独特的娱乐体验',
        '体验{name}的精彩项目，感受{location}的活力',
        '在{name}度过愉快时光，创造美好旅行回忆'
      ],
      highlights: [
        '项目丰富适合全家', '设施先进体验佳', '安全措施完善',
        '工作人员服务热情', '性价比高值得体验', '适合拍照留念'
      ],
      tips: [
        '建议提前购票避免排队', '注意身高体重限制',
        '携带必要的个人物品', '遵守安全规定和指示',
        '可购买快速通道票', '注意营业时间安排'
      ]
    },
    
    shopping: {
      descriptions: [
        '在{name}探索{location}的购物天堂，寻找心仪商品',
        '{name}汇聚了{location}的特色商品和国际品牌',
        '体验{name}的购物乐趣，发现{location}的独特商品',
        '在{name}享受购物时光，带走{location}的美好回忆'
      ],
      highlights: [
        '品牌齐全选择丰富', '价格合理性价比高', '购物环境舒适',
        '服务周到体验佳', '特色商品值得购买', '交通便利易到达'
      ],
      tips: [
        '可比较不同店铺价格', '注意退税政策和流程',
        '保留购物小票备查', '了解商品保修政策',
        '可询问店员推荐商品', '注意营业时间安排'
      ]
    }
  };

  // 文化洞察库
  private static readonly CULTURAL_INSIGHTS = {
    japan: {
      general: [
        '日本文化注重细节和完美主义', '礼貌和尊重是日本社会的核心价值',
        '季节变化在日本文化中占重要地位', '传统与现代在日本和谐共存'
      ],
      dining: [
        '用餐前说"いただきます"表示感谢', '不要在碗里留下米粒',
        '筷子使用有特定礼仪', '拉面可以发出声音表示美味'
      ],
      temples: [
        '进入神社前要洗手漱口', '参拜时要鞠躬表示尊敬',
        '不要大声喧哗保持肃静', '可以购买御守作为纪念'
      ]
    },
    china: {
      general: [
        '中华文化历史悠久博大精深', '尊老爱幼是传统美德',
        '中医养生理念深入人心', '书法绘画体现文化修养'
      ],
      dining: [
        '圆桌用餐体现团圆文化', '长辈先动筷表示尊重',
        '茶文化是重要组成部分', '不同地区有不同饮食习惯'
      ],
      attractions: [
        '古建筑体现传统工艺精髓', '园林设计追求天人合一',
        '文物保护意识需要加强', '历史故事增加游览趣味'
      ]
    }
  };

  /**
   * 📝 增强活动内容
   */
  static enhanceActivityContent(request: ContentEnhancementRequest): EnhancedContent {
    console.log(`📝 开始增强活动内容: ${request.activity.name}`);
    
    const activity = request.activity;
    const activityType = activity.type || 'attraction';
    
    // 1. 生成增强描述
    const { enhancedDescription, enhancedDescriptionZh } = this.generateEnhancedDescription(
      activity,
      request.destination,
      activityType
    );
    
    // 2. 生成亮点
    const highlights = this.generateHighlights(activity, activityType);
    
    // 3. 生成实用提示
    const practicalTips = this.generatePracticalTips(activity, activityType, request.userPreferences);
    
    // 4. 生成推荐建议
    const recommendations = this.generateRecommendations(activity, request.contextActivities);
    
    // 5. 生成警告信息
    const warnings = this.generateWarnings(activity, activityType);
    
    // 6. 生成最佳访问时间
    const bestTimeToVisit = this.generateBestTimeToVisit(activity, activityType);
    
    // 7. 生成预估时长
    const estimatedDuration = this.generateEstimatedDuration(activity);
    
    // 8. 评估难度等级
    const difficultyLevel = this.assessDifficultyLevel(activity, activityType);
    
    // 9. 生成适合人群
    const suitableFor = this.generateSuitableFor(activity, activityType);
    
    // 10. 生成周边景点
    const nearbyAttractions = this.generateNearbyAttractions(activity, request.destination);
    
    // 11. 生成文化洞察
    const culturalInsights = this.generateCulturalInsights(activity, request.destination);
    
    // 12. 生成拍照点
    const photoSpots = this.generatePhotoSpots(activity, activityType);
    
    // 13. 生成预算提示
    const budgetTips = this.generateBudgetTips(activity, request.userPreferences?.travelStyle);
    
    // 14. 生成无障碍信息
    const accessibilityInfo = this.generateAccessibilityInfo(activity, activityType);
    
    // 15. 生成天气考虑
    const weatherConsiderations = this.generateWeatherConsiderations(activity, activityType);
    
    // 16. 计算质量评分
    const qualityScore = this.calculateQualityScore({
      enhancedDescription,
      highlights,
      practicalTips,
      recommendations,
      culturalInsights
    });
    
    // 17. 生成增强原因
    const enhancementReason = this.generateEnhancementReason(activity, qualityScore);
    
    const result: EnhancedContent = {
      enhancedDescription,
      enhancedDescriptionZh,
      highlights,
      practicalTips,
      recommendations,
      warnings,
      bestTimeToVisit,
      estimatedDuration,
      difficultyLevel,
      suitableFor,
      nearbyAttractions,
      culturalInsights,
      photoSpots,
      budgetTips,
      accessibilityInfo,
      weatherConsiderations,
      qualityScore,
      enhancementReason
    };
    
    console.log(`✅ 内容增强完成，质量评分: ${(qualityScore * 100).toFixed(1)}%`);
    
    return result;
  }

  /**
   * 📝 生成增强描述
   */
  private static generateEnhancedDescription(
    activity: any,
    destination: string,
    activityType: string
  ): { enhancedDescription: string; enhancedDescriptionZh: string } {
    const templates = this.CONTENT_TEMPLATES[activityType]?.descriptions || 
                    this.CONTENT_TEMPLATES.attraction.descriptions;
    
    const template = templates[Math.floor(Math.random() * templates.length)];
    
    // 替换模板变量
    let description = template
      .replace('{name}', activity.name || activity.name_zh || '此地')
      .replace('{location}', destination)
      .replace('{cuisine}', this.inferCuisineType(activity));
    
    // 基于现有描述增强
    if (activity.description || activity.description_zh) {
      const existingDesc = activity.description || activity.description_zh;
      description = `${description}。${existingDesc}`;
    }
    
    // 添加特色信息
    description = this.addSpecialFeatures(description, activity, activityType);
    
    return {
      enhancedDescription: description,
      enhancedDescriptionZh: description // 目前中英文相同，可以后续分别优化
    };
  }

  /**
   * ⭐ 生成亮点
   */
  private static generateHighlights(activity: any, activityType: string): string[] {
    const baseHighlights = this.CONTENT_TEMPLATES[activityType]?.highlights || 
                          this.CONTENT_TEMPLATES.attraction.highlights;
    
    const highlights = [...baseHighlights.slice(0, 3)]; // 取前3个基础亮点
    
    // 基于活动特征添加特定亮点
    if (activity.cost?.amount === 0) {
      highlights.push('完全免费无需门票');
    }
    
    if (activity.details?.rating && activity.details.rating > 4.5) {
      highlights.push('游客评价极高推荐');
    }
    
    if (activity.details?.bookingRequired) {
      highlights.push('热门景点建议预订');
    }
    
    if (activity.location?.name?.includes('中心') || activity.location?.name?.includes('市区')) {
      highlights.push('地理位置优越便利');
    }
    
    return highlights.slice(0, 4); // 最多4个亮点
  }

  /**
   * 💡 生成实用提示
   */
  private static generatePracticalTips(
    activity: any,
    activityType: string,
    userPreferences?: any
  ): string[] {
    const baseTips = this.CONTENT_TEMPLATES[activityType]?.tips || 
                    this.CONTENT_TEMPLATES.attraction.tips;
    
    const tips = [...baseTips.slice(0, 2)]; // 取前2个基础提示
    
    // 基于时间的提示
    if (activity.timing?.duration > 120) {
      tips.push('活动时间较长建议携带水和小食');
    }
    
    // 基于费用的提示
    if (activity.cost?.amount > 100) {
      tips.push('费用较高建议提前了解包含项目');
    }
    
    // 基于用户偏好的提示
    if (userPreferences?.travelStyle === 'budget') {
      tips.push('可寻找优惠券或团购价格');
    }
    
    if (userPreferences?.groupType === 'family') {
      tips.push('适合家庭出游注意儿童安全');
    }
    
    return tips.slice(0, 4); // 最多4个提示
  }

  /**
   * 🎯 生成推荐建议
   */
  private static generateRecommendations(activity: any, contextActivities?: any[]): string[] {
    const recommendations: string[] = [];
    
    // 基于活动类型的推荐
    if (activity.type === 'meal') {
      recommendations.push('可尝试当地特色菜品');
      recommendations.push('建议询问服务员推荐');
    } else if (activity.type === 'attraction') {
      recommendations.push('建议了解历史背景增加趣味');
      recommendations.push('可购买纪念品留作纪念');
    }
    
    // 基于上下文活动的推荐
    if (contextActivities && contextActivities.length > 0) {
      const hasNearbyActivity = contextActivities.some(ctx => 
        ctx.location?.name?.includes(activity.location?.name?.split('附近')[0] || '')
      );
      
      if (hasNearbyActivity) {
        recommendations.push('可与附近其他活动一起安排');
      }
    }
    
    // 基于时间的推荐
    if (activity.timing?.startTime) {
      const hour = parseInt(activity.timing.startTime.split(':')[0]);
      if (hour < 10) {
        recommendations.push('早晨时光适合拍照人少景美');
      } else if (hour > 18) {
        recommendations.push('傍晚时分可欣赏夜景灯光');
      }
    }
    
    return recommendations.slice(0, 3); // 最多3个推荐
  }

  /**
   * ⚠️ 生成警告信息
   */
  private static generateWarnings(activity: any, activityType: string): string[] {
    const warnings: string[] = [];
    
    // 基于活动类型的警告
    if (activityType === 'attraction' && activity.location?.name?.includes('山')) {
      warnings.push('山区活动注意天气变化和安全');
    }
    
    if (activityType === 'meal' && activity.cost?.amount > 200) {
      warnings.push('高端餐厅建议提前预订');
    }
    
    // 基于时间的警告
    if (activity.timing?.duration > 180) {
      warnings.push('活动时间较长请合理安排体力');
    }
    
    // 基于费用的警告
    if (activity.details?.bookingRequired) {
      warnings.push('需要预订请提前安排避免失望');
    }
    
    return warnings;
  }

  /**
   * ⏰ 生成最佳访问时间
   */
  private static generateBestTimeToVisit(activity: any, activityType: string): string {
    if (activityType === 'meal') {
      if (activity.name?.includes('早餐')) return '08:00-10:00';
      if (activity.name?.includes('午餐')) return '12:00-14:00';
      if (activity.name?.includes('晚餐')) return '18:00-20:00';
      return '用餐时间';
    }
    
    if (activityType === 'attraction') {
      if (activity.location?.name?.includes('观景') || activity.location?.name?.includes('景台')) {
        return '日出日落时分最佳';
      }
      return '上午或下午避开高峰';
    }
    
    if (activityType === 'cultural') {
      return '开馆后1-2小时人流较少';
    }
    
    return '根据个人时间安排';
  }

  /**
   * ⏱️ 生成预估时长
   */
  private static generateEstimatedDuration(activity: any): string {
    const duration = activity.timing?.duration || activity.duration || 90;
    
    if (duration < 60) {
      return `约${duration}分钟`;
    } else if (duration < 120) {
      return `约${Math.round(duration/60*10)/10}小时`;
    } else {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return minutes > 0 ? `约${hours}小时${minutes}分钟` : `约${hours}小时`;
    }
  }

  /**
   * 📊 评估难度等级
   */
  private static assessDifficultyLevel(activity: any, activityType: string): 'easy' | 'moderate' | 'challenging' {
    // 基于活动类型
    if (activityType === 'meal' || activityType === 'shopping') {
      return 'easy';
    }
    
    // 基于时长
    const duration = activity.timing?.duration || 90;
    if (duration > 180) {
      return 'moderate';
    }
    
    // 基于名称特征
    const name = (activity.name || '').toLowerCase();
    if (name.includes('徒步') || name.includes('登山') || name.includes('hiking')) {
      return 'challenging';
    }
    
    if (name.includes('博物馆') || name.includes('展览')) {
      return 'moderate';
    }
    
    return 'easy';
  }

  /**
   * 👥 生成适合人群
   */
  private static generateSuitableFor(activity: any, activityType: string): string[] {
    const suitableFor: string[] = [];
    
    // 基于活动类型
    if (activityType === 'meal') {
      suitableFor.push('美食爱好者', '所有年龄段');
    } else if (activityType === 'cultural') {
      suitableFor.push('文化爱好者', '学生群体', '成年人');
    } else if (activityType === 'entertainment') {
      suitableFor.push('家庭出游', '年轻人', '朋友聚会');
    } else {
      suitableFor.push('所有游客', '摄影爱好者');
    }
    
    // 基于难度等级
    const difficulty = this.assessDifficultyLevel(activity, activityType);
    if (difficulty === 'easy') {
      suitableFor.push('老人儿童');
    } else if (difficulty === 'challenging') {
      suitableFor.push('体力较好者');
    }
    
    return [...new Set(suitableFor)]; // 去重
  }

  /**
   * 🗺️ 生成周边景点
   */
  private static generateNearbyAttractions(activity: any, destination: string): string[] {
    // 这里可以基于地理位置API或预设数据生成
    // 目前使用模拟数据
    const nearbyAttractions: string[] = [];
    
    if (activity.location?.name?.includes('市中心')) {
      nearbyAttractions.push(`${destination}中央广场`, `${destination}购物街`, `${destination}历史博物馆`);
    } else if (activity.location?.name?.includes('老城')) {
      nearbyAttractions.push(`${destination}古城墙`, `${destination}传统市场`, `${destination}文化街`);
    } else {
      nearbyAttractions.push(`${destination}地标建筑`, `${destination}特色街区`);
    }
    
    return nearbyAttractions.slice(0, 3);
  }

  /**
   * 🏛️ 生成文化洞察
   */
  private static generateCulturalInsights(activity: any, destination: string): string[] {
    const insights: string[] = [];
    
    // 基于目的地获取文化洞察
    const destinationLower = destination.toLowerCase();
    let culturalData = null;
    
    if (destinationLower.includes('日本') || destinationLower.includes('japan') || 
        destinationLower.includes('东京') || destinationLower.includes('osaka')) {
      culturalData = this.CULTURAL_INSIGHTS.japan;
    } else if (destinationLower.includes('中国') || destinationLower.includes('china') ||
               destinationLower.includes('北京') || destinationLower.includes('上海')) {
      culturalData = this.CULTURAL_INSIGHTS.china;
    }
    
    if (culturalData) {
      insights.push(...culturalData.general.slice(0, 1));
      
      if (activity.type === 'meal' && culturalData.dining) {
        insights.push(...culturalData.dining.slice(0, 1));
      } else if (activity.type === 'attraction' && culturalData.attractions) {
        insights.push(...culturalData.attractions.slice(0, 1));
      } else if (activity.name?.includes('寺') && culturalData.temples) {
        insights.push(...culturalData.temples.slice(0, 1));
      }
    }
    
    // 通用文化洞察
    if (insights.length === 0) {
      insights.push('了解当地文化背景能增加旅行体验的深度');
    }
    
    return insights.slice(0, 2);
  }

  /**
   * 📸 生成拍照点
   */
  private static generatePhotoSpots(activity: any, activityType: string): string[] {
    const photoSpots: string[] = [];
    
    if (activityType === 'attraction') {
      photoSpots.push('主要建筑正面', '标志性角度', '全景视角');
    } else if (activityType === 'meal') {
      photoSpots.push('精美菜品', '餐厅环境', '用餐氛围');
    } else if (activityType === 'cultural') {
      photoSpots.push('展品特写', '建筑细节', '艺术装置');
    } else {
      photoSpots.push('特色场景', '标志元素');
    }
    
    return photoSpots.slice(0, 3);
  }

  /**
   * 💰 生成预算提示
   */
  private static generateBudgetTips(activity: any, travelStyle?: string): string[] {
    const tips: string[] = [];
    
    if (activity.cost?.amount === 0) {
      tips.push('完全免费是预算友好的选择');
    } else if (activity.cost?.amount > 0) {
      if (travelStyle === 'budget') {
        tips.push('可寻找优惠券或团购价格');
        tips.push('避开高峰时段可能有折扣');
      } else if (travelStyle === 'luxury') {
        tips.push('可考虑VIP服务或私人导览');
      } else {
        tips.push('价格合理性价比不错');
      }
    }
    
    if (activity.type === 'meal') {
      tips.push('可选择套餐更加经济实惠');
    }
    
    return tips.slice(0, 2);
  }

  /**
   * ♿ 生成无障碍信息
   */
  private static generateAccessibilityInfo(activity: any, activityType: string): string[] {
    const info: string[] = [];
    
    // 基于活动类型提供通用无障碍信息
    if (activityType === 'attraction') {
      info.push('建议提前了解无障碍设施情况');
      if (activity.location?.name?.includes('公园') || activity.location?.name?.includes('广场')) {
        info.push('户外场所通常有较好的无障碍通道');
      }
    } else if (activityType === 'meal') {
      info.push('大多数餐厅提供无障碍座位');
    } else if (activityType === 'cultural') {
      info.push('博物馆通常配备完善的无障碍设施');
    }
    
    return info.slice(0, 2);
  }

  /**
   * 🌤️ 生成天气考虑
   */
  private static generateWeatherConsiderations(activity: any, activityType: string): string[] {
    const considerations: string[] = [];
    
    // 基于活动类型
    if (activity.location?.name?.includes('户外') || 
        activity.location?.name?.includes('公园') ||
        activity.location?.name?.includes('广场')) {
      considerations.push('户外活动建议关注天气预报');
      considerations.push('雨天可能影响体验建议备选方案');
    } else if (activityType === 'meal' || activityType === 'cultural' || activityType === 'shopping') {
      considerations.push('室内活动不受天气影响');
    }
    
    // 季节性考虑
    considerations.push('不同季节可能有不同的最佳体验时间');
    
    return considerations.slice(0, 2);
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 🍽️ 推断菜系类型
   */
  private static inferCuisineType(activity: any): string {
    const name = (activity.name || '').toLowerCase();
    
    if (name.includes('拉面') || name.includes('ramen')) return '日式拉面';
    if (name.includes('寿司') || name.includes('sushi')) return '日式料理';
    if (name.includes('火锅')) return '中式火锅';
    if (name.includes('烧烤') || name.includes('bbq')) return '烧烤料理';
    if (name.includes('咖啡') || name.includes('coffee')) return '咖啡文化';
    
    return '当地美食';
  }

  /**
   * ✨ 添加特色信息
   */
  private static addSpecialFeatures(description: string, activity: any, activityType: string): string {
    let enhanced = description;
    
    // 基于评分添加信息
    if (activity.details?.rating && activity.details.rating > 4.5) {
      enhanced += '，深受游客好评推荐';
    }
    
    // 基于费用添加信息
    if (activity.cost?.amount === 0) {
      enhanced += '，更是免费开放的良心景点';
    }
    
    // 基于预订要求添加信息
    if (activity.details?.bookingRequired) {
      enhanced += '，因其热门程度建议提前预订';
    }
    
    return enhanced;
  }

  /**
   * 📊 计算质量评分
   */
  private static calculateQualityScore(content: any): number {
    let score = 0.5; // 基础分数
    
    // 描述质量
    if (content.enhancedDescription && content.enhancedDescription.length > 50) {
      score += 0.15;
    }
    
    // 亮点数量和质量
    if (content.highlights && content.highlights.length >= 3) {
      score += 0.1;
    }
    
    // 实用提示
    if (content.practicalTips && content.practicalTips.length >= 2) {
      score += 0.1;
    }
    
    // 推荐建议
    if (content.recommendations && content.recommendations.length >= 2) {
      score += 0.1;
    }
    
    // 文化洞察
    if (content.culturalInsights && content.culturalInsights.length > 0) {
      score += 0.05;
    }
    
    return Math.min(1.0, score);
  }

  /**
   * 📝 生成增强原因
   */
  private static generateEnhancementReason(activity: any, qualityScore: number): string[] {
    const reasons: string[] = [];
    
    if (qualityScore > 0.8) {
      reasons.push('内容全面丰富，提供了详细的旅行指导');
    } else if (qualityScore > 0.6) {
      reasons.push('内容质量良好，包含实用信息');
    } else {
      reasons.push('基础内容增强，提供必要信息');
    }
    
    if (!activity.description || activity.description.length < 30) {
      reasons.push('原始描述较简单，已进行详细补充');
    }
    
    reasons.push('添加了文化背景和实用建议');
    
    return reasons;
  }

  /**
   * 🔄 批量增强内容
   */
  static enhanceMultipleActivities(requests: ContentEnhancementRequest[]): EnhancedContent[] {
    console.log(`📝 开始批量增强${requests.length}个活动内容`);
    
    const results = requests.map(request => this.enhanceActivityContent(request));
    
    const averageQuality = results.reduce((sum, result) => sum + result.qualityScore, 0) / results.length;
    console.log(`📊 批量增强完成，平均质量评分: ${(averageQuality * 100).toFixed(1)}%`);
    
    return results;
  }
}