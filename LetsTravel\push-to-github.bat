@echo off
echo 🚀 Trekmate项目推送到GitHub脚本
echo =====================================

REM 请在下面替换YOUR_USERNAME为您的GitHub用户名
set GITHUB_USERNAME=CreaiTechnology
set REPO_NAME=trekmate

echo 📝 当前配置:
echo GitHub用户名: %GITHUB_USERNAME%
echo 仓库名称: %REPO_NAME%
echo.

if "%GITHUB_USERNAME%"=="YOUR_USERNAME" (
    echo ❌ 错误: 请先编辑此脚本，将YOUR_USERNAME替换为您的GitHub用户名
    echo.
    echo 编辑方法:
    echo 1. 打开 push-to-github.bat
    echo 2. 将第6行的 YOUR_USERNAME 替换为您的GitHub用户名
    echo 3. 保存文件后重新运行
    pause
    exit /b 1
)

echo 🔧 设置远程仓库地址...
git remote set-url origin https://github.com/%GITHUB_USERNAME%/%REPO_NAME%.git

echo 📋 检查远程仓库配置...
git remote -v

echo.
echo 🚀 开始推送到GitHub...
echo.

REM 推送开发分支
echo 📤 推送 trekmate-4.0-development 分支...
git push -u origin trekmate-4.0-development

if %ERRORLEVEL% EQU 0 (
    echo ✅ trekmate-4.0-development 分支推送成功！
) else (
    echo ❌ trekmate-4.0-development 分支推送失败
    echo.
    echo 可能的原因:
    echo 1. GitHub用户名不正确
    echo 2. 仓库名称不正确  
    echo 3. 没有推送权限
    echo 4. 网络连接问题
    echo.
    echo 请检查以上问题后重试
    pause
    exit /b 1
)

echo.
echo 📤 推送 master 分支...
git checkout master
git push -u origin master

if %ERRORLEVEL% EQU 0 (
    echo ✅ master 分支推送成功！
) else (
    echo ⚠️ master 分支推送失败（可能不存在，这是正常的）
)

echo.
echo 🎉 推送完成！
echo.
echo 📊 推送总结:
echo • 仓库地址: https://github.com/%GITHUB_USERNAME%/%REPO_NAME%
echo • 主要分支: trekmate-4.0-development
echo • 提交数量: 查看git log获取详细信息
echo.
echo 🌐 访问您的GitHub仓库:
echo https://github.com/%GITHUB_USERNAME%/%REPO_NAME%
echo.

pause
