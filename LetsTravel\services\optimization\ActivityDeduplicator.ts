/**
 * 🔄 活动去重服务
 * 
 * 智能检测和移除重复或相似的活动，确保行程的多样性和质量
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 接口定义 =====

export interface Activity {
  id?: string;
  name: string;
  type: string;
  category?: string;
  location?: {
    name?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  duration?: number;
  cost?: number;
  description?: string;
  tags?: string[];
}

export interface DeduplicationResult {
  originalCount: number;
  deduplicatedCount: number;
  removedActivities: Activity[];
  keptActivities: Activity[];
  duplicateGroups: DuplicateGroup[];
}

export interface DuplicateGroup {
  representative: Activity;
  duplicates: Activity[];
  similarity: number;
  reason: string;
}

export interface DeduplicationOptions {
  nameSimilarityThreshold?: number;
  locationDistanceThreshold?: number; // 米
  typeSimilarityWeight?: number;
  preserveHighQuality?: boolean;
  maxActivitiesPerType?: number;
  enableSmartMerging?: boolean;
}

// ===== 活动去重服务 =====

export class ActivityDeduplicator {
  private static instance: ActivityDeduplicator;
  
  private readonly defaultOptions: DeduplicationOptions = {
    nameSimilarityThreshold: 0.8,
    locationDistanceThreshold: 500, // 500米内视为相同位置
    typeSimilarityWeight: 0.3,
    preserveHighQuality: true,
    maxActivitiesPerType: 3,
    enableSmartMerging: true
  };

  private constructor() {}

  /**
   * 🎯 获取单例实例
   */
  static getInstance(): ActivityDeduplicator {
    if (!ActivityDeduplicator.instance) {
      ActivityDeduplicator.instance = new ActivityDeduplicator();
    }
    return ActivityDeduplicator.instance;
  }

  /**
   * 🔄 主要去重方法
   */
  async deduplicateActivities(
    activities: Activity[],
    options: DeduplicationOptions = {}
  ): Promise<DeduplicationResult> {
    
    console.log(`🔄 开始活动去重，原始活动数量: ${activities.length}`);
    
    const mergedOptions = { ...this.defaultOptions, ...options };
    const duplicateGroups: DuplicateGroup[] = [];
    const processedActivities = [...activities];
    
    // Step 1: 检测重复活动组
    const groups = this.detectDuplicateGroups(processedActivities, mergedOptions);
    duplicateGroups.push(...groups);
    
    // Step 2: 从每组中选择最佳代表
    const keptActivities = this.selectBestRepresentatives(groups, mergedOptions);
    
    // Step 3: 添加非重复活动
    const nonDuplicateActivities = this.getNonDuplicateActivities(
      processedActivities, 
      groups
    );
    keptActivities.push(...nonDuplicateActivities);
    
    // Step 4: 按类型限制数量
    const finalActivities = this.limitActivitiesByType(keptActivities, mergedOptions);
    
    // Step 5: 生成结果报告
    const removedActivities = activities.filter(
      activity => !finalActivities.some(kept => this.isSameActivity(activity, kept))
    );
    
    const result: DeduplicationResult = {
      originalCount: activities.length,
      deduplicatedCount: finalActivities.length,
      removedActivities,
      keptActivities: finalActivities,
      duplicateGroups
    };
    
    console.log(`✅ 去重完成: ${activities.length} → ${finalActivities.length} (移除${removedActivities.length}个重复)`);
    
    return result;
  }

  /**
   * 🔍 检测重复活动组
   */
  private detectDuplicateGroups(
    activities: Activity[], 
    options: DeduplicationOptions
  ): DuplicateGroup[] {
    
    const groups: DuplicateGroup[] = [];
    const processed = new Set<number>();
    
    for (let i = 0; i < activities.length; i++) {
      if (processed.has(i)) continue;
      
      const currentActivity = activities[i];
      const duplicates: Activity[] = [];
      
      for (let j = i + 1; j < activities.length; j++) {
        if (processed.has(j)) continue;
        
        const otherActivity = activities[j];
        const similarity = this.calculateSimilarity(currentActivity, otherActivity, options);
        
        if (similarity >= options.nameSimilarityThreshold!) {
          duplicates.push(otherActivity);
          processed.add(j);
        }
      }
      
      if (duplicates.length > 0) {
        groups.push({
          representative: currentActivity,
          duplicates,
          similarity: duplicates.length > 0 ? 
            Math.max(...duplicates.map(d => this.calculateSimilarity(currentActivity, d, options))) : 0,
          reason: this.getDuplicationReason(currentActivity, duplicates[0])
        });
        processed.add(i);
      }
    }
    
    return groups;
  }

  /**
   * 📊 计算活动相似度
   */
  private calculateSimilarity(
    activity1: Activity, 
    activity2: Activity, 
    options: DeduplicationOptions
  ): number {
    
    let totalSimilarity = 0;
    let weightSum = 0;
    
    // 1. 名称相似度 (权重: 0.5)
    const nameSimilarity = this.calculateNameSimilarity(activity1.name, activity2.name);
    totalSimilarity += nameSimilarity * 0.5;
    weightSum += 0.5;
    
    // 2. 类型相似度 (权重: 0.3)
    const typeSimilarity = this.calculateTypeSimilarity(activity1, activity2);
    totalSimilarity += typeSimilarity * (options.typeSimilarityWeight || 0.3);
    weightSum += (options.typeSimilarityWeight || 0.3);
    
    // 3. 位置相似度 (权重: 0.2)
    if (activity1.location?.coordinates && activity2.location?.coordinates) {
      const locationSimilarity = this.calculateLocationSimilarity(
        activity1.location.coordinates,
        activity2.location.coordinates,
        options.locationDistanceThreshold || 500
      );
      totalSimilarity += locationSimilarity * 0.2;
      weightSum += 0.2;
    }
    
    return weightSum > 0 ? totalSimilarity / weightSum : 0;
  }

  /**
   * 📝 计算名称相似度
   */
  private calculateNameSimilarity(name1: string, name2: string): number {
    if (!name1 || !name2) return 0;
    
    const normalized1 = this.normalizeActivityName(name1);
    const normalized2 = this.normalizeActivityName(name2);
    
    // 完全匹配
    if (normalized1 === normalized2) return 1.0;
    
    // 包含关系
    if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
      return 0.9;
    }
    
    // 编辑距离相似度
    const editDistance = this.calculateEditDistance(normalized1, normalized2);
    const maxLength = Math.max(normalized1.length, normalized2.length);
    const similarity = 1 - (editDistance / maxLength);
    
    return Math.max(0, similarity);
  }

  /**
   * 🏷️ 计算类型相似度
   */
  private calculateTypeSimilarity(activity1: Activity, activity2: Activity): number {
    if (activity1.type === activity2.type) return 1.0;
    if (activity1.category === activity2.category) return 0.8;
    
    // 检查标签相似度
    if (activity1.tags && activity2.tags) {
      const commonTags = activity1.tags.filter(tag => activity2.tags!.includes(tag));
      const totalTags = new Set([...activity1.tags, ...activity2.tags]).size;
      return commonTags.length / totalTags;
    }
    
    return 0;
  }

  /**
   * 📍 计算位置相似度
   */
  private calculateLocationSimilarity(
    coord1: { lat: number; lng: number },
    coord2: { lat: number; lng: number },
    threshold: number
  ): number {
    
    const distance = this.calculateDistance(coord1, coord2);
    
    if (distance <= threshold) {
      return 1 - (distance / threshold); // 距离越近相似度越高
    }
    
    return 0;
  }

  /**
   * 🌍 计算两点间距离 (米)
   */
  private calculateDistance(
    coord1: { lat: number; lng: number },
    coord2: { lat: number; lng: number }
  ): number {
    
    const R = 6371000; // 地球半径 (米)
    const dLat = this.toRadians(coord2.lat - coord1.lat);
    const dLng = this.toRadians(coord2.lng - coord1.lng);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(coord1.lat)) * Math.cos(this.toRadians(coord2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c;
  }

  /**
   * 🔧 辅助方法
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private normalizeActivityName(name: string): string {
    return name.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 移除特殊字符，保留中文
      .replace(/\s+/g, ' ')
      .trim();
  }

  private calculateEditDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private selectBestRepresentatives(
    groups: DuplicateGroup[], 
    options: DeduplicationOptions
  ): Activity[] {
    
    return groups.map(group => {
      const allActivities = [group.representative, ...group.duplicates];
      
      if (options.preserveHighQuality) {
        // 选择质量最高的活动
        return allActivities.reduce((best, current) => {
          const bestScore = this.calculateQualityScore(best);
          const currentScore = this.calculateQualityScore(current);
          return currentScore > bestScore ? current : best;
        });
      }
      
      return group.representative;
    });
  }

  private calculateQualityScore(activity: Activity): number {
    let score = 0;
    
    // 有描述 +10分
    if (activity.description && activity.description.length > 10) score += 10;
    
    // 有坐标 +15分
    if (activity.location?.coordinates) score += 15;
    
    // 有费用信息 +5分
    if (activity.cost !== undefined) score += 5;
    
    // 有标签 +5分
    if (activity.tags && activity.tags.length > 0) score += 5;
    
    // 名称不是通用模板 +20分
    if (!this.isGenericName(activity.name)) score += 20;
    
    return score;
  }

  private isGenericName(name: string): boolean {
    const genericPatterns = [
      '传统.*体验',
      '当地.*体验',
      '文化.*体验',
      '特色.*体验',
      '.*传统.*',
      '.*体验.*'
    ];
    
    return genericPatterns.some(pattern => new RegExp(pattern).test(name));
  }

  private getNonDuplicateActivities(
    activities: Activity[], 
    groups: DuplicateGroup[]
  ): Activity[] {
    
    const duplicateActivityIds = new Set();
    
    groups.forEach(group => {
      duplicateActivityIds.add(this.getActivityId(group.representative));
      group.duplicates.forEach(dup => {
        duplicateActivityIds.add(this.getActivityId(dup));
      });
    });
    
    return activities.filter(activity => 
      !duplicateActivityIds.has(this.getActivityId(activity))
    );
  }

  private limitActivitiesByType(
    activities: Activity[], 
    options: DeduplicationOptions
  ): Activity[] {
    
    if (!options.maxActivitiesPerType) return activities;
    
    const typeGroups = new Map<string, Activity[]>();
    
    activities.forEach(activity => {
      const type = activity.type || 'other';
      if (!typeGroups.has(type)) {
        typeGroups.set(type, []);
      }
      typeGroups.get(type)!.push(activity);
    });
    
    const limitedActivities: Activity[] = [];
    
    typeGroups.forEach((typeActivities, type) => {
      const sorted = typeActivities.sort((a, b) => 
        this.calculateQualityScore(b) - this.calculateQualityScore(a)
      );
      
      limitedActivities.push(...sorted.slice(0, options.maxActivitiesPerType));
    });
    
    return limitedActivities;
  }

  private isSameActivity(activity1: Activity, activity2: Activity): boolean {
    return this.getActivityId(activity1) === this.getActivityId(activity2);
  }

  private getActivityId(activity: Activity): string {
    return activity.id || `${activity.name}_${activity.type}_${activity.location?.name || ''}`;
  }

  private getDuplicationReason(activity1: Activity, activity2: Activity): string {
    if (activity1.name === activity2.name) return '名称完全相同';
    if (activity1.type === activity2.type && activity1.location?.name === activity2.location?.name) {
      return '类型和位置相同';
    }
    return '高度相似';
  }
}

export default ActivityDeduplicator;
