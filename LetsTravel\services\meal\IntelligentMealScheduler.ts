/**
 * 🍽️ 智能用餐调度器
 * 
 * 解决以下问题：
 * 1. 一天只有一餐或没有用餐安排
 * 2. 用餐时间与活动时间冲突
 * 3. 缺乏合理的早中晚餐分布
 * 4. 用餐地点与活动地点不协调
 * 
 * <AUTHOR> Think System
 * @version 2.0.0
 * @created 2025-01-30
 */

export interface MealSchedulingRequest {
  activities: any[];
  destination: string;
  totalDays: number;
  userPreferences?: {
    dietaryRestrictions?: string[];
    cuisinePreferences?: string[];
    budgetLevel?: 'budget' | 'moderate' | 'premium';
    mealImportance?: 'low' | 'medium' | 'high';
  };
  accommodationInfo?: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
    hasBreakfast?: boolean;
  };
}

export interface IntelligentMealActivity {
  id: string;
  name: string;
  name_zh: string;
  type: 'meal';
  category: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  description: string;
  description_zh: string;
  location: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
  };
  timing: {
    startTime: string;
    endTime: string;
    duration: number;
    day: number;
    date: string;
  };
  startTime: string;
  endTime: string;
  duration: number;
  cost: {
    amount: number;
    currency: string;
    priceLevel: 'budget' | 'moderate' | 'expensive' | 'premium';
  };
  details: {
    cuisineType: string;
    mealType: string;
    rating?: number;
    highlights: string[];
    dietaryInfo: string[];
    bookingRequired: boolean;
    tips: string[];
  };
  metadata: {
    source: string;
    confidence: number;
    qualityScore: number;
    lastUpdated: string;
    schedulingReason: string;
    generatedBy: 'intelligent_meal_scheduler';
  };
}

export interface MealSchedulingResult {
  success: boolean;
  meals: IntelligentMealActivity[];
  summary: {
    totalMeals: number;
    mealsByType: Record<string, number>;
    totalCost: number;
    averageMealsPerDay: number;
    coverageByDay: Record<number, { breakfast: boolean; lunch: boolean; dinner: boolean }>;
  };
  qualityMetrics: {
    completeness: number;    // 用餐完整性 0-1
    timing: number;         // 时间合理性 0-1
    variety: number;        // 用餐多样性 0-1
    locationOptimization: number; // 地理位置优化 0-1
  };
  warnings: string[];
  recommendations: string[];
}

/**
 * 智能用餐调度器
 * 
 * 核心特性：
 * 1. 完整覆盖：确保每天都有合理的早中晚餐安排
 * 2. 时间优化：基于活动时间智能安排用餐时间
 * 3. 地点协调：选择与活动地点相近的用餐地点
 * 4. 多样化：提供不同类型和风格的用餐体验
 */
export class IntelligentMealScheduler {
  
  // 用餐时间配置
  private static readonly MEAL_TIME_CONFIG = {
    breakfast: {
      idealStart: 8 * 60,    // 08:00
      idealEnd: 10 * 60,     // 10:00
      duration: 45,          // 45分钟
      flexibility: 60,       // 1小时灵活性
      priority: 7
    },
    lunch: {
      idealStart: 12 * 60,   // 12:00
      idealEnd: 14 * 60,     // 14:00
      duration: 60,          // 60分钟
      flexibility: 90,       // 1.5小时灵活性
      priority: 8
    },
    dinner: {
      idealStart: 18 * 60,   // 18:00
      idealEnd: 20 * 60,     // 20:00
      duration: 75,          // 75分钟
      flexibility: 120,      // 2小时灵活性
      priority: 9
    },
    snack: {
      idealStart: 15 * 60,   // 15:00
      idealEnd: 17 * 60,     // 17:00
      duration: 20,          // 20分钟
      flexibility: 180,      // 3小时灵活性
      priority: 4
    }
  };

  // 用餐类型配置
  private static readonly MEAL_TYPE_CONFIG = {
    breakfast: {
      keywords: ['早餐', '早点', '粥', '包子', '豆浆', '咖啡', 'breakfast', 'cafe'],
      avgCost: 25,
      cuisineTypes: ['中式早餐', '西式早餐', '咖啡厅', '茶餐厅']
    },
    lunch: {
      keywords: ['午餐', '中餐', '快餐', '简餐', 'lunch', 'restaurant'],
      avgCost: 60,
      cuisineTypes: ['中餐', '西餐', '日料', '韩料', '东南亚菜']
    },
    dinner: {
      keywords: ['晚餐', '正餐', '火锅', '烧烤', 'dinner', 'fine dining'],
      avgCost: 120,
      cuisineTypes: ['精品中餐', '西式正餐', '特色料理', '当地美食']
    },
    snack: {
      keywords: ['小食', '点心', '甜品', '茶点', 'snack', 'dessert'],
      avgCost: 30,
      cuisineTypes: ['甜品', '茶点', '小食', '咖啡']
    }
  };

  /**
   * 🍽️ 生成智能用餐计划
   */
  static scheduleDailyMeals(request: MealSchedulingRequest): MealSchedulingResult {
    console.log('🍽️ 开始生成智能用餐计划');
    console.log(`📊 输入: ${request.activities.length}个活动, ${request.totalDays}天`);

    try {
      // 1. 按天分组活动
      const activitiesByDay = this.groupActivitiesByDay(request.activities);
      
      // 2. 为每天生成用餐安排
      const allMeals: IntelligentMealActivity[] = [];
      const coverageByDay: Record<number, { breakfast: boolean; lunch: boolean; dinner: boolean }> = {};
      
      for (let day = 1; day <= request.totalDays; day++) {
        const dayActivities = activitiesByDay[day] || [];
        const dayMeals = this.scheduleDayMeals(
          dayActivities,
          day,
          request
        );
        
        allMeals.push(...dayMeals);
        coverageByDay[day] = this.analyzeDayMealCoverage(dayMeals);
        
        console.log(`📅 Day ${day}: 安排${dayMeals.length}餐，早餐${coverageByDay[day].breakfast ? '✓' : '✗'} 午餐${coverageByDay[day].lunch ? '✓' : '✗'} 晚餐${coverageByDay[day].dinner ? '✓' : '✗'}`);
      }
      
      // 3. 计算总结和指标
      const summary = this.generateMealSummary(allMeals, coverageByDay);
      const qualityMetrics = this.calculateMealQuality(allMeals, request);
      
      // 4. 生成建议和警告
      const { warnings, recommendations } = this.generateMealInsights(
        allMeals,
        coverageByDay,
        qualityMetrics
      );
      
      const result: MealSchedulingResult = {
        success: true,
        meals: allMeals,
        summary,
        qualityMetrics,
        warnings,
        recommendations
      };
      
      console.log('✅ 智能用餐计划生成完成');
      console.log(`📈 质量指标: 完整性${(qualityMetrics.completeness * 100).toFixed(1)}%, 时间合理性${(qualityMetrics.timing * 100).toFixed(1)}%`);
      
      return result;
      
    } catch (error) {
      console.error('❌ 用餐计划生成失败:', error);
      
      return {
        success: false,
        meals: [],
        summary: {
          totalMeals: 0,
          mealsByType: {},
          totalCost: 0,
          averageMealsPerDay: 0,
          coverageByDay: {}
        },
        qualityMetrics: {
          completeness: 0,
          timing: 0,
          variety: 0,
          locationOptimization: 0
        },
        warnings: [`用餐计划生成失败: ${error instanceof Error ? error.message : '未知错误'}`],
        recommendations: ['请检查活动数据的完整性，或联系技术支持']
      };
    }
  }

  /**
   * 📅 按天分组活动
   */
  private static groupActivitiesByDay(activities: any[]): Record<number, any[]> {
    const groups: Record<number, any[]> = {};
    
    activities.forEach(activity => {
      const day = activity.timing?.day || activity.day || 1;
      if (!groups[day]) groups[day] = [];
      groups[day].push(activity);
    });
    
    // 按时间排序每天的活动
    Object.keys(groups).forEach(dayStr => {
      const day = parseInt(dayStr);
      groups[day].sort((a, b) => {
        const timeA = this.parseTime(a.timing?.startTime || a.startTime || '09:00');
        const timeB = this.parseTime(b.timing?.startTime || b.startTime || '09:00');
        return timeA - timeB;
      });
    });
    
    return groups;
  }

  /**
   * 🍽️ 为单天安排用餐
   */
  private static scheduleDayMeals(
    dayActivities: any[],
    day: number,
    request: MealSchedulingRequest
  ): IntelligentMealActivity[] {
    console.log(`🍽️ 为Day ${day}安排用餐，活动数量: ${dayActivities.length}`);
    
    const meals: IntelligentMealActivity[] = [];
    const mealImportance = request.userPreferences?.mealImportance || 'medium';
    
    // 分析当天的时间空隙
    const timeSlots = this.analyzeAvailableTimeSlots(dayActivities);
    
    // 1. 安排早餐
    const breakfastSlot = this.findOptimalMealSlot(timeSlots, 'breakfast', day);
    if (breakfastSlot && !this.hasAccommodationBreakfast(request.accommodationInfo)) {
      const breakfast = this.createMealActivity(
        'breakfast',
        breakfastSlot,
        day,
        request,
        dayActivities
      );
      if (breakfast) {
        meals.push(breakfast);
        console.log(`🌅 安排早餐: ${breakfast.name} (${breakfast.startTime}-${breakfast.endTime})`);
      }
    }
    
    // 2. 安排午餐
    const lunchSlot = this.findOptimalMealSlot(timeSlots, 'lunch', day);
    if (lunchSlot) {
      const lunch = this.createMealActivity(
        'lunch',
        lunchSlot,
        day,
        request,
        dayActivities
      );
      if (lunch) {
        meals.push(lunch);
        console.log(`🍜 安排午餐: ${lunch.name} (${lunch.startTime}-${lunch.endTime})`);
      }
    }
    
    // 3. 安排晚餐
    const dinnerSlot = this.findOptimalMealSlot(timeSlots, 'dinner', day);
    if (dinnerSlot) {
      const dinner = this.createMealActivity(
        'dinner',
        dinnerSlot,
        day,
        request,
        dayActivities
      );
      if (dinner) {
        meals.push(dinner);
        console.log(`🍽️ 安排晚餐: ${dinner.name} (${dinner.startTime}-${dinner.endTime})`);
      }
    }
    
    // 4. 根据重要性安排额外用餐
    if (mealImportance === 'high' && dayActivities.length > 4) {
      const snackSlot = this.findOptimalMealSlot(timeSlots, 'snack', day);
      if (snackSlot) {
        const snack = this.createMealActivity(
          'snack',
          snackSlot,
          day,
          request,
          dayActivities
        );
        if (snack) {
          meals.push(snack);
          console.log(`🍰 安排茶点: ${snack.name} (${snack.startTime}-${snack.endTime})`);
        }
      }
    }
    
    return meals;
  }

  /**
   * ⏰ 分析可用时间段
   */
  private static analyzeAvailableTimeSlots(activities: any[]): Array<{start: number; end: number}> {
    if (activities.length === 0) {
      return [{ start: 8 * 60, end: 22 * 60 }]; // 全天可用
    }
    
    const timeSlots: Array<{start: number; end: number}> = [];
    
    // 排序活动
    const sortedActivities = [...activities].sort((a, b) => {
      const timeA = this.parseTime(a.timing?.startTime || a.startTime || '09:00');
      const timeB = this.parseTime(b.timing?.startTime || b.startTime || '09:00');
      return timeA - timeB;
    });
    
    // 第一个活动前的时间
    const firstActivityStart = this.parseTime(sortedActivities[0].timing?.startTime || sortedActivities[0].startTime || '09:00');
    if (firstActivityStart > 8 * 60) {
      timeSlots.push({ start: 8 * 60, end: firstActivityStart });
    }
    
    // 活动间的时间空隙
    for (let i = 0; i < sortedActivities.length - 1; i++) {
      const currentEnd = this.parseTime(sortedActivities[i].timing?.endTime || sortedActivities[i].endTime || '10:00');
      const nextStart = this.parseTime(sortedActivities[i + 1].timing?.startTime || sortedActivities[i + 1].startTime || '11:00');
      
      if (nextStart - currentEnd >= 30) { // 至少30分钟空隙
        timeSlots.push({ start: currentEnd, end: nextStart });
      }
    }
    
    // 最后一个活动后的时间
    const lastActivityEnd = this.parseTime(sortedActivities[sortedActivities.length - 1].timing?.endTime || sortedActivities[sortedActivities.length - 1].endTime || '18:00');
    if (lastActivityEnd < 22 * 60) {
      timeSlots.push({ start: lastActivityEnd, end: 22 * 60 });
    }
    
    return timeSlots;
  }

  /**
   * 🎯 找到最优用餐时间段
   */
  private static findOptimalMealSlot(
    availableSlots: Array<{start: number; end: number}>,
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack',
    day: number
  ): {start: number; end: number} | null {
    const mealConfig = this.MEAL_TIME_CONFIG[mealType];
    let bestSlot: {start: number; end: number} | null = null;
    let bestScore = -1;
    
    for (const slot of availableSlots) {
      // 检查时间段是否足够长
      if (slot.end - slot.start < mealConfig.duration) {
        continue;
      }
      
      // 计算最佳开始时间
      const idealStart = mealConfig.idealStart;
      const latestStart = slot.end - mealConfig.duration;
      const possibleStart = Math.max(slot.start, Math.min(idealStart, latestStart));
      
      // 计算适合度分数
      let score = 0;
      
      // 1. 时间匹配度
      const timeDiff = Math.abs(possibleStart - idealStart);
      if (timeDiff <= 30) {
        score += 50; // 理想时间
      } else if (timeDiff <= 60) {
        score += 30; // 可接受时间
      } else if (timeDiff <= mealConfig.flexibility) {
        score += 10; // 勉强可接受
      } else {
        continue; // 时间不合适
      }
      
      // 2. 时间段长度奖励
      const slotDuration = slot.end - slot.start;
      if (slotDuration >= mealConfig.duration * 1.5) {
        score += 20; // 充裕时间
      } else if (slotDuration >= mealConfig.duration * 1.2) {
        score += 10; // 合适时间
      }
      
      // 3. 避免过早或过晚
      if (possibleStart < 7 * 60 || possibleStart > 21 * 60) {
        score -= 20; // 时间过早或过晚
      }
      
      if (score > bestScore) {
        bestScore = score;
        bestSlot = {
          start: possibleStart,
          end: possibleStart + mealConfig.duration
        };
      }
    }
    
    return bestSlot;
  }

  /**
   * 🍽️ 创建用餐活动
   */
  private static createMealActivity(
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack',
    timeSlot: {start: number; end: number},
    day: number,
    request: MealSchedulingRequest,
    dayActivities: any[]
  ): IntelligentMealActivity | null {
    const mealConfig = this.MEAL_TYPE_CONFIG[mealType];
    const budgetLevel = request.userPreferences?.budgetLevel || 'moderate';
    
    // 选择最近的活动地点作为参考
    const referenceLocation = this.findNearestActivityLocation(timeSlot.start, dayActivities);
    
    // 生成用餐地点
    const mealLocation = this.generateMealLocation(
      mealType,
      referenceLocation,
      request.destination
    );
    
    // 计算费用
    const baseCost = mealConfig.avgCost;
    const budgetMultiplier = budgetLevel === 'budget' ? 0.7 : budgetLevel === 'premium' ? 1.5 : 1.0;
    const finalCost = Math.round(baseCost * budgetMultiplier);
    
    const startTime = this.minutesToTime(timeSlot.start);
    const endTime = this.minutesToTime(timeSlot.end);
    const duration = timeSlot.end - timeSlot.start;
    
    // 选择菜系类型
    const cuisineType = this.selectCuisineType(mealType, request.userPreferences?.cuisinePreferences);
    
    return {
      id: `meal_${mealType}_day${day}_${Date.now()}`,
      name: this.generateMealName(mealType, cuisineType, request.destination),
      name_zh: this.generateMealNameZh(mealType, cuisineType, request.destination),
      type: 'meal',
      category: mealType,
      description: this.generateMealDescription(mealType, cuisineType, mealLocation.name),
      description_zh: this.generateMealDescriptionZh(mealType, cuisineType, mealLocation.name),
      location: mealLocation,
      timing: {
        startTime,
        endTime,
        duration,
        day,
        date: new Date().toISOString().split('T')[0] // 临时日期，将被外部系统更新
      },
      startTime,
      endTime,
      duration,
      cost: {
        amount: finalCost,
        currency: 'CNY',
        priceLevel: this.determinePriceLevel(finalCost)
      },
      details: {
        cuisineType,
        mealType: this.getMealTypeDescription(mealType),
        rating: 4.2 + Math.random() * 0.6, // 4.2-4.8 随机评分
        highlights: this.generateMealHighlights(mealType, cuisineType),
        dietaryInfo: this.generateDietaryInfo(request.userPreferences?.dietaryRestrictions),
        bookingRequired: mealType === 'dinner' && budgetLevel === 'premium',
        tips: this.generateMealTips(mealType, cuisineType)
      },
      metadata: {
        source: 'intelligent_scheduler',
        confidence: 0.85,
        qualityScore: 0.8,
        lastUpdated: new Date().toISOString(),
        schedulingReason: `Day ${day} ${this.getMealTimeDescription(mealType)}时段智能安排`,
        generatedBy: 'intelligent_meal_scheduler'
      }
    };
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 🏨 检查住宿是否包含早餐
   */
  private static hasAccommodationBreakfast(accommodationInfo?: any): boolean {
    return accommodationInfo?.hasBreakfast === true;
  }

  /**
   * 📍 找到最近的活动地点
   */
  private static findNearestActivityLocation(mealTime: number, activities: any[]): any {
    if (activities.length === 0) {
      return { name: '市中心', address: '市中心区域', coordinates: { lat: 0, lng: 0 } };
    }
    
    // 找到时间上最接近的活动
    let nearestActivity = activities[0];
    let minTimeDiff = Infinity;
    
    activities.forEach(activity => {
      const activityStart = this.parseTime(activity.timing?.startTime || activity.startTime || '09:00');
      const activityEnd = this.parseTime(activity.timing?.endTime || activity.endTime || '10:00');
      const activityMidTime = (activityStart + activityEnd) / 2;
      
      const timeDiff = Math.abs(mealTime - activityMidTime);
      if (timeDiff < minTimeDiff) {
        minTimeDiff = timeDiff;
        nearestActivity = activity;
      }
    });
    
    return nearestActivity.location || { name: '附近区域', address: '附近区域', coordinates: { lat: 0, lng: 0 } };
  }

  /**
   * 🍽️ 生成用餐地点
   */
  private static generateMealLocation(
    mealType: string,
    referenceLocation: any,
    destination: string
  ): { name: string; address: string; coordinates?: { lat: number; lng: number } } {
    const mealTypeNames = {
      breakfast: ['咖啡厅', '早餐店', '茶餐厅', '面包房'],
      lunch: ['餐厅', '食堂', '快餐店', '小食店'],
      dinner: ['餐厅', '酒楼', '特色餐厅', '美食广场'],
      snack: ['咖啡厅', '甜品店', '茶室', '小食店']
    };
    
    const typeOptions = mealTypeNames[mealType] || mealTypeNames.lunch;
    const selectedType = typeOptions[Math.floor(Math.random() * typeOptions.length)];
    
    const locationName = `${referenceLocation.name}附近${selectedType}`;
    const locationAddress = `${referenceLocation.address}附近`;
    
    return {
      name: locationName,
      address: locationAddress,
      coordinates: referenceLocation.coordinates
    };
  }

  /**
   * 🍜 选择菜系类型
   */
  private static selectCuisineType(mealType: string, preferences?: string[]): string {
    const mealConfig = this.MEAL_TYPE_CONFIG[mealType];
    const availableCuisines = mealConfig.cuisineTypes;
    
    // 如果有偏好，优先选择偏好菜系
    if (preferences && preferences.length > 0) {
      for (const preference of preferences) {
        const matchingCuisine = availableCuisines.find(cuisine => 
          cuisine.toLowerCase().includes(preference.toLowerCase()) ||
          preference.toLowerCase().includes(cuisine.toLowerCase())
        );
        if (matchingCuisine) {
          return matchingCuisine;
        }
      }
    }
    
    // 随机选择
    return availableCuisines[Math.floor(Math.random() * availableCuisines.length)];
  }

  /**
   * 🏷️ 生成用餐名称
   */
  private static generateMealName(mealType: string, cuisineType: string, destination: string): string {
    const templates = {
      breakfast: [`${destination}特色早餐`, `${cuisineType}早餐体验`, `当地早餐时光`],
      lunch: [`${cuisineType}午餐`, `${destination}风味午餐`, `当地特色午餐`],
      dinner: [`${cuisineType}晚餐`, `${destination}美食晚餐`, `精品晚餐体验`],
      snack: [`${destination}特色茶点`, `${cuisineType}下午茶`, `当地小食体验`]
    };
    
    const options = templates[mealType] || templates.lunch;
    return options[Math.floor(Math.random() * options.length)];
  }

  /**
   * 🏷️ 生成中文用餐名称
   */
  private static generateMealNameZh(mealType: string, cuisineType: string, destination: string): string {
    return this.generateMealName(mealType, cuisineType, destination); // 目前与英文版本相同
  }

  /**
   * 📝 生成用餐描述
   */
  private static generateMealDescription(mealType: string, cuisineType: string, locationName: string): string {
    const descriptions = {
      breakfast: `在${locationName}享用${cuisineType}，开启美好的一天`,
      lunch: `在${locationName}品尝${cuisineType}，补充旅途能量`,
      dinner: `在${locationName}享用${cuisineType}，结束充实的一天`,
      snack: `在${locationName}品尝${cuisineType}，享受悠闲时光`
    };
    
    return descriptions[mealType] || descriptions.lunch;
  }

  /**
   * 📝 生成中文用餐描述
   */
  private static generateMealDescriptionZh(mealType: string, cuisineType: string, locationName: string): string {
    return this.generateMealDescription(mealType, cuisineType, locationName); // 目前与英文版本相同
  }

  /**
   * ⭐ 生成用餐亮点
   */
  private static generateMealHighlights(mealType: string, cuisineType: string): string[] {
    const highlights = {
      breakfast: ['新鲜制作', '营养丰富', '当地特色', '环境舒适'],
      lunch: ['口味正宗', '分量充足', '性价比高', '服务周到'],
      dinner: ['精心烹饪', '氛围优雅', '食材新鲜', '体验独特'],
      snack: ['制作精美', '口感丰富', '环境雅致', '适合休憩']
    };
    
    const baseHighlights = highlights[mealType] || highlights.lunch;
    const cuisineHighlight = `正宗${cuisineType}`;
    
    return [cuisineHighlight, ...baseHighlights.slice(0, 2)];
  }

  /**
   * 🥗 生成饮食信息
   */
  private static generateDietaryInfo(restrictions?: string[]): string[] {
    const info: string[] = ['提供多种选择'];
    
    if (restrictions && restrictions.length > 0) {
      restrictions.forEach(restriction => {
        switch (restriction.toLowerCase()) {
          case 'vegetarian':
            info.push('提供素食选项');
            break;
          case 'halal':
            info.push('提供清真食品');
            break;
          case 'gluten-free':
            info.push('提供无麸质选项');
            break;
          default:
            info.push(`考虑${restriction}需求`);
        }
      });
    }
    
    return info;
  }

  /**
   * 💡 生成用餐提示
   */
  private static generateMealTips(mealType: string, cuisineType: string): string[] {
    const tips = {
      breakfast: ['建议提前10分钟到达', '可询问当日特色推荐'],
      lunch: ['高峰时段可能需要等位', '可提前电话咨询'],
      dinner: ['建议提前预订', '注意营业时间'],
      snack: ['适合休息时享用', '可外带']
    };
    
    const baseTips = tips[mealType] || tips.lunch;
    const cuisineTip = `推荐尝试${cuisineType}特色菜品`;
    
    return [cuisineTip, ...baseTips];
  }

  /**
   * 🕐 时间字符串转分钟
   */
  private static parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * 🕐 分钟转时间字符串
   */
  private static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * 💰 确定价格等级
   */
  private static determinePriceLevel(cost: number): 'budget' | 'moderate' | 'expensive' | 'premium' {
    if (cost <= 30) return 'budget';
    if (cost <= 80) return 'moderate';
    if (cost <= 150) return 'expensive';
    return 'premium';
  }

  /**
   * 🏷️ 获取用餐类型描述
   */
  private static getMealTypeDescription(mealType: string): string {
    const descriptions = {
      breakfast: '早餐',
      lunch: '午餐',
      dinner: '晚餐',
      snack: '茶点'
    };
    
    return descriptions[mealType] || '用餐';
  }

  /**
   * ⏰ 获取用餐时间描述
   */
  private static getMealTimeDescription(mealType: string): string {
    const descriptions = {
      breakfast: '早晨',
      lunch: '中午',
      dinner: '傍晚',
      snack: '下午'
    };
    
    return descriptions[mealType] || '用餐';
  }

  /**
   * 📊 分析单天用餐覆盖情况
   */
  private static analyzeDayMealCoverage(meals: IntelligentMealActivity[]): { breakfast: boolean; lunch: boolean; dinner: boolean } {
    return {
      breakfast: meals.some(meal => meal.category === 'breakfast'),
      lunch: meals.some(meal => meal.category === 'lunch'),
      dinner: meals.some(meal => meal.category === 'dinner')
    };
  }

  /**
   * 📊 生成用餐总结
   */
  private static generateMealSummary(
    meals: IntelligentMealActivity[],
    coverageByDay: Record<number, { breakfast: boolean; lunch: boolean; dinner: boolean }>
  ) {
    const totalMeals = meals.length;
    const mealsByType = meals.reduce((count, meal) => {
      count[meal.category] = (count[meal.category] || 0) + 1;
      return count;
    }, {} as Record<string, number>);
    
    const totalCost = meals.reduce((sum, meal) => sum + meal.cost.amount, 0);
    const totalDays = Object.keys(coverageByDay).length;
    const averageMealsPerDay = totalDays > 0 ? totalMeals / totalDays : 0;
    
    return {
      totalMeals,
      mealsByType,
      totalCost: Math.round(totalCost * 100) / 100,
      averageMealsPerDay: Math.round(averageMealsPerDay * 10) / 10,
      coverageByDay
    };
  }

  /**
   * 📈 计算用餐质量指标
   */
  private static calculateMealQuality(
    meals: IntelligentMealActivity[],
    request: MealSchedulingRequest
  ) {
    // 完整性：基于每天的用餐覆盖率
    const totalDays = request.totalDays;
    const activitiesByDay = this.groupActivitiesByDay(request.activities);
    
    let completenessScore = 0;
    for (let day = 1; day <= totalDays; day++) {
      const dayMeals = meals.filter(meal => meal.timing.day === day);
      const coverage = this.analyzeDayMealCoverage(dayMeals);
      
      let dayScore = 0;
      if (coverage.breakfast) dayScore += 0.3;
      if (coverage.lunch) dayScore += 0.4;
      if (coverage.dinner) dayScore += 0.3;
      
      completenessScore += dayScore;
    }
    const completeness = totalDays > 0 ? completenessScore / totalDays : 0;
    
    // 时间合理性：检查用餐时间是否合理
    let timingScore = 0;
    meals.forEach(meal => {
      const mealTime = this.parseTime(meal.startTime);
      const idealTime = this.MEAL_TIME_CONFIG[meal.category].idealStart;
      const timeDiff = Math.abs(mealTime - idealTime);
      
      if (timeDiff <= 30) {
        timingScore += 1; // 理想时间
      } else if (timeDiff <= 60) {
        timingScore += 0.8; // 可接受时间
      } else if (timeDiff <= 120) {
        timingScore += 0.5; // 勉强可接受
      } else {
        timingScore += 0.2; // 时间不理想
      }
    });
    const timing = meals.length > 0 ? timingScore / meals.length : 0;
    
    // 多样性：检查菜系和用餐类型的多样性
    const cuisineTypes = new Set(meals.map(meal => meal.details.cuisineType));
    const mealTypes = new Set(meals.map(meal => meal.category));
    
    const cuisineVariety = Math.min(1, cuisineTypes.size / 4); // 最多4种菜系
    const typeVariety = Math.min(1, mealTypes.size / 3); // 最多3种用餐类型
    const variety = (cuisineVariety + typeVariety) / 2;
    
    // 地理位置优化：检查用餐地点与活动地点的协调性
    let locationScore = 0.8; // 基础分数，因为我们已经基于活动地点选择用餐地点
    
    return {
      completeness,
      timing,
      variety,
      locationOptimization: locationScore
    };
  }

  /**
   * 💡 生成用餐洞察和建议
   */
  private static generateMealInsights(
    meals: IntelligentMealActivity[],
    coverageByDay: Record<number, { breakfast: boolean; lunch: boolean; dinner: boolean }>,
    qualityMetrics: any
  ) {
    const warnings: string[] = [];
    const recommendations: string[] = [];
    
    // 检查每天的用餐覆盖
    Object.entries(coverageByDay).forEach(([dayStr, coverage]) => {
      const day = parseInt(dayStr);
      const missing: string[] = [];
      
      if (!coverage.breakfast) missing.push('早餐');
      if (!coverage.lunch) missing.push('午餐');
      if (!coverage.dinner) missing.push('晚餐');
      
      if (missing.length > 0) {
        warnings.push(`Day ${day}缺少${missing.join('、')}安排`);
      }
      
      if (missing.length === 1) {
        recommendations.push(`Day ${day}可以考虑增加${missing[0]}安排以完善用餐体验`);
      }
    });
    
    // 检查质量指标
    if (qualityMetrics.completeness < 0.7) {
      warnings.push('整体用餐完整性不足，建议增加必要的用餐安排');
    }
    
    if (qualityMetrics.timing < 0.6) {
      recommendations.push('部分用餐时间安排可以进一步优化，建议调整到更合适的时间段');
    }
    
    if (qualityMetrics.variety < 0.5) {
      recommendations.push('用餐类型相对单一，建议增加不同菜系和用餐体验的多样性');
    }
    
    // 基于用餐分布的建议
    const totalMeals = meals.length;
    const totalDays = Object.keys(coverageByDay).length;
    const averageMealsPerDay = totalDays > 0 ? totalMeals / totalDays : 0;
    
    if (averageMealsPerDay < 2) {
      recommendations.push('每天用餐次数较少，建议增加用餐安排以保证旅途体力');
    } else if (averageMealsPerDay > 4) {
      recommendations.push('用餐安排较为丰富，适合美食爱好者的深度体验');
    }
    
    return { warnings, recommendations };
  }
}