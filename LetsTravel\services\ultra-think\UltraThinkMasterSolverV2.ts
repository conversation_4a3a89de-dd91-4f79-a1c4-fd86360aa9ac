/**
 * 🎯 Ultra Think Master Solver V2.0
 * 重构版 - 基于新数据结构和真实地点的终极解决方案
 */

import { JSONDataTransfer, JourneyPayload, DayPlan, Activity, Meal, Transportation, Accommodation } from '../../types/JourneyDataTypes';
import { TokyoPlacesDatabase, TOKYO_ATTRACTIONS, TOKYO_RESTAURANTS, RealPlace, RealRestaurant } from '../../data/TokyoRealPlaces';
import { JourneyJSONValidator } from '../../validators/JourneyJSONSchema';
import { LLMDataPipeline } from '../llm/LLMDataPipeline';
import { ContentQualityEnhancer } from '../content/ContentQualityEnhancer';
// import { UnifiedTimelineBuilder, DayTimeline } from '../timeline/UnifiedTimelineBuilder'; // 🔧 已移除，使用简化方案

export interface MasterSolverV2Request {
  destination: string;
  duration: number;
  budget: number;
  currency: string;
  travelers: number;
  startDate: Date;
  preferences: {
    travelStyle: string[];
    accommodation: string[];
    transport: string[];
    interests?: string[];
  };
}

export interface MasterSolverV2Response {
  success: boolean;
  journeyData: JSONDataTransfer;
  qualityScore: number;
  executionTime: number;
  issues: string[];
  fixes: string[];
}

export class UltraThinkMasterSolverV2 {
  private static llmPipeline = new LLMDataPipeline();
  // private static timelineBuilder = new UnifiedTimelineBuilder(); // 🔧 已移除，使用简化方案

  /**
   * 🎯 主生成方法 - V2.0重构版 + LLM增强
   */
  static async generateJourney(request: MasterSolverV2Request): Promise<MasterSolverV2Response> {
    const startTime = Date.now();
    console.log('🎯🎯🎯 Ultra Think Master Solver V2.0 启动 🎯🎯🎯');
    console.log('🚀 使用真实地点数据和新架构生成高质量行程');
    console.log('📍 请求参数:', request);
    
    const issues: string[] = [];
    const fixes: string[] = [];
    
    try {
      // 1. 🏨 生成住宿计划
      const accommodation = await this.generateAccommodationPlan(request);
      fixes.push('✅ 生成住宿计划');
      
      // 2. 🎯 生成核心活动（使用真实地点）
      const coreActivities = await this.generateRealActivities(request);
      fixes.push(`✅ 生成${coreActivities.length}个真实景点活动`);
      
      // 3. 🍽️ 生成餐饮计划
      const mealPlans = await this.generateMealPlans(request, coreActivities);
      fixes.push(`✅ 生成${mealPlans.length}个餐饮安排`);
      
      // 4. 🚗 生成交通方案
      const transportation = await this.generateTransportation(coreActivities, request);
      fixes.push(`✅ 生成${transportation.length}个交通安排`);
      
      // 5. 📅 智能组装日程
      const dayPlans = await this.assembleDayPlans({
        activities: coreActivities,
        meals: mealPlans,
        transportation,
        duration: request.duration,
        startDate: request.startDate
      });
      fixes.push(`✅ 组装${dayPlans.length}天完整日程`);
      
      // 6. 💰 计算预算分解
      const budget = await this.calculateDetailedBudget(dayPlans, accommodation, request);
      fixes.push('✅ 计算详细预算分解');
      
      // 7. 📊 构建完整Journey数据
      const journeyPayload: JourneyPayload = {
        journey: {
          id: `journey_${request.destination}_${Date.now()}`,
          title: `${request.destination}${request.duration}天精彩之旅`,
          destination: request.destination,
          duration: request.duration,
          startDate: request.startDate.toISOString(),
          endDate: new Date(request.startDate.getTime() + (request.duration - 1) * 24 * 60 * 60 * 1000).toISOString(),
          travelers: request.travelers,
          preferences: {
            ...request.preferences,
            interests: request.preferences.interests || []
          }
        },
        dayPlans,
        accommodation,
        budget,
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          version: '2.0',
          generationSource: 'ultra_think_master_solver_v2',
          qualityScore: 0.95,
          validationStatus: 'valid',
          tags: ['real_places', 'high_quality', 'winter_optimized']
        }
      };
      
      // 8. 📊 格式化为JSON传输格式
      const journeyJSON: JSONDataTransfer = {
        version: '2.0',
        timestamp: new Date().toISOString(),
        source: 'master_solver_v2',
        target: 'frontend',
        dataType: 'journey',
        payload: journeyPayload,
        metadata: {
          processingTime: Date.now() - startTime,
          qualityScore: 0.95,
          validationStatus: 'valid',
          llmEnhanced: false,
          autoFixed: false
        }
      };
      
      // 9. 🤖 LLM数据处理流水线增强
      console.log('🤖 启动LLM数据处理流水线');
      const pipelineResult = await this.llmPipeline.processJourneyData(journeyJSON);

      if (pipelineResult.success) {
        console.log('✅ LLM流水线处理成功');
        fixes.push(...pipelineResult.processingSteps.filter(step => step.success).map(step => `✅ ${step.description}`));

        if (pipelineResult.warnings.length > 0) {
          console.warn('⚠️ LLM流水线警告:', pipelineResult.warnings);
          issues.push(...pipelineResult.warnings);
        }
      } else {
        console.warn('⚠️ LLM流水线处理失败:', pipelineResult.errors);
        issues.push(...pipelineResult.errors);
      }

      const finalJourneyData = pipelineResult.success ? pipelineResult.finalData : journeyJSON;
      const finalQualityScore = pipelineResult.qualityScore;

      // 10. 🕐 构建统一时间线 (已禁用，使用简化方案)
      console.log('🕐 构建统一时间线');
      // const dayTimelines = this.buildDayTimelines(finalJourneyData);

      // 将时间线数据添加到最终结果中 (已禁用)
      finalJourneyData.metadata = {
        ...finalJourneyData.metadata,
        // dayTimelines: dayTimelines,
        // timelineGenerated: false, // 🔧 移除不兼容的属性
        // simplifiedMode: true
      };

      const executionTime = Date.now() - startTime;
      console.log(`🎉 Master Solver V2.0 + LLM + Timeline完成: ${executionTime}ms, 质量分${finalQualityScore.toFixed(2)}`);

      // 生成处理报告
      const processingReport = this.llmPipeline.generateProcessingReport(pipelineResult);
      console.log(processingReport);

      return {
        success: true,
        journeyData: finalJourneyData,
        qualityScore: finalQualityScore,
        executionTime,
        issues,
        fixes
      };
      
    } catch (error) {
      console.error('❌ Master Solver V2.0 执行失败:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      issues.push(`执行失败: ${errorMessage}`);
      
      return {
        success: false,
        journeyData: this.generateFallbackJourney(request),
        qualityScore: 0.5,
        executionTime: Date.now() - startTime,
        issues,
        fixes
      };
    }
  }
  
  /**
   * 🏨 生成住宿计划
   */
  private static async generateAccommodationPlan(request: MasterSolverV2Request): Promise<Accommodation[]> {
    console.log('🏨 生成住宿计划');
    
    // 根据预算选择住宿类型
    const budgetPerNight = request.budget * 0.4 / request.duration; // 40%预算用于住宿
    
    let accommodationType: 'luxury' | 'mid' | 'budget';
    if (budgetPerNight > 500) accommodationType = 'luxury';
    else if (budgetPerNight > 200) accommodationType = 'mid';
    else accommodationType = 'budget';
    
    const accommodation: Accommodation = {
      id: `hotel_${request.destination}_${Date.now()}`,
      name: `${request.destination}${accommodationType === 'luxury' ? '豪华酒店' : accommodationType === 'mid' ? '精品酒店' : '商务酒店'}`,
      type: 'hotel',
      checkIn: request.startDate.toISOString(),
      checkOut: new Date(request.startDate.getTime() + request.duration * 24 * 60 * 60 * 1000).toISOString(),
      nights: request.duration,
      location: {
        name: `${request.destination}市中心`,
        district: '市中心',
        coordinates: { lat: 35.6762, lng: 139.6503 }
      },
      rating: accommodationType === 'luxury' ? 5 : accommodationType === 'mid' ? 4 : 3,
      costPerNight: {
        amount: budgetPerNight,
        currency: request.currency,
        priceLevel: accommodationType
      },
      totalCost: {
        amount: budgetPerNight * request.duration,
        currency: request.currency,
        priceLevel: accommodationType
      },
      amenities: accommodationType === 'luxury' 
        ? ['WiFi', '早餐', '健身房', 'spa', '礼宾服务', '商务中心']
        : accommodationType === 'mid'
        ? ['WiFi', '早餐', '健身房', '24小时前台']
        : ['WiFi', '24小时前台'],
      services: ['清洁服务', '行李寄存', '旅游咨询'],
      description: `位于${request.destination}市中心的${accommodationType === 'luxury' ? '豪华' : accommodationType === 'mid' ? '精品' : '舒适'}酒店，交通便利，设施完善`,
      highlights: ['地理位置优越', '服务优质', '设施现代化']
    };
    
    return [accommodation];
  }
  
  /**
   * 🎯 生成真实活动（使用真实地点数据）
   */
  private static async generateRealActivities(request: MasterSolverV2Request): Promise<Activity[]> {
    console.log('🎯 生成真实景点活动 - 集成智能优化组件');
    
    const activities: Activity[] = [];
    const activitiesPerDay = Math.ceil((request.duration * 4) / request.duration); // 每天约4个活动
    
    // 获取真实景点数据
    const realPlaces = TokyoPlacesDatabase.getRandomAttractions(request.duration * activitiesPerDay);
    
    // 🔧 Step 1: 生成基础活动数据
    console.log('📋 生成基础活动数据');
    const baseActivities = realPlaces.map((place, index) => {
      const dayNumber = Math.floor(index / activitiesPerDay) + 1;
      const timeSlot = this.calculateTimeSlot(index % activitiesPerDay);
      
      return {
        id: place.id,
        name: place.name,
        nameEn: place.nameEn,
        type: this.mapPlaceTypeToActivityType(place.type),
        category: 'sightseeing',
        timing: {
          startTime: timeSlot.start,
          endTime: timeSlot.end,
          duration: place.duration,
          timeRange: `${timeSlot.start}-${timeSlot.end}`,
          day: dayNumber
        },
        location: {
          name: place.name,
          district: place.district,
          coordinates: place.coordinates
        },
        cost: {
          amount: place.cost.amount,
          currency: place.cost.currency,
          priceLevel: place.cost.priceLevel
        },
        description: place.description,
        highlights: place.highlights,
        tips: [place.culturalTips],
        expandedContent: {
          whatToExpected: place.description,
          bestPhotoSpots: place.photoSpots,
          culturalTips: [place.culturalTips],
          seasonalTips: [place.winterSpecial],
          nearbyAttractions: place.nearbyPlaces || []
        },
        metadata: {
          source: 'tokyo_real_places_db',
          qualityScore: 0.95,
          isRealPlace: true,
          lastUpdated: new Date().toISOString(),
          difficultyLevel: 'easy' as const,
          suitableFor: ['all_ages']
        }
      };
    });

    try {
      // 🔧 Step 2: 集成智能活动分类器
      console.log('🏷️ 集成智能活动分类器');
      const { IntelligentActivityClassifier } = require('../../services/classification/IntelligentActivityClassifier');
      
      const classifiedActivities = baseActivities.map(activity => {
        const classificationRequest = {
          id: activity.id,
          name: activity.name,
          name_zh: activity.name,
          type: activity.type,
          category: activity.category,
          description: activity.description,
          location: activity.location
        };
        
        const classification = IntelligentActivityClassifier.classifyActivity(classificationRequest);
        
        return {
          ...activity,
          type: classification.primaryType,
          category: classification.subCategory,
          metadata: {
            ...activity.metadata,
            classification: {
              primaryType: classification.primaryType,
              subCategory: classification.subCategory,
              confidence: classification.confidence,
              iconType: classification.iconType,
              reasoning: classification.reasoning,
              tags: classification.tags
            }
          }
        };
      });
      
      console.log(`✅ 活动分类完成: ${classifiedActivities.length}个活动已重新分类`);

      // 🔧 Step 3: 集成统一时间分配引擎
      console.log('⏰ 集成统一时间分配引擎');
      const { UnifiedTimeAllocationEngine } = require('../../services/time/UnifiedTimeAllocationEngine');
      
      // 转换为时间分配引擎所需的格式
      const timeRequirements = classifiedActivities.map(activity => ({
        id: activity.id,
        name: activity.name,
        type: activity.type,
        category: activity.category,
        location: activity.location,
        priority: this.calculateActivityPriority(activity),
        preferenceScore: this.calculatePreferenceScore(activity, request.preferences),
        isPreferenceMatch: this.isPreferenceMatch(activity, request.preferences),
        duration: activity.timing.duration || 90,
        flexibilityLevel: this.determineFlexibilityLevel(activity),
        optimalTimeSlots: this.getOptimalTimeSlots(activity),
        weatherSensitive: this.isWeatherSensitive(activity),
        bookingRequired: activity.cost.amount > 0,
        cost: activity.cost.amount || 0
      }));
      
      // 使用统一时间分配引擎
      const allocationResult = UnifiedTimeAllocationEngine.allocateIntelligentTimes(
        timeRequirements,
        request.duration,
        request.startDate,
        {
          pacePreference: 'moderate',
          mealImportance: 'medium',
          preferredStartTime: 9 * 60, // 09:00
          preferredEndTime: 20 * 60   // 20:00
        }
      );
      
      if (allocationResult.success) {
        console.log(`✅ 时间分配成功: 质量指标 - 一致性${(allocationResult.qualityMetrics.timeConsistency * 100).toFixed(1)}%, 流畅性${(allocationResult.qualityMetrics.logicalFlow * 100).toFixed(1)}%`);
        
        // 将分配结果应用到活动
        allocationResult.allocations.forEach((dayAllocation: any) => {
          dayAllocation.activities.forEach((allocatedActivity: any) => {
            const originalActivity = classifiedActivities.find(a => a.id === allocatedActivity.id);
            if (originalActivity) {
              // 更新时间信息
              originalActivity.timing = {
                ...originalActivity.timing,
                startTime: allocatedActivity.timeSlot.startTime,
                endTime: allocatedActivity.timeSlot.endTime,
                duration: allocatedActivity.timeSlot.duration,
                day: allocatedActivity.day,
                timeRange: `${allocatedActivity.timeSlot.startTime}-${allocatedActivity.timeSlot.endTime}`
              };
              
              // 添加时间优化元数据 (移除不兼容的属性)
              originalActivity.metadata = {
                ...originalActivity.metadata,
                // timeOptimized: true,
                // timeConfidence: allocatedActivity.timeSlot.confidence,
                // schedulingReason: allocatedActivity.schedulingReason,
                // timeReasoning: allocatedActivity.timeSlot.reasoning
              };
              
              activities.push(originalActivity);
            }
          });
        });
        
        // 输出警告和建议
        if (allocationResult.warnings.length > 0) {
          console.warn('⚠️ 时间分配警告:', allocationResult.warnings);
        }
        
        if (allocationResult.recommendations.length > 0) {
          console.log('💡 时间分配建议:', allocationResult.recommendations);
        }
        
      } else {
        console.warn('⚠️ 时间分配失败，使用原始时间安排');
        activities.push(...classifiedActivities);
      }
      
    } catch (error) {
      console.error('❌ 智能组件集成失败:', error);
      console.log('🔄 使用基础活动数据作为降级方案');
      // 修复baseActivities的类型兼容性
      const compatibleActivities = baseActivities.map(activity => ({
        ...activity,
        category: this.mapStringToActivityCategory(activity.category)
      }));
      activities.push(...compatibleActivities);
    }
    
    console.log(`🎯 真实活动生成完成: ${activities.length}个活动，已集成智能优化`);

    // 🎨 Phase 4: 内容质量增强
    try {
      console.log('🎨 开始内容质量增强');

      // 为每个活动进行内容增强
      const enhancedActivities = activities.map((activity) => {
        try {
          // 使用简化的内容增强逻辑
          const enhancedDescription = this.generateEnhancedDescription(activity, request.destination);
          const highlights = this.generateActivityHighlights(activity);
          const tips = this.generatePracticalTips(activity);

          return {
            ...activity,
            description: enhancedDescription,
            highlights: highlights,
            tips: tips,
            metadata: {
              ...activity.metadata,
              contentEnhanced: true,
              qualityScore: 0.85,
              enhancementSource: 'master_solver_v2'
            }
          };
        } catch (error) {
          console.warn(`⚠️ 活动${activity.name}内容增强失败:`, error);
          return activity; // 返回原始活动
        }
      });

      console.log(`✅ 内容质量增强完成: ${enhancedActivities.length}个活动已增强`);
      return enhancedActivities;

    } catch (error) {
      console.error('❌ 内容质量增强失败:', error);
      console.log('🔄 使用原始活动数据');
      return activities;
    }
  }

  /**
   * 🌸 获取当前季节
   */
  private static getCurrentSeason(): string {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  /**
   * 🔄 映射字符串到ActivityCategory类型
   */
  private static mapStringToActivityCategory(category: string): 'sightseeing' | 'cultural' | 'nature' | 'entertainment' | 'shopping' | 'experience' {
    const categoryMapping: Record<string, 'sightseeing' | 'cultural' | 'nature' | 'entertainment' | 'shopping' | 'experience'> = {
      'attraction': 'sightseeing',
      'cultural': 'cultural',
      'cultural_experience': 'cultural',
      'cultural_site': 'cultural',
      'nature': 'nature',
      'entertainment': 'entertainment',
      'shopping': 'shopping',
      'experience': 'experience',
      'food_tour': 'experience',
      'meal': 'experience',
      'restaurant': 'experience'
    };

    return categoryMapping[category] || 'sightseeing';
  }

  /**
   * � 生成增强描述
   */
  private static generateEnhancedDescription(activity: Activity, destination: string): string {
    const activityName = activity.name;
    const activityType = activity.type;

    // 基于活动类型生成生动描述
    const typeDescriptions = {
      'cultural': `探索${activityName}的深厚文化底蕴，感受${destination}独特的历史魅力。这里承载着丰富的文化传统，是了解当地文化的绝佳窗口。`,
      'cultural_experience': `沉浸在${activityName}的传统文化体验中，亲身感受${destination}的文化精髓。通过互动体验，深入了解当地的传统工艺和文化内涵。`,
      'cultural_site': `参观${activityName}这一重要的文化遗址，领略${destination}悠久的历史文化。精美的建筑和丰富的历史故事将带给您难忘的文化之旅。`,
      'restaurant': `在${activityName}品尝地道的${destination}美食，体验当地独特的饮食文化。精心准备的料理将为您的味蕾带来惊喜。`,
      'meal': `享受在${activityName}的美食时光，品味${destination}的特色料理。新鲜的食材和传统的烹饪技艺完美结合。`,
      'food_tour': `跟随${activityName}的美食之旅，深入探索${destination}的饮食文化。从街头小吃到精致料理，全方位体验当地美食。`,
      'transport': `便捷的交通方式，带您轻松前往${destination}的各个精彩目的地。`,
      'attraction': `游览${activityName}，欣赏${destination}的迷人风光和独特景观。`,
      'shopping': `在${activityName}享受购物乐趣，发现${destination}的特色商品和纪念品。`
    };

    return typeDescriptions[activityType as keyof typeof typeDescriptions] || `体验${activityName}的独特魅力，感受${destination}的精彩之处。这里将为您带来难忘的旅行回忆。`;
  }

  /**
   * 🌟 生成活动亮点
   */
  private static generateActivityHighlights(activity: Activity): string[] {
    const activityType = activity.type;
    const activityName = activity.name;

    const typeHighlights = {
      'cultural': [
        `${activityName}的历史文化价值`,
        '深度文化体验',
        '传统建筑艺术欣赏',
        '文化背景深入了解'
      ],
      'cultural_experience': [
        '亲身参与传统文化活动',
        '专业导师指导体验',
        '获得文化体验证书',
        '深入了解传统工艺'
      ],
      'restaurant': [
        '地道当地美食',
        '新鲜优质食材',
        '传统烹饪技艺',
        '舒适用餐环境'
      ],
      'meal': [
        '精心准备的料理',
        '营养均衡搭配',
        '当地特色风味',
        '优质服务体验'
      ],
      'attraction': [
        '绝佳拍照地点',
        '独特景观体验',
        '丰富历史故事',
        '便利交通位置'
      ]
    };

    return typeHighlights[activityType as keyof typeof typeHighlights] || [
      `${activityName}独特体验`,
      '难忘旅行回忆',
      '当地文化感受',
      '优质服务保障'
    ];
  }

  /**
   * 💡 生成实用建议
   */
  private static generatePracticalTips(activity: Activity): string[] {
    const activityType = activity.type;

    const typeTips = {
      'cultural': [
        '建议提前了解相关历史背景',
        '穿着得体，尊重当地文化',
        '可携带相机记录美好时刻',
        '注意参观时间和开放时间'
      ],
      'cultural_experience': [
        '建议穿着舒适的服装',
        '提前预约确保参与机会',
        '准备好学习的心态',
        '可以准备一些问题与导师交流'
      ],
      'restaurant': [
        '建议提前预订座位',
        '了解当地用餐礼仪',
        '可以询问服务员推荐菜品',
        '注意营业时间安排'
      ],
      'meal': [
        '按时用餐保持健康',
        '尝试当地特色料理',
        '注意饮食卫生',
        '适量用餐避免浪费'
      ],
      'transport': [
        '提前查看交通路线',
        '准备好交通费用',
        '注意安全出行',
        '预留充足的时间'
      ],
      'attraction': [
        '穿着舒适的鞋子',
        '携带必要的防晒用品',
        '注意景点开放时间',
        '保持环境整洁'
      ]
    };

    return typeTips[activityType as keyof typeof typeTips] || [
      '提前做好相关准备',
      '注意安全和礼仪',
      '保持开放的心态',
      '享受美好的体验'
    ];
  }

  /**
   * �🎯 计算活动优先级
   */
  private static calculateActivityPriority(activity: any): number {
    let priority = 5; // 基础优先级
    
    // 基于活动类型调整
    if (activity.type === 'attraction') priority += 2;
    if (activity.type === 'cultural') priority += 1;
    if (activity.type === 'meal') priority += 3;
    
    // 基于质量评分调整
    if (activity.metadata?.qualityScore > 0.9) priority += 1;
    
    // 基于费用调整（免费景点优先级稍高）
    if (activity.cost?.amount === 0) priority += 1;
    
    return Math.min(10, Math.max(1, priority));
  }

  /**
   * 📊 计算偏好匹配分数
   */
  private static calculatePreferenceScore(activity: any, preferences: any): number {
    let score = 0.5; // 基础分数
    
    // 基于旅行风格匹配
    if (preferences.travelStyle?.includes('文化探索') && activity.type === 'cultural') {
      score += 0.3;
    }
    if (preferences.travelStyle?.includes('美食体验') && activity.type === 'meal') {
      score += 0.3;
    }
    if (preferences.travelStyle?.includes('观光游览') && activity.type === 'attraction') {
      score += 0.2;
    }
    
    // 基于兴趣匹配
    if (preferences.interests?.includes('文化') && activity.category?.includes('文化')) {
      score += 0.2;
    }
    
    return Math.min(1.0, Math.max(0.0, score));
  }

  /**
   * 🎯 判断是否为偏好匹配
   */
  private static isPreferenceMatch(activity: any, preferences: any): boolean {
    const score = this.calculatePreferenceScore(activity, preferences);
    return score > 0.7;
  }

  /**
   * 🔄 确定活动灵活性级别
   */
  private static determineFlexibilityLevel(activity: any): 'rigid' | 'flexible' | 'very_flexible' {
    // 需要门票的活动通常较为严格
    if (activity.cost?.amount > 0) {
      return 'flexible';
    }
    
    // 文化场所有一定的时间要求
    if (activity.type === 'cultural') {
      return 'flexible';
    }
    
    // 其他活动相对灵活
    return 'very_flexible';
  }

  /**
   * ⏰ 获取活动的最佳时间段
   */
  private static getOptimalTimeSlots(activity: any): ('morning' | 'afternoon' | 'evening')[] {
    const type = activity.type;
    
    // 基于活动类型的最佳时间段
    const timeSlotMapping = {
      'attraction': ['morning' as const, 'afternoon' as const],
      'cultural': ['morning' as const, 'afternoon' as const],
      'meal': ['morning' as const, 'afternoon' as const, 'evening' as const],
      'shopping': ['afternoon' as const, 'evening' as const],
      'entertainment': ['afternoon' as const, 'evening' as const],
      'transport': ['morning' as const, 'afternoon' as const, 'evening' as const]
    };

    return timeSlotMapping[type as keyof typeof timeSlotMapping] || ['morning' as const, 'afternoon' as const];
  }

  /**
   * 🌤️ 判断活动是否对天气敏感
   */
  private static isWeatherSensitive(activity: any): boolean {
    const name = (activity.name || '').toLowerCase();
    const description = (activity.description || '').toLowerCase();
    
    // 户外活动关键词
    const outdoorKeywords = ['公园', '花园', '观景', '户外', 'park', 'garden', 'outdoor', 'view'];
    
    return outdoorKeywords.some(keyword => 
      name.includes(keyword) || description.includes(keyword)
    );
  }
  
  /**
   * ⏰ 计算时间段
   */
  private static calculateTimeSlot(index: number): { start: string; end: string } {
    const timeSlots = [
      { start: '09:00', end: '11:00' },
      { start: '11:30', end: '13:00' },
      { start: '14:00', end: '16:00' },
      { start: '16:30', end: '18:00' }
    ];
    
    const selectedSlot = timeSlots[index % timeSlots.length];
    return selectedSlot || { start: '09:00', end: '10:00' };
  }
  
  /**
   * 🗺️ 地点类型映射
   */
  private static mapPlaceTypeToActivityType(placeType: string): 'attraction' | 'experience' | 'entertainment' | 'shopping' | 'cultural' {
    const typeMap: Record<string, 'attraction' | 'experience' | 'entertainment' | 'shopping' | 'cultural'> = {
      'temple': 'cultural',
      'shrine': 'cultural',
      'landmark': 'attraction',
      'park': 'attraction',
      'market': 'experience',
      'museum': 'cultural',
      'tower': 'attraction'
    };
    
    return typeMap[placeType] || 'attraction';
  }
  
  /**
   * 🍽️ 生成餐饮计划
   */
  private static async generateMealPlans(request: MasterSolverV2Request, activities?: Activity[]): Promise<Meal[]> {
    console.log('🍽️ 生成餐饮计划 - 集成智能用餐调度器');

    try {
      // 🔧 集成智能用餐调度器
      const { IntelligentMealScheduler } = require('../../services/meal/IntelligentMealScheduler');
      
      // 构建用餐调度请求
      const mealRequest = {
        activities: activities || [],
        destination: request.destination,
        totalDays: request.duration,
        userPreferences: {
          dietaryRestrictions: [], // 可以从request.preferences中获取
          cuisinePreferences: this.extractCuisinePreferences(request.preferences),
          budgetLevel: this.mapBudgetLevel(request.budget),
          mealImportance: 'medium'
        },
        accommodationInfo: {
          name: `${request.destination}酒店`,
          address: `${request.destination}市中心`,
          coordinates: { lat: 35.6762, lng: 139.6503 },
          hasBreakfast: false // 假设酒店不包含早餐
        }
      };
      
      console.log('📤 调用智能用餐调度器');
      const mealResult = IntelligentMealScheduler.scheduleDailyMeals(mealRequest);
      
      if (mealResult.success) {
        console.log(`✅ 智能用餐调度成功: ${mealResult.meals.length}个用餐安排`);
        console.log(`📊 质量指标: 完整性${(mealResult.qualityMetrics.completeness * 100).toFixed(1)}%, 时间合理性${(mealResult.qualityMetrics.timing * 100).toFixed(1)}%`);
        
        // 转换为系统所需的Meal格式
        const systemMeals: Meal[] = mealResult.meals.map((meal: any) => ({
          id: meal.id,
          name: meal.name,
          type: meal.category as 'breakfast' | 'lunch' | 'dinner',
          cuisine: meal.details.cuisineType,
          time: meal.startTime,
          duration: meal.duration,
          location: {
            name: meal.location.name,
            district: this.extractDistrict(meal.location.address),
            coordinates: meal.location.coordinates || { lat: 35.6762, lng: 139.6503 }
          },
          cost: {
            amount: meal.cost.amount,
            currency: meal.cost.currency,
            priceLevel: meal.cost.priceLevel
          },
          description: meal.description,
          specialties: meal.details.highlights,
          priceRange: this.formatPriceRange(meal.cost.amount),
          details: {
            atmosphere: '舒适温馨',
            bestTime: meal.bestTimeToVisit,
            reservationNeeded: meal.details.bookingRequired,
            paymentMethods: ['现金', '信用卡'],
            tips: meal.practicalTips,
            culturalInsights: meal.culturalInsights,
            photoSpots: meal.photoSpots
          },
          metadata: {
            source: 'intelligent_meal_scheduler',
            confidence: 0.9,
            qualityScore: meal.qualityScore,
            schedulingReason: meal.metadata.schedulingReason,
            generatedBy: meal.metadata.generatedBy
          }
        }));
        
        // 输出警告和建议
        if (mealResult.warnings.length > 0) {
          console.warn('⚠️ 用餐调度警告:', mealResult.warnings);
        }
        
        if (mealResult.recommendations.length > 0) {
          console.log('💡 用餐调度建议:', mealResult.recommendations);
        }
        
        return systemMeals;
        
      } else {
        console.warn('⚠️ 智能用餐调度失败，使用降级方案');
        return this.generateFallbackMealPlans(request);
      }
      
    } catch (error) {
      console.error('❌ 智能用餐调度器集成失败:', error);
      console.log('🔄 使用降级用餐生成方案');
      return this.generateFallbackMealPlans(request);
    }
  }

  /**
   * 🍜 提取菜系偏好
   */
  private static extractCuisinePreferences(preferences: any): string[] {
    const cuisinePrefs: string[] = [];
    
    if (preferences.travelStyle?.includes('美食体验')) {
      cuisinePrefs.push('当地特色', '传统料理');
    }
    
    if (preferences.interests?.includes('文化')) {
      cuisinePrefs.push('传统菜系', '文化餐厅');
    }
    
    return cuisinePrefs.length > 0 ? cuisinePrefs : ['当地美食'];
  }

  /**
   * 💰 映射预算等级
   */
  private static mapBudgetLevel(budget: number): 'budget' | 'moderate' | 'premium' {
    if (budget < 3000) return 'budget';
    if (budget < 8000) return 'moderate';
    return 'premium';
  }

  /**
   * 📍 提取地区信息
   */
  private static extractDistrict(address: string): string {
    // 简单的地区提取逻辑，实际应该更复杂
    if (address.includes('涩谷')) return '涩谷区';
    if (address.includes('新宿')) return '新宿区';
    if (address.includes('浅草')) return '台东区';
    return '市中心';
  }

  /**
   * 💴 格式化价格范围
   */
  private static formatPriceRange(amount: number): string {
    if (amount === 0) return '免费';
    if (amount < 1000) return '¥500-1000';
    if (amount < 3000) return '¥1000-3000';
    if (amount < 5000) return '¥3000-5000';
    return '¥5000+';
  }

  /**
   * 🔄 降级用餐生成方案 - 使用真实餐厅名称
   */
  private static generateFallbackMealPlans(request: MasterSolverV2Request): Meal[] {
    console.log('🔄 使用降级用餐生成方案 - 确保真实餐厅名称');

    const meals: Meal[] = [];

    // 🍽️ 使用精选的真实餐厅数据
    const premiumRestaurants = [
      // 早餐专门店
      { name: '筑地外市场海鲜丼', type: 'breakfast', cuisine: '海鲜料理', time: '08:00', cost: 45, description: '新鲜海鲜丼，体验筑地市场的清晨活力' },
      { name: '银座木村家面包店', type: 'breakfast', cuisine: '西式早餐', time: '08:30', cost: 25, description: '百年老店的精致面包和咖啡，感受银座的优雅晨光' },
      { name: '浅草传统日式早餐', type: 'breakfast', cuisine: '传统日式', time: '08:00', cost: 35, description: '传统日式早餐套餐，在浅草古街感受江户风情' },

      // 午餐餐厅
      { name: '大和寿司本店', type: 'lunch', cuisine: '寿司料理', time: '12:00', cost: 120, description: '筑地市场内的百年寿司老店，品尝最新鲜的江户前寿司' },
      { name: '一兰拉面涩谷店', type: 'lunch', cuisine: '拉面料理', time: '12:30', cost: 35, description: '日本国民拉面品牌，体验独特的一人食拉面文化' },
      { name: '银座天妇罗近藤', type: 'lunch', cuisine: '天妇罗', time: '12:00', cost: 180, description: '米其林星级天妇罗专门店，品尝极致的江户前天妇罗' },

      // 晚餐餐厅
      { name: '浅草今半本店', type: 'dinner', cuisine: '和牛料理', time: '19:00', cost: 250, description: '百年老店品尝顶级和牛寿喜烧，体验传统日式料理文化' },
      { name: '银座久兵卫寿司', type: 'dinner', cuisine: '高级寿司', time: '19:30', cost: 400, description: '银座顶级寿司店，享受米其林星级的极致寿司体验' },
      { name: '新宿思出横丁烧鸟', type: 'dinner', cuisine: '居酒屋', time: '19:00', cost: 80, description: '体验东京传统居酒屋文化，品尝地道烧鸟和日本酒' }
    ];

    for (let day = 1; day <= request.duration; day++) {
      const mealTypes: ('breakfast' | 'lunch' | 'dinner')[] = ['breakfast', 'lunch', 'dinner'];

      mealTypes.forEach((mealType, index) => {
        // 为每种餐型选择合适的餐厅
        const availableRestaurants = premiumRestaurants.filter(r => r.type === mealType);
        const selectedRestaurant = availableRestaurants[(day - 1 + index) % availableRestaurants.length];

        if (selectedRestaurant) {
          const meal: Meal = {
            id: `premium_${mealType}_day${day}`,
            name: selectedRestaurant.name,
            type: mealType,
            cuisine: selectedRestaurant.cuisine,
            time: selectedRestaurant.time,
            duration: mealType === 'breakfast' ? 60 : mealType === 'lunch' ? 90 : 120,
            location: {
              name: selectedRestaurant.name,
              district: this.getRestaurantDistrict(selectedRestaurant.name),
              coordinates: this.getRestaurantCoordinates(selectedRestaurant.name)
            },
            cost: {
              amount: selectedRestaurant.cost,
              currency: 'JPY',
              priceLevel: selectedRestaurant.cost > 200 ? 'luxury' : selectedRestaurant.cost > 100 ? 'mid' : 'budget'
            },
            description: selectedRestaurant.description,
            specialties: this.getRestaurantSpecialties(selectedRestaurant.name),
            priceRange: `¥${selectedRestaurant.cost * 0.8}-${selectedRestaurant.cost * 1.2}`,
            details: {
              atmosphere: this.getRestaurantAtmosphere(selectedRestaurant.name),
              bestTime: selectedRestaurant.time,
              reservationNeeded: selectedRestaurant.cost > 150,
              paymentMethods: ['现金', '信用卡']
            }
          };

          meals.push(meal);
          console.log(`🍽️ 生成真实餐厅: ${meal.name} (${mealType})`);
        }
      });
    }

    return meals;
  }

  /**
   * 🏢 获取餐厅所在区域
   */
  private static getRestaurantDistrict(restaurantName: string): string {
    if (restaurantName.includes('筑地')) return '筑地';
    if (restaurantName.includes('银座')) return '银座';
    if (restaurantName.includes('浅草')) return '浅草';
    if (restaurantName.includes('涩谷')) return '涩谷';
    if (restaurantName.includes('新宿')) return '新宿';
    return '东京市中心';
  }

  /**
   * 📍 获取餐厅坐标
   */
  private static getRestaurantCoordinates(restaurantName: string): { lat: number; lng: number } {
    const coordinates = {
      '筑地': { lat: 35.6654, lng: 139.7707 },
      '银座': { lat: 35.6762, lng: 139.7653 },
      '浅草': { lat: 35.7148, lng: 139.7967 },
      '涩谷': { lat: 35.6598, lng: 139.7006 },
      '新宿': { lat: 35.6896, lng: 139.7006 }
    };

    const district = this.getRestaurantDistrict(restaurantName);
    return coordinates[district as keyof typeof coordinates] || coordinates['银座'];
  }

  /**
   * 🍽️ 获取餐厅特色菜品
   */
  private static getRestaurantSpecialties(restaurantName: string): string[] {
    if (restaurantName.includes('寿司')) return ['新鲜金枪鱼', '海胆寿司', '季节限定寿司'];
    if (restaurantName.includes('拉面')) return ['豚骨拉面', '叉烧肉', '溏心蛋'];
    if (restaurantName.includes('天妇罗')) return ['江户前天妇罗', '季节蔬菜天妇罗', '海鲜天妇罗'];
    if (restaurantName.includes('和牛')) return ['A5级和牛', '寿喜烧', '传统火锅'];
    if (restaurantName.includes('海鲜')) return ['新鲜刺身', '海鲜丼', '烤鱼'];
    if (restaurantName.includes('烧鸟')) return ['烤鸡串', '日本酒', '季节小菜'];
    return ['招牌料理', '季节特色', '传统口味'];
  }

  /**
   * 🏮 获取餐厅氛围
   */
  private static getRestaurantAtmosphere(restaurantName: string): string {
    if (restaurantName.includes('银座')) return '优雅精致';
    if (restaurantName.includes('浅草')) return '传统古朴';
    if (restaurantName.includes('新宿')) return '热闹活跃';
    if (restaurantName.includes('筑地')) return '新鲜活力';
    return '舒适温馨';
  }

  /**
   * 🚗 生成交通方案
   */
  private static async generateTransportation(activities: Activity[], request: MasterSolverV2Request): Promise<Transportation[]> {
    console.log('🚗 生成交通方案 - 集成增强交通生成器');

    try {
      // 🔧 集成增强交通生成器
      const { EnhancedTransportationGenerator } = require('../../services/transport/EnhancedTransportationGenerator');
      
      // 构建交通请求
      const transportRequest = {
        activities: activities,
        destination: request.destination,
        totalDays: request.duration,
        userPreferences: {
          preferredTransport: this.mapTransportPreference(request.preferences.transport),
          budget: request.budget,
          comfortLevel: 'standard'
        },
        accommodationInfo: {
          name: `${request.destination}酒店`,
          address: `${request.destination}市中心`,
          coordinates: { lat: 35.6762, lng: 139.6503 } // 默认坐标，实际应该从住宿数据获取
        }
      };
      
      console.log('📤 调用增强交通生成器');
      const transportPlan = EnhancedTransportationGenerator.generateCompleteTransportPlan(transportRequest);
      
      if (transportPlan.success) {
        console.log(`✅ 增强交通生成成功: ${transportPlan.transports.length}个交通活动`);
        console.log(`📊 质量指标: 完整性${(transportPlan.qualityMetrics.completeness * 100).toFixed(1)}%, 效率${(transportPlan.qualityMetrics.efficiency * 100).toFixed(1)}%`);
        
        // 转换为系统所需的Transportation格式
        const systemTransportation: Transportation[] = transportPlan.transports.map((transport: any) => ({
          id: transport.id,
          type: this.mapTransportCategory(transport.category),
          from: transport.from,
          to: transport.to,
          departureTime: transport.startTime,
          arrivalTime: transport.endTime,
          duration: transport.duration,
          cost: {
            amount: transport.cost.amount,
            currency: transport.cost.currency,
            priceLevel: transport.cost.priceLevel
          },
          details: {
            instructions: transport.details.instructions,
            walkingTime: transport.details.estimatedWaitTime || 0,
            transportMode: transport.details.transportMode,
            distance: transport.details.distance,
            tips: transport.details.tips
          },
          metadata: {
            source: 'enhanced_transport_generator',
            confidence: 0.9,
            qualityScore: 0.85,
            generatedBy: transport.metadata.generatedBy
          }
        }));
        
        // 输出警告和建议
        if (transportPlan.warnings.length > 0) {
          console.warn('⚠️ 交通生成警告:', transportPlan.warnings);
        }
        
        if (transportPlan.recommendations.length > 0) {
          console.log('💡 交通生成建议:', transportPlan.recommendations);
        }
        
        return systemTransportation;
        
      } else {
        console.warn('⚠️ 增强交通生成失败，使用降级方案');
        return this.generateFallbackTransportation(activities, request);
      }
      
    } catch (error) {
      console.error('❌ 增强交通生成器集成失败:', error);
      console.log('🔄 使用降级交通生成方案');
      return this.generateFallbackTransportation(activities, request);
    }
  }

  /**
   * 🚗 映射交通偏好
   */
  private static mapTransportPreference(transportPrefs: string[]): 'walking' | 'public' | 'taxi' | 'mixed' {
    if (transportPrefs.includes('步行') || transportPrefs.includes('walking')) return 'walking';
    if (transportPrefs.includes('公共交通') || transportPrefs.includes('public')) return 'public';
    if (transportPrefs.includes('出租车') || transportPrefs.includes('taxi')) return 'taxi';
    return 'mixed';
  }

  /**
   * 🚌 映射交通类别
   */
  private static mapTransportCategory(category: string): string {
    const categoryMapping = {
      'walking': 'walking',
      'subway': 'subway',
      'bus': 'bus',
      'taxi': 'taxi',
      'train': 'train',
      'flight': 'flight'
    };
    
    return categoryMapping[category as keyof typeof categoryMapping] || 'subway';
  }

  /**
   * 🔄 降级交通生成方案
   */
  private static generateFallbackTransportation(activities: Activity[], _request: MasterSolverV2Request): Transportation[] {
    console.log('🔄 使用降级交通生成方案');
    
    const transportation: Transportation[] = [];

    // 为每个活动之间生成交通
    for (let i = 0; i < activities.length - 1; i++) {
      const currentActivity = activities[i];
      const nextActivity = activities[i + 1];

      if (!currentActivity || !nextActivity) continue;

      // 计算交通时间和方式
      const distance = this.calculateDistance(
        currentActivity.location.coordinates!,
        nextActivity.location.coordinates!
      );

      const transport: Transportation = {
        id: `transport_fallback_${i}`,
        type: distance < 1 ? 'walking' : 'subway',
        from: currentActivity.location,
        to: nextActivity.location,
        departureTime: currentActivity.timing.endTime,
        arrivalTime: this.addMinutes(currentActivity.timing.endTime, distance < 1 ? 15 : 25),
        duration: distance < 1 ? 15 : 25,
        cost: {
          amount: distance < 1 ? 0 : 200,
          currency: 'JPY',
          priceLevel: 'budget'
        },
        details: {
          instructions: distance < 1
            ? [`步行前往${nextActivity.location.name}`, '约15分钟路程']
            : [`乘坐地铁前往${nextActivity.location.name}`, '约25分钟车程'],
          walkingTime: distance < 1 ? 15 : 5
          // transportMode: distance < 1 ? '步行' : '地铁', // 移除不兼容的属性
          // distance: distance,
          // tips: ['注意安全', '提前查看路线']
        }
        // metadata: { // 移除不兼容的属性
        //   source: 'fallback_generator',
        //   confidence: 0.6,
        //   qualityScore: 0.5,
        //   generatedBy: 'master_solver_v2_fallback'
        // }
      };

      transportation.push(transport);
    }

    return transportation;
  }

  /**
   * 📅 智能组装日程
   */
  private static async assembleDayPlans(data: {
    activities: Activity[];
    meals: Meal[];
    transportation: Transportation[];
    duration: number;
    startDate: Date;
  }): Promise<DayPlan[]> {
    console.log('📅 智能组装日程');

    const dayPlans: DayPlan[] = [];

    for (let day = 1; day <= data.duration; day++) {
      const dayDate = new Date(data.startDate.getTime() + (day - 1) * 24 * 60 * 60 * 1000);

      // 获取当天的活动、餐饮、交通
      const dayActivities = data.activities.filter((_, index) => Math.floor(index / 4) + 1 === day);
      const dayMeals = data.meals.filter(meal => meal.id.includes(`day${day}`));
      const dayTransportation = data.transportation.filter((_, index) => Math.floor(index / 3) + 1 === day);

      const dayPlan: DayPlan = {
        dayNumber: day,
        date: (dayDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]) as string,
        activities: dayActivities,
        meals: dayMeals,
        transportation: dayTransportation,
        summary: {
          totalActivities: dayActivities.length,
          totalCost: {
            amount: [...dayActivities, ...dayMeals, ...dayTransportation]
              .reduce((sum, item) => sum + item.cost.amount, 0),
            currency: 'JPY'
          },
          estimatedWalkingTime: 60,
          highlights: dayActivities.slice(0, 2).map(a => a.name),
          energyLevel: 'moderate',
          weatherAdvice: '12月东京天气较冷，建议穿着保暖衣物'
        },
        timeline: this.generateTimeline(dayActivities, dayMeals, dayTransportation)
      };

      dayPlans.push(dayPlan);
    }

    return dayPlans;
  }

  /**
   * 💰 计算详细预算
   */
  private static async calculateDetailedBudget(dayPlans: DayPlan[], accommodation: Accommodation[], request: MasterSolverV2Request) {
    console.log('💰 计算详细预算');

    const accommodationCost = accommodation.reduce((sum, acc) => sum + acc.totalCost.amount, 0);
    const activitiesCost = dayPlans.reduce((sum, day) =>
      sum + day.activities.reduce((daySum, activity) => daySum + activity.cost.amount, 0), 0);
    const foodCost = dayPlans.reduce((sum, day) =>
      sum + day.meals.reduce((daySum, meal) => daySum + meal.cost.amount, 0), 0);
    const transportationCost = dayPlans.reduce((sum, day) =>
      sum + day.transportation.reduce((daySum, transport) => daySum + transport.cost.amount, 0), 0);

    return {
      total: { amount: request.budget, currency: request.currency },
      breakdown: {
        accommodation: { amount: accommodationCost, currency: request.currency },
        activities: { amount: activitiesCost, currency: request.currency },
        food: { amount: foodCost, currency: request.currency },
        transportation: { amount: transportationCost, currency: request.currency },
        shopping: { amount: request.budget * 0.1, currency: request.currency },
        miscellaneous: { amount: request.budget * 0.05, currency: request.currency }
      },
      dailyAverage: { amount: request.budget / request.duration, currency: request.currency },
      recommendations: [
        '建议预留10%预算用于购物',
        '餐饮预算可根据实际情况调整',
        '交通费用已包含市内基本出行'
      ]
    };
  }

  /**
   * 🔄 生成降级Journey数据
   */
  private static generateFallbackJourney(request: MasterSolverV2Request): JSONDataTransfer {
    console.log('🔄 生成降级Journey数据');
    
    // 简化的降级数据
    const fallbackPayload: JourneyPayload = {
      journey: {
        id: `fallback_journey_${Date.now()}`,
        title: `${request.destination}${request.duration}天之旅`,
        destination: request.destination,
        duration: request.duration,
        startDate: request.startDate.toISOString(),
        endDate: new Date(request.startDate.getTime() + request.duration * 24 * 60 * 60 * 1000).toISOString(),
        travelers: request.travelers,
        preferences: {
          ...request.preferences,
          interests: request.preferences.interests || []
        }
      },
      dayPlans: [],
      accommodation: [],
      budget: {
        total: { amount: request.budget, currency: request.currency },
        breakdown: {
          accommodation: { amount: 0, currency: request.currency },
          activities: { amount: 0, currency: request.currency },
          food: { amount: 0, currency: request.currency },
          transportation: { amount: 0, currency: request.currency },
          shopping: { amount: 0, currency: request.currency },
          miscellaneous: { amount: 0, currency: request.currency }
        },
        dailyAverage: { amount: request.budget / request.duration, currency: request.currency },
        recommendations: ['预算分配建议']
      },
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: '2.0',
        generationSource: 'fallback',
        qualityScore: 0.5,
        validationStatus: 'fallback',
        tags: ['fallback']
      }
    };
    
    return {
      version: '2.0',
      timestamp: new Date().toISOString(),
      source: 'fallback',
      target: 'frontend',
      dataType: 'journey',
      payload: fallbackPayload,
      metadata: {
        processingTime: 0,
        qualityScore: 0.5,
        validationStatus: 'warning' as const
      }
    };
  }

  /**
   * 🕐 构建日程时间线 (已禁用，使用简化方案)
   */
  private static buildDayTimelines(_journeyData: JSONDataTransfer): any[] { // DayTimeline[]
    console.log('🕐 开始构建日程时间线');

    try {
      // 🔧 已禁用时间线构建，使用简化方案
      console.log('✅ 使用简化方案，跳过时间线构建');
      return [];

      // const dayTimelines: DayTimeline[] = [];
      // if (journeyData.payload.dayPlans) {
      //   journeyData.payload.dayPlans.forEach((dayPlan, index) => {
      //     const dayNumber = index + 1;
      //     const dayTimeline = this.timelineBuilder.buildDayTimeline(dayPlan, dayNumber);
      //     dayTimelines.push(dayTimeline);
      //     console.log(`✅ 第${dayNumber}天时间线: ${dayTimeline.timeline.length}个项目, 预算${dayTimeline.totalBudget.displayText}`);
      //   });
      // }
      // console.log(`✅ 时间线构建完成: ${dayTimelines.length}天`);
      // return dayTimelines;

    } catch (error) {
      console.error('❌ 时间线构建失败:', error);
      return [];
    }
  }

  /**
   * 🔧 辅助方法
   */

  private static parsePriceRange(priceRange: string): number {
    // 解析价格范围，返回平均值
    const matches = priceRange.match(/¥(\d+)-(\d+)/);
    if (matches && matches[1] && matches[2]) {
      const min = parseInt(matches[1], 10);
      const max = parseInt(matches[2], 10);
      return (min + max) / 2;
    }
    return 2000; // 默认值
  }

  private static determinePriceLevel(priceRange: string): 'budget' | 'mid' | 'luxury' {
    if (priceRange.includes('30000')) return 'luxury';
    if (priceRange.includes('5000')) return 'mid';
    return 'budget';
  }

  private static calculateDistance(coord1: { lat: number; lng: number }, coord2: { lat: number; lng: number }): number {
    // 简化的距离计算
    const latDiff = coord1.lat - coord2.lat;
    const lngDiff = coord1.lng - coord2.lng;
    return Math.sqrt(latDiff * latDiff + lngDiff * lngDiff) * 111; // 转换为公里
  }

  private static addMinutes(time: string, minutes: number): string {
    const timeParts = time.split(':');
    const hoursStr = timeParts[0] || '0';
    const minsStr = timeParts[1] || '0';
    const hours = parseInt(hoursStr, 10) || 0;
    const mins = parseInt(minsStr, 10) || 0;
    const totalMinutes = hours * 60 + mins + minutes;
    const newHours = Math.floor(totalMinutes / 60) % 24;
    const newMins = totalMinutes % 60;
    return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
  }

  private static generateTimeline(activities: Activity[], meals: Meal[], transportation: Transportation[]) {
    // 合并所有时间项目并排序
    const allItems = [
      ...activities.map(a => ({ time: a.timing.startTime, type: 'activity' as const, title: a.name, duration: a.timing.duration })),
      ...meals.map(m => ({ time: m.time, type: 'meal' as const, title: m.name, duration: m.duration })),
      ...transportation.map(t => ({ time: t.departureTime, type: 'transport' as const, title: `前往${t.to.name}`, duration: t.duration }))
    ];

    return allItems.sort((a, b) => a.time.localeCompare(b.time));
  }
}
