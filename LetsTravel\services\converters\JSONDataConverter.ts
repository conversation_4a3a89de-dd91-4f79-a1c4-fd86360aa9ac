/**
 * 🔄 JSON数据转换器
 * 将Master Solver V2.0的JSON数据转换为前端组件可用的格式
 */

import { JSONDataTransfer, DayPlan, Activity, Meal, Transportation } from '../../types/JourneyDataTypes';
import { DataIntegrityValidator, StandardizedActivity } from '../../utils/DataIntegrityValidator';
import { SmartTimeHandler } from '../../utils/SmartTimeHandler';
import { TrulyUnifiedBudgetEngine } from '../../utils/TrulyUnifiedBudgetEngine';

export interface ComponentPropsData {
  dayCardProps: DayCardProps[];
  timelineProps: TimelineProps;
  budgetProps: BudgetProps;
  mapProps: MapProps;
  headerProps: HeaderProps;
  summaryProps: SummaryProps;
}

export interface DayCardProps {
  dayNumber: number;
  dayTitle: string;
  dateText: string;
  dayOfWeek: string;
  activities: ActivityCardData[];
  meals: MealCardData[];
  transportation: TransportCardData[];
  summary: DaySummaryData;
  onDayPress: () => void;
  onActivityPress: (activityId: string) => void;
}

export interface ActivityCardData {
  id: string;
  title: string;
  subtitle: string;
  timeRange: string;
  duration: string;
  location: string;
  cost: string;
  icon: string;
  color: string;
  expandedContent: {
    description: string;
    highlights: string[];
    tips: string[];
    photoSpots: string[];
    culturalTips: string[];
  };
  onPress: () => void;
}

export interface MealCardData {
  type: string;
  name: string;
  time: string;
  cuisine: string;
  cost: string;
  location: string;
  specialties: string[];
}

export interface TransportCardData {
  type: string;
  from: string;
  to: string;
  departureTime: string;
  arrivalTime: string;
  duration: string;
  cost: string;
  instructions: string[];
}

export interface DaySummaryData {
  totalActivities: number;
  totalCost: string;
  highlights: string[];
  energyLevel: string;
  weatherAdvice: string;
}

export interface TimelineProps {
  days: TimelineDayData[];
}

export interface TimelineDayData {
  dayNumber: number;
  date: string;
  items: TimelineItemData[];
}

export interface TimelineItemData {
  time: string;
  type: 'activity' | 'meal' | 'transport';
  title: string;
  duration: number;
  location?: string;
  icon: string;
  color: string;
}

export interface BudgetProps {
  total: string;
  currency: string;
  breakdown: {
    accommodation: { amount: string; percentage: number };
    activities: { amount: string; percentage: number };
    food: { amount: string; percentage: number };
    transportation: { amount: string; percentage: number };
    shopping: { amount: string; percentage: number };
    miscellaneous: { amount: string; percentage: number };
  };
  dailyAverage: string;
  recommendations: string[];
}

export interface MapProps {
  center: { lat: number; lng: number };
  markers: MapMarkerData[];
  routes: MapRouteData[];
}

export interface MapMarkerData {
  id: string;
  title: string;
  type: 'activity' | 'meal' | 'accommodation';
  coordinates: { lat: number; lng: number };
  day: number;
  icon: string;
  color: string;
}

export interface MapRouteData {
  from: { lat: number; lng: number };
  to: { lat: number; lng: number };
  type: 'walking' | 'subway' | 'taxi';
  color: string;
}

export interface HeaderProps {
  title: string;
  destination: string;
  duration: number;
  travelers: number;
  dateRange: string;
  qualityScore: number;
}

export interface SummaryProps {
  totalActivities: number;
  totalCost: string;
  averageDailyCost: string;
  topHighlights: string[];
  weatherSummary: string;
  travelTips: string[];
}

export class JSONDataConverter {

  /**
   * 🔄 增强版主转换方法 - 确保day信息完整性
   */
  static convertToComponentPropsEnhanced(journeyJSON: JSONDataTransfer): ComponentPropsData {
    console.log('🔄 开始增强版JSON数据转换，确保day信息完整性');

    try {
      // 1. 提取并验证所有活动
      const allActivities = this.extractAllActivities(journeyJSON);
      console.log(`📊 提取到${allActivities.length}个活动`);

      // 2. 验证和修复day信息
      const validatedActivities = allActivities.map(activity =>
        DataIntegrityValidator.standardizeActivity(activity)
      );

      // 3. 按天分组并验证
      const groupedByDay = this.groupAndValidateByDay(validatedActivities);

      // 4. 生成增强的DayCard Props
      const dayCardProps = this.generateEnhancedDayCardProps(groupedByDay, journeyJSON);

      // 5. 转换其他数据（保持原有逻辑）
      const payload = journeyJSON.payload;

      console.log(`✅ 增强版数据转换完成: ${dayCardProps.length}天，活动分布:`,
        dayCardProps.map(day => `Day${day.dayNumber}: ${day.activities.length}个活动`));

      return {
        dayCardProps,
        timelineProps: this.generateTimelineProps(payload.dayPlans),
        budgetProps: this.generateBudgetProps(payload.budget),
        mapProps: this.generateMapProps(payload.dayPlans, payload.accommodation),
        headerProps: this.generateHeaderProps(payload.journey, journeyJSON.metadata),
        summaryProps: this.generateSummaryProps(payload)
      };

    } catch (error) {
      console.error('❌ 增强版JSON数据转换失败，降级到原始方法:', error);
      return this.convertToComponentProps(journeyJSON);
    }
  }

  /**
   * 🔄 原始转换方法 (保持兼容性)
   */
  static convertToComponentProps(journeyJSON: JSONDataTransfer): ComponentPropsData {
    console.log('🔄 开始JSON数据转换为组件Props');
    
    try {
      const payload = journeyJSON.payload;
      
      return {
        dayCardProps: this.generateDayCardProps(payload.dayPlans, payload.journey),
        timelineProps: this.generateTimelineProps(payload.dayPlans),
        budgetProps: this.generateBudgetProps(payload.budget),
        mapProps: this.generateMapProps(payload.dayPlans, payload.accommodation),
        headerProps: this.generateHeaderProps(payload.journey, journeyJSON.metadata),
        summaryProps: this.generateSummaryProps(payload)
      };
      
    } catch (error) {
      console.error('❌ JSON数据转换失败:', error);
      return this.generateFallbackProps();
    }
  }
  
  /**
   * 📅 生成DayCard组件Props
   */
  private static generateDayCardProps(dayPlans: DayPlan[], journey: any): DayCardProps[] {
    return dayPlans.map((dayPlan, index) => ({
      dayNumber: dayPlan.dayNumber,
      dayTitle: `Day ${dayPlan.dayNumber}`,  // 🔧 修复：确保显示"Day 1", "Day 2"等
      dateText: this.formatDate(dayPlan.date),
      dayOfWeek: this.getDayOfWeek(dayPlan.date),
      
      // 🎯 活动数据转换
      activities: dayPlan.activities.map(activity => this.convertActivityToCardData(activity)),
      
      // 🍽️ 餐饮数据转换
      meals: dayPlan.meals.map(meal => this.convertMealToCardData(meal)),
      
      // 🚗 交通数据转换
      transportation: dayPlan.transportation.map(transport => this.convertTransportToCardData(transport)),
      
      // 📊 摘要数据
      summary: {
        totalActivities: dayPlan.activities.length,
        totalCost: this.formatCost(dayPlan.summary.totalCost),
        highlights: dayPlan.summary.highlights,
        energyLevel: dayPlan.summary.energyLevel,
        weatherAdvice: dayPlan.summary.weatherAdvice || '12月东京天气较冷，建议保暖'
      },
      
      // 🎯 交互回调
      onDayPress: () => console.log(`Day ${dayPlan.dayNumber} pressed`),
      onActivityPress: (activityId: string) => console.log(`Activity ${activityId} pressed`)
    }));
  }
  
  /**
   * 🎯 转换活动为卡片数据
   */
  private static convertActivityToCardData(activity: Activity): ActivityCardData {
    return {
      id: activity.id,
      title: activity.name || activity.title || activity.activityName || '未命名活动',  // 🔧 修复：多重保护确保活动名称不丢失
      subtitle: activity.location?.name || '位置待定',
      timeRange: activity.timing?.timeRange || `${activity.timing?.startTime || '09:00'}-${activity.timing?.endTime || '10:00'}`,  // 🔧 修复：显示完整时间范围
      duration: this.formatDuration(activity.timing?.duration || 120),
      location: activity.location?.district || activity.location?.name || '位置待定',
      cost: this.formatCost(activity.cost || { amount: 0, currency: 'MYR' }),
      icon: this.getActivityIcon(activity.type),
      color: this.getActivityColor(activity.type),
      expandedContent: {
        description: activity.description || `探索${activity.name || '这个景点'}，体验当地文化`,
        highlights: activity.highlights || ['独特体验', '文化背景', '拍照胜地'],
        tips: activity.tips || ['建议提前了解背景', '注意开放时间'],
        photoSpots: activity.expandedContent?.bestPhotoSpots || [],
        culturalTips: activity.expandedContent?.culturalTips || []
      },
      onPress: () => console.log(`Activity ${activity.id} pressed`)
    };
  }
  
  /**
   * 🍽️ 转换餐饮为卡片数据
   */
  private static convertMealToCardData(meal: Meal): MealCardData {
    return {
      type: this.getMealTypeDisplay(meal.type || 'meal'),
      name: meal.name || '美食体验',
      time: meal.time || '12:00',
      cuisine: meal.cuisine || '当地美食',
      cost: this.formatCost(meal.cost || { amount: 0, currency: 'MYR' }),
      location: meal.location?.district || meal.location?.name || '餐厅位置',
      specialties: meal.specialties
    };
  }
  
  /**
   * 🚗 转换交通为卡片数据
   */
  private static convertTransportToCardData(transport: Transportation): TransportCardData {
    return {
      type: this.getTransportTypeDisplay(transport.type || 'transport'),
      from: transport.from?.name || '出发地',
      to: transport.to?.name || '目的地',
      departureTime: transport.departureTime || '09:00',
      arrivalTime: transport.arrivalTime || '10:00',
      duration: this.formatDuration(transport.duration || 60),
      cost: this.formatCost(transport.cost || { amount: 0, currency: 'MYR' }),
      instructions: transport.details?.instructions || ['请按时出发']
    };
  }
  
  /**
   * ⏰ 生成时间线Props
   */
  private static generateTimelineProps(dayPlans: DayPlan[]): TimelineProps {
    return {
      days: dayPlans.map(dayPlan => ({
        dayNumber: dayPlan.dayNumber,
        date: dayPlan.date,
        items: dayPlan.timeline.map(item => ({
          time: item.time,
          type: item.type,
          title: item.title,
          duration: item.duration,
          location: item.location,
          icon: this.getTimelineIcon(item.type),
          color: this.getTimelineColor(item.type)
        }))
      }))
    };
  }
  
  /**
   * 💰 生成预算Props
   */
  private static generateBudgetProps(budget: any): BudgetProps {
    const total = budget.total.amount;
    
    return {
      total: this.formatCost(budget.total),
      currency: budget.total.currency,
      breakdown: {
        accommodation: {
          amount: this.formatCost(budget.breakdown.accommodation),
          percentage: Math.round((budget.breakdown.accommodation.amount / total) * 100)
        },
        activities: {
          amount: this.formatCost(budget.breakdown.activities),
          percentage: Math.round((budget.breakdown.activities.amount / total) * 100)
        },
        food: {
          amount: this.formatCost(budget.breakdown.food),
          percentage: Math.round((budget.breakdown.food.amount / total) * 100)
        },
        transportation: {
          amount: this.formatCost(budget.breakdown.transportation),
          percentage: Math.round((budget.breakdown.transportation.amount / total) * 100)
        },
        shopping: {
          amount: this.formatCost(budget.breakdown.shopping),
          percentage: Math.round((budget.breakdown.shopping.amount / total) * 100)
        },
        miscellaneous: {
          amount: this.formatCost(budget.breakdown.miscellaneous),
          percentage: Math.round((budget.breakdown.miscellaneous.amount / total) * 100)
        }
      },
      dailyAverage: this.formatCost(budget.dailyAverage),
      recommendations: budget.recommendations
    };
  }
  
  /**
   * 🗺️ 生成地图Props
   */
  private static generateMapProps(dayPlans: DayPlan[], accommodation: any[]): MapProps {
    const markers: MapMarkerData[] = [];
    const routes: MapRouteData[] = [];
    
    // 添加活动标记
    dayPlans.forEach(dayPlan => {
      dayPlan.activities.forEach(activity => {
        if (activity.location.coordinates) {
          markers.push({
            id: activity.id,
            title: activity.name,
            type: 'activity',
            coordinates: activity.location.coordinates,
            day: dayPlan.dayNumber,
            icon: this.getActivityIcon(activity.type),
            color: this.getActivityColor(activity.type)
          });
        }
      });
    });
    
    return {
      center: { lat: 35.6762, lng: 139.6503 }, // 东京中心
      markers,
      routes
    };
  }
  
  /**
   * 📊 生成头部Props
   */
  private static generateHeaderProps(journey: any, metadata: any): HeaderProps {
    return {
      title: journey.title,
      destination: journey.destination,
      duration: journey.duration,
      travelers: journey.travelers,
      dateRange: `${this.formatDate(journey.startDate)} - ${this.formatDate(journey.endDate)}`,
      qualityScore: metadata.qualityScore
    };
  }
  
  /**
   * 📈 生成摘要Props
   */
  private static generateSummaryProps(payload: any): SummaryProps {
    const totalActivities = payload.dayPlans.reduce((sum: number, day: DayPlan) => sum + day.activities.length, 0);
    
    return {
      totalActivities,
      totalCost: this.formatCost(payload.budget.total),
      averageDailyCost: this.formatCost(payload.budget.dailyAverage),
      topHighlights: payload.dayPlans.flatMap((day: DayPlan) => day.summary.highlights).slice(0, 5),
      weatherSummary: '12月东京天气较冷，平均气温5-12°C',
      travelTips: [
        '建议穿着保暖衣物',
        '携带雨具以防突然降雨',
        '提前预约热门餐厅',
        '购买交通一日券更划算'
      ]
    };
  }
  
  /**
   * 🔧 辅助方法
   */
  
  private static formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', { 
      month: 'long', 
      day: 'numeric',
      weekday: 'short'
    });
  }
  
  private static getDayOfWeek(dateString: string): string {
    const date = new Date(dateString);
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return days[date.getDay()];
  }
  
  private static formatDuration(minutes: number): string {
    if (minutes < 60) return `${minutes}分钟`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
  }
  
  private static formatCost(cost: { amount: number; currency: string }): string {
    const symbols: Record<string, string> = {
      'JPY': '¥',
      'MYR': 'RM',
      'USD': '$',
      'CNY': '¥'
    };
    
    const symbol = symbols[cost.currency] || cost.currency;
    return `${symbol}${cost.amount.toLocaleString()}`;
  }
  
  private static getActivityIcon(type: string): string {
    const icons: Record<string, string> = {
      'attraction': '🏛️',
      'cultural': '⛩️',
      'experience': '🎭',
      'entertainment': '🎪',
      'shopping': '🛍️'
    };
    return icons[type] || '📍';
  }
  
  private static getActivityColor(type: string): string {
    const colors: Record<string, string> = {
      'attraction': '#FF6B6B',
      'cultural': '#4ECDC4',
      'experience': '#45B7D1',
      'entertainment': '#96CEB4',
      'shopping': '#FFEAA7'
    };
    return colors[type] || '#DDA0DD';
  }
  
  private static getMealTypeDisplay(type: string): string {
    const displays: Record<string, string> = {
      'breakfast': '早餐',
      'lunch': '午餐',
      'dinner': '晚餐',
      'snack': '小食',
      'cafe': '咖啡'
    };
    return displays[type] || type;
  }
  
  private static getTransportTypeDisplay(type: string): string {
    const displays: Record<string, string> = {
      'walking': '步行',
      'subway': '地铁',
      'taxi': '出租车',
      'bus': '公交',
      'train': '电车'
    };
    return displays[type] || type;
  }
  
  private static getTimelineIcon(type: string): string {
    const icons: Record<string, string> = {
      'activity': '📍',
      'meal': '🍽️',
      'transport': '🚇'
    };
    return icons[type] || '⭐';
  }
  
  private static getTimelineColor(type: string): string {
    const colors: Record<string, string> = {
      'activity': '#FF6B6B',
      'meal': '#4ECDC4',
      'transport': '#45B7D1'
    };
    return colors[type] || '#DDA0DD';
  }
  
  private static generateFallbackProps(): ComponentPropsData {
    console.log('🔄 生成降级组件Props');
    
    return {
      dayCardProps: [],
      timelineProps: { days: [] },
      budgetProps: {
        total: 'RM0',
        currency: 'MYR',
        breakdown: {
          accommodation: { amount: 'RM0', percentage: 0 },
          activities: { amount: 'RM0', percentage: 0 },
          food: { amount: 'RM0', percentage: 0 },
          transportation: { amount: 'RM0', percentage: 0 },
          shopping: { amount: 'RM0', percentage: 0 },
          miscellaneous: { amount: 'RM0', percentage: 0 }
        },
        dailyAverage: 'RM0',
        recommendations: []
      },
      mapProps: {
        center: { lat: 35.6762, lng: 139.6503 },
        markers: [],
        routes: []
      },
      headerProps: {
        title: '行程生成中...',
        destination: '',
        duration: 0,
        travelers: 0,
        dateRange: '',
        qualityScore: 0
      },
      summaryProps: {
        totalActivities: 0,
        totalCost: 'RM0',
        averageDailyCost: 'RM0',
        topHighlights: [],
        weatherSummary: '',
        travelTips: []
      }
    };
  }

  /**
   * 📊 提取所有活动（从多个可能的位置）
   */
  private static extractAllActivities(journeyJSON: JSONDataTransfer): any[] {
    const activities: any[] = [];

    try {
      // 从dayPlans中提取活动
      if (journeyJSON.payload?.dayPlans) {
        journeyJSON.payload.dayPlans.forEach((dayPlan, dayIndex) => {
          // 提取活动
          if (dayPlan.activities) {
            dayPlan.activities.forEach(activity => {
              activities.push({
                ...activity,
                day: dayIndex + 1,
                source: 'dayPlan'
              });
            });
          }

          // 提取餐饮
          if (dayPlan.meals) {
            dayPlan.meals.forEach(meal => {
              activities.push({
                ...meal,
                day: dayIndex + 1,
                type: 'meal',
                source: 'meal'
              });
            });
          }

          // 提取交通
          if (dayPlan.transportation) {
            dayPlan.transportation.forEach(transport => {
              activities.push({
                ...transport,
                day: dayIndex + 1,
                type: 'transport',
                source: 'transport'
              });
            });
          }
        });
      }

      console.log(`📊 从dayPlans提取到${activities.length}个活动`);

    } catch (error) {
      console.error('❌ 活动提取失败:', error);
    }

    return activities;
  }

  /**
   * 🔍 按天分组并验证
   */
  private static groupAndValidateByDay(activities: StandardizedActivity[]): { [day: number]: StandardizedActivity[] } {
    const grouped: { [day: number]: StandardizedActivity[] } = {};

    activities.forEach(activity => {
      const day = activity.day;
      if (!grouped[day]) {
        grouped[day] = [];
      }
      grouped[day].push(activity);
    });

    // 验证分组结果
    const isValid = DataIntegrityValidator.validateGrouping(grouped);

    // 如果所有活动都在Day 1，尝试智能重新分配
    if (!isValid && Object.keys(grouped).length === 1 && activities.length > 6) {
      console.log('⚠️ 检测到所有活动都在Day 1，启动智能重新分配');
      return DataIntegrityValidator.intelligentRedistribution(activities);
    }

    return grouped;
  }

  /**
   * 🎨 生成增强的DayCard Props
   */
  private static generateEnhancedDayCardProps(groupedActivities: { [day: number]: StandardizedActivity[] }, journeyJSON: JSONDataTransfer): DayCardProps[] {
    const dayCardProps: DayCardProps[] = [];
    const totalDays = Math.max(...Object.keys(groupedActivities).map(Number), 3);

    for (let dayNumber = 1; dayNumber <= totalDays; dayNumber++) {
      const dayActivities = groupedActivities[dayNumber] || [];

      // 按时间排序活动
      const sortedActivities = SmartTimeHandler.sortActivitiesByTime(dayActivities);

      // 计算预算 - 使用统一预算引擎
      const dayBudget = dayActivities.reduce((total, activity) => {
        const budget = TrulyUnifiedBudgetEngine.getUnifiedActivityBudget(activity);
        return total + (budget.isFree ? 0 : budget.amount);
      }, 0);

      dayCardProps.push({
        dayNumber,
        dayTitle: `Day ${dayNumber}`,
        dateText: SmartTimeHandler.getDateDisplay(dayNumber),
        dayOfWeek: this.getDayOfWeek(SmartTimeHandler.getDateDisplay(dayNumber)),

        // 转换活动数据
        activities: sortedActivities.map(activity => this.convertEnhancedActivityToCardData(activity)),

        // 空的餐饮和交通（已包含在activities中）
        meals: [],
        transportation: [],

        // 摘要信息
        summary: {
          totalActivities: dayActivities.length,
          totalCost: `RM${dayBudget.total}`,
          weatherAdvice: SmartTimeHandler.getWeatherInfo(dayNumber),
          highlights: this.generateDayHighlights(dayActivities),
          tips: this.generateDayTips(dayActivities)
        }
      });
    }

    return dayCardProps;
  }

  /**
   * 🎯 转换增强的活动数据
   */
  private static convertEnhancedActivityToCardData(activity: StandardizedActivity): ActivityCardData {
    return {
      id: activity.id,
      title: activity.name,
      subtitle: activity.location?.name || '位置待定',
      timeRange: SmartTimeHandler.formatTimeRange(activity.startTime, activity.endTime),
      duration: this.formatDuration(SmartTimeHandler.calculateDuration(activity.startTime, activity.endTime)),
      location: activity.location?.district || activity.location?.name || '位置待定',
      cost: this.formatCost({ amount: activity.cost, currency: 'MYR' }),
      type: activity.type,
      icon: this.getActivityIcon(activity.type),
      expandedContent: {
        description: this.generateActivityDescription(activity),
        highlights: this.generateActivityHighlights(activity),
        tips: this.generateActivityTips(activity),
        photoSpots: [],
        culturalTips: []
      }
    };
  }

  /**
   * 🏷️ 获取活动图标
   */
  private static getActivityIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'attraction': '🏛️',
      'meal': '🍽️',
      'transport': '🚇',
      'accommodation': '🏨',
      'shopping': '🛍️',
      'other': '📍'
    };
    return icons[type] || '📍';
  }
}
