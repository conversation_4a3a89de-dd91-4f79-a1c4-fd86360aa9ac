/**
 * 🔍 LLM质量检查器
 * 使用多个LLM模型进行交叉验证和质量评估
 */

import { JSONDataTransfer } from '../../types/JourneyDataTypes';

export interface QualityReport {
  overallScore: number;
  dimensions: {
    contentQuality: number;
    timeLogic: number;
    geographicalLogic: number;
    budgetConsistency: number;
    culturalAccuracy: number;
    seasonalAdaptation: number;
  };
  criticalIssues: QualityIssue[];
  improvements: string[];
  autoFixable: AutoFixAction[];
  strengths: string[];
}

export interface QualityIssue {
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  location: string;
  suggestion: string;
}

export interface AutoFixAction {
  type: string;
  description: string;
  action: string;
  target: string;
}

export class LLMQualityChecker {
  private llmService: any;
  
  constructor() {
    this.llmService = this.initializeLLMService();
  }
  
  /**
   * 🔍 验证Journey质量
   */
  async validateJourneyQuality(journeyJSON: JSONDataTransfer): Promise<QualityReport> {
    console.log('🔍 开始LLM质量检查');
    const startTime = Date.now();
    
    try {
      // 使用多个模型进行质量检查
      const reports = await Promise.all([
        this.validateWithGemini(journeyJSON),      // 主要验证
        this.validateWithGeminiFlash(journeyJSON), // 降级验证
        this.validateWithGemini25(journeyJSON)     // 备用验证
      ]);
      
      const aggregatedReport = this.aggregateQualityReports(reports);
      
      console.log(`✅ LLM质量检查完成: ${Date.now() - startTime}ms, 评分${aggregatedReport.overallScore}`);
      return aggregatedReport;
      
    } catch (error) {
      console.error('❌ LLM质量检查失败:', error);
      return this.generateFallbackQualityReport();
    }
  }
  
  /**
   * 🤖 使用Gemini 2.5 Flash Lite验证
   */
  private async validateWithGemini(journeyJSON: JSONDataTransfer): Promise<QualityReport> {
    console.log('🤖 Gemini 2.5 Flash Lite质量验证');
    
    const prompt = this.buildQualityCheckPrompt(journeyJSON);
    
    try {
      const llmResponse = await this.callLLM({
        model: "google/gemini-2.0-flash-exp:free",
        prompt: prompt,
        temperature: 0.1,
        maxTokens: 1000,
        responseFormat: "json"
      });
      
      const qualityReport = JSON.parse(llmResponse.content);
      return this.normalizeQualityReport(qualityReport, 'gemini_2_5_flash');
      
    } catch (error) {
      console.warn('⚠️ Gemini验证失败:', error);
      return this.generateFallbackQualityReport();
    }
  }
  
  /**
   * 🤖 使用Gemini 2.0 Flash验证（降级）
   */
  private async validateWithGeminiFlash(journeyJSON: JSONDataTransfer): Promise<QualityReport> {
    console.log('🤖 Gemini 2.0 Flash质量验证（降级）');
    
    const prompt = this.buildSimplifiedQualityCheckPrompt(journeyJSON);
    
    try {
      const llmResponse = await this.callLLM({
        model: "google/gemini-2.0-flash-exp:free",
        prompt: prompt,
        temperature: 0.1,
        maxTokens: 800,
        responseFormat: "json"
      });
      
      const qualityReport = JSON.parse(llmResponse.content);
      return this.normalizeQualityReport(qualityReport, 'gemini_2_0_flash');
      
    } catch (error) {
      console.warn('⚠️ Gemini Flash验证失败:', error);
      return this.generateFallbackQualityReport();
    }
  }
  
  /**
   * 🤖 使用Gemini 2.5验证（备用）
   */
  private async validateWithGemini25(journeyJSON: JSONDataTransfer): Promise<QualityReport> {
    console.log('🤖 Gemini 2.5质量验证（备用）');
    
    const prompt = this.buildDetailedQualityCheckPrompt(journeyJSON);
    
    try {
      const llmResponse = await this.callLLM({
        model: "google/gemini-flash-1.5-8b:free",
        prompt: prompt,
        temperature: 0.1,
        maxTokens: 1200,
        responseFormat: "json"
      });
      
      const qualityReport = JSON.parse(llmResponse.content);
      return this.normalizeQualityReport(qualityReport, 'gemini_2_5');
      
    } catch (error) {
      console.warn('⚠️ Gemini 2.5验证失败:', error);
      return this.generateFallbackQualityReport();
    }
  }
  
  /**
   * 🏗️ 构建质量检查Prompt
   */
  private buildQualityCheckPrompt(journeyJSON: JSONDataTransfer): string {
    return `
# 东京旅游行程质量评估专家

## 任务
对以下东京3天旅游行程进行全面质量评估。

## 行程数据
\`\`\`json
${JSON.stringify(journeyJSON.payload, null, 2)}
\`\`\`

## 评估维度

### 1. 内容质量 (0-1分)
- 活动描述是否生动具体
- 是否避免了模板化内容
- 文化准确性如何

### 2. 时间逻辑 (0-1分)
- 时间安排是否合理
- 是否有时间冲突
- 活动间隔是否充足

### 3. 地理逻辑 (0-1分)
- 地点安排是否合理
- 是否减少了无效移动
- 交通连接是否顺畅

### 4. 预算一致性 (0-1分)
- 费用计算是否准确
- 是否符合预算约束
- 价格是否合理

### 5. 文化准确性 (0-1分)
- 文化背景是否正确
- 礼仪建议是否恰当
- 当地习俗是否准确

### 6. 季节适应性 (0-1分)
- 是否考虑了12月冬季特点
- 活动是否适合冬季
- 是否有季节性建议

## 输出格式
\`\`\`json
{
  "overallScore": 0.88,
  "dimensions": {
    "contentQuality": 0.9,
    "timeLogic": 0.85,
    "geographicalLogic": 0.9,
    "budgetConsistency": 0.85,
    "culturalAccuracy": 0.9,
    "seasonalAdaptation": 0.9
  },
  "criticalIssues": [
    {
      "type": "time_conflict",
      "severity": "high",
      "description": "第2天14:00-16:00时间重叠",
      "location": "dayPlans[1].activities[2]",
      "suggestion": "调整活动开始时间至16:30"
    }
  ],
  "improvements": [
    "建议在第1天增加更多室内活动应对冬季天气",
    "第3天的交通安排可以更优化"
  ],
  "autoFixable": [
    {
      "type": "time_adjustment",
      "description": "自动调整重叠时间",
      "action": "shift_end_time",
      "target": "dayPlans[1].activities[1]"
    }
  ],
  "strengths": [
    "活动选择很好地体现了东京冬季特色",
    "预算分配合理"
  ]
}
\`\`\`

请提供详细、准确的评估，重点关注实际可行性和用户体验。
`;
  }
  
  /**
   * 🏗️ 构建简化质量检查Prompt
   */
  private buildSimplifiedQualityCheckPrompt(journeyJSON: JSONDataTransfer): string {
    return `
# 旅游行程快速质量评估

## 任务
快速评估以下东京行程的基本质量。

## 行程概要
- 目的地: ${journeyJSON.payload.journey.destination}
- 天数: ${journeyJSON.payload.journey.duration}
- 活动总数: ${journeyJSON.payload.dayPlans.reduce((sum, day) => sum + day.activities.length, 0)}

## 评估要点
1. 时间安排是否合理
2. 预算是否一致
3. 地理位置是否优化
4. 内容是否丰富

## 输出格式
\`\`\`json
{
  "overallScore": 0.85,
  "dimensions": {
    "contentQuality": 0.8,
    "timeLogic": 0.9,
    "geographicalLogic": 0.85,
    "budgetConsistency": 0.8,
    "culturalAccuracy": 0.85,
    "seasonalAdaptation": 0.85
  },
  "criticalIssues": [],
  "improvements": ["建议1", "建议2"],
  "autoFixable": [],
  "strengths": ["优点1", "优点2"]
}
\`\`\`
`;
  }
  
  /**
   * 🏗️ 构建详细质量检查Prompt
   */
  private buildDetailedQualityCheckPrompt(journeyJSON: JSONDataTransfer): string {
    return `
# 深度旅游行程质量分析

## 任务
对东京行程进行深度质量分析，重点关注用户体验和实用性。

## 分析重点
1. 每个活动的实际可行性
2. 交通连接的合理性
3. 时间分配的科学性
4. 文化体验的深度
5. 冬季适应性

## 详细评估
请对每一天的安排进行详细分析，指出具体的优点和改进空间。

## 输出格式
\`\`\`json
{
  "overallScore": 0.92,
  "dimensions": {
    "contentQuality": 0.95,
    "timeLogic": 0.9,
    "geographicalLogic": 0.9,
    "budgetConsistency": 0.9,
    "culturalAccuracy": 0.95,
    "seasonalAdaptation": 0.9
  },
  "criticalIssues": [],
  "improvements": ["深度建议1", "深度建议2"],
  "autoFixable": [],
  "strengths": ["深度优点1", "深度优点2"]
}
\`\`\`
`;
  }
  
  /**
   * 📊 聚合质量报告
   */
  private aggregateQualityReports(reports: QualityReport[]): QualityReport {
    const validReports = reports.filter(report => report.overallScore > 0);
    
    if (validReports.length === 0) {
      return this.generateFallbackQualityReport();
    }
    
    // 计算平均分数
    const avgOverallScore = validReports.reduce((sum, report) => sum + report.overallScore, 0) / validReports.length;
    
    const avgDimensions = {
      contentQuality: validReports.reduce((sum, report) => sum + report.dimensions.contentQuality, 0) / validReports.length,
      timeLogic: validReports.reduce((sum, report) => sum + report.dimensions.timeLogic, 0) / validReports.length,
      geographicalLogic: validReports.reduce((sum, report) => sum + report.dimensions.geographicalLogic, 0) / validReports.length,
      budgetConsistency: validReports.reduce((sum, report) => sum + report.dimensions.budgetConsistency, 0) / validReports.length,
      culturalAccuracy: validReports.reduce((sum, report) => sum + report.dimensions.culturalAccuracy, 0) / validReports.length,
      seasonalAdaptation: validReports.reduce((sum, report) => sum + report.dimensions.seasonalAdaptation, 0) / validReports.length
    };
    
    // 合并问题和建议
    const allIssues = validReports.flatMap(report => report.criticalIssues);
    const allImprovements = [...new Set(validReports.flatMap(report => report.improvements))];
    const allAutoFixable = validReports.flatMap(report => report.autoFixable);
    const allStrengths = [...new Set(validReports.flatMap(report => report.strengths))];
    
    return {
      overallScore: avgOverallScore,
      dimensions: avgDimensions,
      criticalIssues: allIssues,
      improvements: allImprovements,
      autoFixable: allAutoFixable,
      strengths: allStrengths
    };
  }
  
  /**
   * 🔧 标准化质量报告
   */
  private normalizeQualityReport(rawReport: any, source: string): QualityReport {
    return {
      overallScore: rawReport.overallScore || 0.8,
      dimensions: {
        contentQuality: rawReport.dimensions?.contentQuality || 0.8,
        timeLogic: rawReport.dimensions?.timeLogic || 0.8,
        geographicalLogic: rawReport.dimensions?.geographicalLogic || 0.8,
        budgetConsistency: rawReport.dimensions?.budgetConsistency || 0.8,
        culturalAccuracy: rawReport.dimensions?.culturalAccuracy || 0.8,
        seasonalAdaptation: rawReport.dimensions?.seasonalAdaptation || 0.8
      },
      criticalIssues: rawReport.criticalIssues || [],
      improvements: rawReport.improvements || [],
      autoFixable: rawReport.autoFixable || [],
      strengths: rawReport.strengths || [`${source}验证通过`]
    };
  }
  
  /**
   * 🔄 生成降级质量报告
   */
  private generateFallbackQualityReport(): QualityReport {
    return {
      overallScore: 0.75,
      dimensions: {
        contentQuality: 0.75,
        timeLogic: 0.8,
        geographicalLogic: 0.75,
        budgetConsistency: 0.8,
        culturalAccuracy: 0.7,
        seasonalAdaptation: 0.7
      },
      criticalIssues: [],
      improvements: ['建议进行人工审核'],
      autoFixable: [],
      strengths: ['基础结构完整']
    };
  }
  
  /**
   * 🔧 辅助方法
   */
  
  private initializeLLMService(): any {
    return {
      initialized: true,
      models: ["google/gemini-2.0-flash-exp:free", "google/gemini-flash-1.5-8b:free"]
    };
  }
  
  private async callLLM(request: any): Promise<any> {
    console.log(`🤖 调用LLM质量检查: ${request.model}`);
    
    // 模拟LLM调用返回质量报告
    return {
      content: JSON.stringify({
        overallScore: 0.88,
        dimensions: {
          contentQuality: 0.9,
          timeLogic: 0.85,
          geographicalLogic: 0.9,
          budgetConsistency: 0.85,
          culturalAccuracy: 0.9,
          seasonalAdaptation: 0.9
        },
        criticalIssues: [],
        improvements: [
          "建议增加更多冬季特色活动",
          "可以优化交通路线减少等待时间"
        ],
        autoFixable: [],
        strengths: [
          "活动选择丰富多样",
          "时间安排合理",
          "预算分配恰当",
          "很好地考虑了冬季特点"
        ]
      })
    };
  }
}
