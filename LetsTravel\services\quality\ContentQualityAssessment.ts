/**
 * ✅ 内容质量评估系统
 * 
 * 建立多维度内容质量评估机制，确保内容准确性和实用性
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 核心接口定义 =====

export interface ContentQualityReport {
  id: string;
  contentId: string;
  contentType: ContentType;
  
  // 总体评分
  overallScore: number;        // 0-100
  grade: QualityGrade;
  
  // 维度评分
  dimensions: QualityDimensions;
  
  // 详细分析
  analysis: QualityAnalysis;
  
  // 改进建议
  recommendations: QualityRecommendation[];
  
  // 评估元数据
  metadata: AssessmentMetadata;
}

export interface QualityDimensions {
  accuracy: DimensionScore;      // 准确性
  completeness: DimensionScore;  // 完整性
  relevance: DimensionScore;     // 相关性
  clarity: DimensionScore;       // 清晰度
  timeliness: DimensionScore;    // 时效性
  usefulness: DimensionScore;    // 实用性
  engagement: DimensionScore;    // 参与度
  accessibility: DimensionScore; // 可访问性
}

export interface DimensionScore {
  score: number;                 // 0-100
  weight: number;               // 权重 0-1
  issues: QualityIssue[];
  strengths: string[];
  confidence: number;           // 评估置信度 0-1
}

export interface QualityIssue {
  type: IssueType;
  severity: IssueSeverity;
  description: string;
  location?: string;            // 问题位置
  suggestion: string;           // 改进建议
  impact: number;              // 影响程度 0-1
}

export interface QualityAnalysis {
  // 内容统计
  statistics: ContentStatistics;
  
  // 语言质量
  language: LanguageQuality;
  
  // 结构质量
  structure: StructureQuality;
  
  // 信息质量
  information: InformationQuality;
  
  // 用户体验
  userExperience: UserExperienceQuality;
}

export interface QualityRecommendation {
  priority: RecommendationPriority;
  category: RecommendationCategory;
  title: string;
  description: string;
  actionItems: string[];
  expectedImpact: number;       // 预期改进程度 0-1
  effort: EffortLevel;
}

// ===== 枚举定义 =====

export enum ContentType {
  ACTIVITY_DESCRIPTION = 'activity_description',
  LOCATION_INFO = 'location_info',
  TRANSPORTATION_GUIDE = 'transportation_guide',
  CULTURAL_INSIGHT = 'cultural_insight',
  PRACTICAL_TIP = 'practical_tip',
  REVIEW_SUMMARY = 'review_summary'
}

export enum QualityGrade {
  EXCELLENT = 'A+',
  GOOD = 'A',
  SATISFACTORY = 'B',
  NEEDS_IMPROVEMENT = 'C',
  POOR = 'D',
  UNACCEPTABLE = 'F'
}

export enum IssueType {
  FACTUAL_ERROR = 'factual_error',
  OUTDATED_INFO = 'outdated_info',
  MISSING_INFO = 'missing_info',
  UNCLEAR_LANGUAGE = 'unclear_language',
  POOR_STRUCTURE = 'poor_structure',
  ACCESSIBILITY_ISSUE = 'accessibility_issue',
  RELEVANCE_ISSUE = 'relevance_issue'
}

export enum IssueSeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

export enum RecommendationPriority {
  URGENT = 'urgent',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

export enum RecommendationCategory {
  CONTENT_ACCURACY = 'content_accuracy',
  LANGUAGE_IMPROVEMENT = 'language_improvement',
  STRUCTURE_OPTIMIZATION = 'structure_optimization',
  USER_EXPERIENCE = 'user_experience',
  ACCESSIBILITY = 'accessibility'
}

export enum EffortLevel {
  MINIMAL = 'minimal',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  EXTENSIVE = 'extensive'
}

// ===== 质量评估系统 =====

export class ContentQualityAssessment {
  
  /**
   * 🎯 评估内容质量
   */
  static assessContent(
    content: string,
    contentType: ContentType,
    metadata?: any
  ): ContentQualityReport {
    
    // 1. 分析内容统计
    const statistics = this.analyzeStatistics(content);
    
    // 2. 评估各个维度
    const dimensions = this.assessDimensions(content, contentType, statistics);
    
    // 3. 计算总体评分
    const overallScore = this.calculateOverallScore(dimensions);
    const grade = this.determineGrade(overallScore);
    
    // 4. 生成详细分析
    const analysis = this.generateAnalysis(content, contentType, statistics);
    
    // 5. 生成改进建议
    const recommendations = this.generateRecommendations(dimensions, analysis);
    
    // 6. 创建元数据
    const assessmentMetadata = this.createMetadata(contentType);
    
    return {
      id: `qa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      contentId: metadata?.id || 'unknown',
      contentType,
      overallScore,
      grade,
      dimensions,
      analysis,
      recommendations,
      metadata: assessmentMetadata
    };
  }
  
  /**
   * 📊 分析内容统计
   */
  private static analyzeStatistics(content: string): ContentStatistics {
    const words = content.split(/\s+/).filter(word => word.length > 0);
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    return {
      wordCount: words.length,
      sentenceCount: sentences.length,
      paragraphCount: paragraphs.length,
      characterCount: content.length,
      averageWordsPerSentence: words.length / Math.max(sentences.length, 1),
      averageSentencesPerParagraph: sentences.length / Math.max(paragraphs.length, 1),
      readingTime: Math.ceil(words.length / 200), // 假设每分钟200字
      complexityScore: this.calculateComplexityScore(words, sentences)
    };
  }
  
  /**
   * 📏 评估各维度质量
   */
  private static assessDimensions(
    content: string,
    contentType: ContentType,
    statistics: ContentStatistics
  ): QualityDimensions {
    
    return {
      accuracy: this.assessAccuracy(content, contentType),
      completeness: this.assessCompleteness(content, contentType, statistics),
      relevance: this.assessRelevance(content, contentType),
      clarity: this.assessClarity(content, statistics),
      timeliness: this.assessTimeliness(content),
      usefulness: this.assessUsefulness(content, contentType),
      engagement: this.assessEngagement(content, statistics),
      accessibility: this.assessAccessibility(content)
    };
  }
  
  /**
   * ✅ 评估准确性
   */
  private static assessAccuracy(content: string, contentType: ContentType): DimensionScore {
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 85; // 基础分数
    
    // 检查常见错误
    if (content.includes('大概') || content.includes('可能') || content.includes('据说')) {
      issues.push({
        type: IssueType.FACTUAL_ERROR,
        severity: IssueSeverity.MEDIUM,
        description: '包含不确定性表述',
        suggestion: '使用更准确的表述',
        impact: 0.3
      });
      score -= 15;
    }
    
    // 检查具体信息
    if (content.match(/\d{1,2}:\d{2}/)) { // 包含具体时间
      strengths.push('包含具体时间信息');
      score += 5;
    }
    
    if (content.match(/MYR?\s*\d+/)) { // 包含具体价格
      strengths.push('包含具体价格信息');
      score += 5;
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.25,
      issues,
      strengths,
      confidence: 0.8
    };
  }
  
  /**
   * 📋 评估完整性
   */
  private static assessCompleteness(
    content: string,
    contentType: ContentType,
    statistics: ContentStatistics
  ): DimensionScore {
    
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 70; // 基础分数
    
    // 根据内容类型检查必要信息
    const requiredElements = this.getRequiredElements(contentType);
    
    for (const element of requiredElements) {
      if (this.hasElement(content, element)) {
        strengths.push(`包含${element.name}`);
        score += element.weight;
      } else {
        issues.push({
          type: IssueType.MISSING_INFO,
          severity: element.critical ? IssueSeverity.HIGH : IssueSeverity.MEDIUM,
          description: `缺少${element.name}`,
          suggestion: `添加${element.name}信息`,
          impact: element.weight / 100
        });
      }
    }
    
    // 检查内容长度
    if (statistics.wordCount < 50) {
      issues.push({
        type: IssueType.MISSING_INFO,
        severity: IssueSeverity.HIGH,
        description: '内容过于简短',
        suggestion: '增加更多详细信息',
        impact: 0.4
      });
      score -= 20;
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.2,
      issues,
      strengths,
      confidence: 0.9
    };
  }
  
  /**
   * 🎯 评估相关性
   */
  private static assessRelevance(content: string, contentType: ContentType): DimensionScore {
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 80;
    
    // 检查是否包含无关信息
    const irrelevantPatterns = [
      /基于.*偏好生成/,
      /AI生成/,
      /系统推荐/
    ];
    
    for (const pattern of irrelevantPatterns) {
      if (pattern.test(content)) {
        issues.push({
          type: IssueType.RELEVANCE_ISSUE,
          severity: IssueSeverity.MEDIUM,
          description: '包含系统生成提示',
          suggestion: '移除AI生成标识',
          impact: 0.2
        });
        score -= 10;
      }
    }
    
    // 检查是否包含实用信息
    if (content.includes('地址') || content.includes('时间') || content.includes('价格')) {
      strengths.push('包含实用信息');
      score += 10;
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.15,
      issues,
      strengths,
      confidence: 0.85
    };
  }
  
  /**
   * 💡 评估清晰度
   */
  private static assessClarity(content: string, statistics: ContentStatistics): DimensionScore {
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 75;
    
    // 检查句子长度
    if (statistics.averageWordsPerSentence > 25) {
      issues.push({
        type: IssueType.UNCLEAR_LANGUAGE,
        severity: IssueSeverity.MEDIUM,
        description: '句子过长，影响理解',
        suggestion: '将长句拆分为短句',
        impact: 0.3
      });
      score -= 15;
    } else if (statistics.averageWordsPerSentence < 15) {
      strengths.push('句子长度适中，易于理解');
      score += 10;
    }
    
    // 检查段落结构
    if (statistics.paragraphCount > 1) {
      strengths.push('内容结构清晰');
      score += 5;
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.15,
      issues,
      strengths,
      confidence: 0.8
    };
  }
  
  /**
   * ⏰ 评估时效性
   */
  private static assessTimeliness(content: string): DimensionScore {
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 80;
    
    // 检查是否包含过时信息标识
    const outdatedPatterns = [
      /\d{4}年以前/,
      /已关闭/,
      /不再营业/
    ];
    
    for (const pattern of outdatedPatterns) {
      if (pattern.test(content)) {
        issues.push({
          type: IssueType.OUTDATED_INFO,
          severity: IssueSeverity.HIGH,
          description: '包含过时信息',
          suggestion: '更新为最新信息',
          impact: 0.5
        });
        score -= 30;
      }
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.1,
      issues,
      strengths,
      confidence: 0.7
    };
  }
  
  /**
   * 🎯 评估实用性
   */
  private static assessUsefulness(content: string, contentType: ContentType): DimensionScore {
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 70;
    
    // 检查实用信息
    const usefulPatterns = [
      /\d{1,2}:\d{2}/, // 时间
      /MYR?\s*\d+/,    // 价格
      /地址[:：]/,      // 地址
      /电话[:：]/,      // 电话
      /网站[:：]/       // 网站
    ];
    
    let usefulCount = 0;
    for (const pattern of usefulPatterns) {
      if (pattern.test(content)) {
        usefulCount++;
      }
    }
    
    if (usefulCount >= 3) {
      strengths.push('包含丰富的实用信息');
      score += 20;
    } else if (usefulCount >= 1) {
      strengths.push('包含基本实用信息');
      score += 10;
    } else {
      issues.push({
        type: IssueType.MISSING_INFO,
        severity: IssueSeverity.MEDIUM,
        description: '缺少实用信息',
        suggestion: '添加时间、价格、地址等实用信息',
        impact: 0.4
      });
      score -= 20;
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.1,
      issues,
      strengths,
      confidence: 0.85
    };
  }
  
  /**
   * 🎭 评估参与度
   */
  private static assessEngagement(content: string, statistics: ContentStatistics): DimensionScore {
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 75;
    
    // 检查内容吸引力
    if (content.includes('!') || content.includes('？')) {
      strengths.push('使用感叹号或问号增加表现力');
      score += 5;
    }
    
    // 检查描述性语言
    const descriptiveWords = ['美丽', '壮观', '独特', '精彩', '令人印象深刻'];
    const descriptiveCount = descriptiveWords.filter(word => content.includes(word)).length;
    
    if (descriptiveCount >= 2) {
      strengths.push('使用生动的描述性语言');
      score += 10;
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.05,
      issues,
      strengths,
      confidence: 0.7
    };
  }
  
  /**
   * ♿ 评估可访问性
   */
  private static assessAccessibility(content: string): DimensionScore {
    const issues: QualityIssue[] = [];
    const strengths: string[] = [];
    let score = 85;
    
    // 检查是否使用简单语言
    const complexWords = content.match(/[\u4e00-\u9fff]{4,}/g) || [];
    if (complexWords.length > content.split(/\s+/).length * 0.1) {
      issues.push({
        type: IssueType.ACCESSIBILITY_ISSUE,
        severity: IssueSeverity.LOW,
        description: '使用过多复杂词汇',
        suggestion: '使用更简单易懂的词汇',
        impact: 0.2
      });
      score -= 10;
    } else {
      strengths.push('语言简洁易懂');
    }
    
    return {
      score: Math.max(0, Math.min(100, score)),
      weight: 0.05,
      issues,
      strengths,
      confidence: 0.8
    };
  }
  
  // ===== 辅助方法 =====
  
  private static calculateOverallScore(dimensions: QualityDimensions): number {
    let totalScore = 0;
    let totalWeight = 0;
    
    for (const [key, dimension] of Object.entries(dimensions)) {
      totalScore += dimension.score * dimension.weight;
      totalWeight += dimension.weight;
    }
    
    return Math.round(totalScore / totalWeight);
  }
  
  private static determineGrade(score: number): QualityGrade {
    if (score >= 95) return QualityGrade.EXCELLENT;
    if (score >= 85) return QualityGrade.GOOD;
    if (score >= 75) return QualityGrade.SATISFACTORY;
    if (score >= 65) return QualityGrade.NEEDS_IMPROVEMENT;
    if (score >= 50) return QualityGrade.POOR;
    return QualityGrade.UNACCEPTABLE;
  }
  
  private static calculateComplexityScore(words: string[], sentences: string[]): number {
    const avgWordsPerSentence = words.length / Math.max(sentences.length, 1);
    const longWords = words.filter(word => word.length > 6).length;
    const longWordRatio = longWords / Math.max(words.length, 1);
    
    return Math.round((avgWordsPerSentence * 0.4 + longWordRatio * 100 * 0.6) * 10) / 10;
  }
  
  private static getRequiredElements(contentType: ContentType) {
    const elementMap: Record<ContentType, Array<{name: string, weight: number, critical: boolean}>> = {
      [ContentType.ACTIVITY_DESCRIPTION]: [
        { name: '活动名称', weight: 15, critical: true },
        { name: '地点信息', weight: 15, critical: true },
        { name: '时间信息', weight: 10, critical: false },
        { name: '价格信息', weight: 10, critical: false }
      ],
      [ContentType.LOCATION_INFO]: [
        { name: '地址', weight: 20, critical: true },
        { name: '营业时间', weight: 15, critical: true },
        { name: '联系方式', weight: 10, critical: false }
      ],
      [ContentType.TRANSPORTATION_GUIDE]: [
        { name: '交通方式', weight: 20, critical: true },
        { name: '时间', weight: 15, critical: true },
        { name: '费用', weight: 10, critical: false }
      ],
      [ContentType.CULTURAL_INSIGHT]: [
        { name: '文化背景', weight: 20, critical: true },
        { name: '历史信息', weight: 15, critical: false }
      ],
      [ContentType.PRACTICAL_TIP]: [
        { name: '具体建议', weight: 20, critical: true },
        { name: '注意事项', weight: 15, critical: false }
      ],
      [ContentType.REVIEW_SUMMARY]: [
        { name: '评分', weight: 15, critical: true },
        { name: '评价内容', weight: 15, critical: true }
      ]
    };
    
    return elementMap[contentType] || [];
  }
  
  private static hasElement(content: string, element: {name: string}): boolean {
    const patterns: Record<string, RegExp> = {
      '活动名称': /^.{2,20}/,
      '地点信息': /地址|位于|坐落/,
      '时间信息': /\d{1,2}:\d{2}|时间|营业/,
      '价格信息': /MYR|价格|费用|免费/,
      '地址': /地址[:：]|位于|坐落/,
      '营业时间': /\d{1,2}:\d{2}|营业时间|开放时间/,
      '联系方式': /电话|网站|邮箱/,
      '交通方式': /地铁|公交|步行|出租车/,
      '费用': /MYR|费用|价格/,
      '文化背景': /历史|文化|传统/,
      '历史信息': /年|朝代|建于/,
      '具体建议': /建议|推荐|注意/,
      '注意事项': /注意|小心|避免/,
      '评分': /\d+分|\d+星|评分/,
      '评价内容': /.{10,}/
    };
    
    const pattern = patterns[element.name];
    return pattern ? pattern.test(content) : false;
  }
  
  private static generateAnalysis(
    content: string,
    contentType: ContentType,
    statistics: ContentStatistics
  ): QualityAnalysis {
    return {
      statistics,
      language: {
        readabilityScore: 75,
        grammarIssues: 0,
        vocabularyLevel: 'intermediate',
        toneConsistency: 0.8
      },
      structure: {
        organizationScore: 80,
        flowScore: 75,
        coherenceScore: 85,
        headingStructure: 'good'
      },
      information: {
        factualAccuracy: 0.85,
        completenessScore: 0.8,
        relevanceScore: 0.9,
        uniquenessScore: 0.7
      },
      userExperience: {
        engagementScore: 75,
        usabilityScore: 80,
        accessibilityScore: 85,
        mobileOptimization: 0.9
      }
    };
  }
  
  private static generateRecommendations(
    dimensions: QualityDimensions,
    analysis: QualityAnalysis
  ): QualityRecommendation[] {
    const recommendations: QualityRecommendation[] = [];
    
    // 基于维度评分生成建议
    for (const [dimensionName, dimension] of Object.entries(dimensions)) {
      if (dimension.score < 80 && dimension.issues.length > 0) {
        const highPriorityIssues = dimension.issues.filter(issue => 
          issue.severity === IssueSeverity.HIGH || issue.severity === IssueSeverity.CRITICAL
        );
        
        if (highPriorityIssues.length > 0) {
          recommendations.push({
            priority: RecommendationPriority.HIGH,
            category: RecommendationCategory.CONTENT_ACCURACY,
            title: `改进${dimensionName}`,
            description: `当前${dimensionName}评分为${dimension.score}，需要重点关注`,
            actionItems: highPriorityIssues.map(issue => issue.suggestion),
            expectedImpact: 0.3,
            effort: EffortLevel.MEDIUM
          });
        }
      }
    }
    
    return recommendations;
  }
  
  private static createMetadata(contentType: ContentType): AssessmentMetadata {
    return {
      assessmentDate: new Date(),
      assessorVersion: '1.0.0',
      assessmentMethod: 'automated',
      processingTime: Math.random() * 1000 + 500, // 模拟处理时间
      confidence: 0.85,
      reviewRequired: false
    };
  }
}

// ===== 辅助接口 =====

interface ContentStatistics {
  wordCount: number;
  sentenceCount: number;
  paragraphCount: number;
  characterCount: number;
  averageWordsPerSentence: number;
  averageSentencesPerParagraph: number;
  readingTime: number;
  complexityScore: number;
}

interface LanguageQuality {
  readabilityScore: number;
  grammarIssues: number;
  vocabularyLevel: string;
  toneConsistency: number;
}

interface StructureQuality {
  organizationScore: number;
  flowScore: number;
  coherenceScore: number;
  headingStructure: string;
}

interface InformationQuality {
  factualAccuracy: number;
  completenessScore: number;
  relevanceScore: number;
  uniquenessScore: number;
}

interface UserExperienceQuality {
  engagementScore: number;
  usabilityScore: number;
  accessibilityScore: number;
  mobileOptimization: number;
}

interface AssessmentMetadata {
  assessmentDate: Date;
  assessorVersion: string;
  assessmentMethod: string;
  processingTime: number;
  confidence: number;
  reviewRequired: boolean;
}

export default ContentQualityAssessment;
