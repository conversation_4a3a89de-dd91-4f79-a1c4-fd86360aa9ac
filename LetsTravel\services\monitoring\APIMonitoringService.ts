/**
 * 📊 API监控服务
 * 
 * 实时监控API使用情况、成本和性能，提供预警和优化建议
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 接口定义 =====

export interface APIUsageMetrics {
  provider: string;
  endpoint: string;
  requestCount: number;
  successCount: number;
  errorCount: number;
  avgResponseTime: number;
  totalCost: number;
  costPerRequest: number;
  timestamp: Date;
  period: 'hour' | 'day' | 'week' | 'month';
}

export interface CostAlert {
  id: string;
  type: 'threshold' | 'spike' | 'trend' | 'budget';
  severity: 'low' | 'medium' | 'high' | 'critical';
  provider: string;
  currentCost: number;
  threshold: number;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

export interface PerformanceMetrics {
  endpoint: string;
  avgResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  errorRate: number;
  throughput: number;
  availability: number;
  timestamp: Date;
}

export interface OptimizationSuggestion {
  id: string;
  type: 'caching' | 'batching' | 'alternative' | 'rate_limiting';
  priority: 'low' | 'medium' | 'high';
  description: string;
  expectedSavings: number;
  implementation: string;
  effort: 'minimal' | 'low' | 'medium' | 'high';
}

export interface MonitoringConfig {
  costThresholds: Record<string, number>;
  performanceThresholds: {
    responseTime: number;
    errorRate: number;
    availability: number;
  };
  alertChannels: string[];
  reportingInterval: number;
  retentionPeriod: number;
}

// ===== API监控服务 =====

export class APIMonitoringService {
  private static instance: APIMonitoringService;
  private metrics: Map<string, APIUsageMetrics[]> = new Map();
  private alerts: CostAlert[] = [];
  private config: MonitoringConfig;
  private monitoringInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.config = {
      costThresholds: {
        'google_places': 100,
        'yelp': 50,
        'osm': 10,
        'nominatim': 5,
        'total_monthly': 500
      },
      performanceThresholds: {
        responseTime: 2000, // 2秒
        errorRate: 0.05,    // 5%
        availability: 0.99  // 99%
      },
      alertChannels: ['email', 'slack'],
      reportingInterval: 60 * 60 * 1000, // 1小时
      retentionPeriod: 30 * 24 * 60 * 60 * 1000 // 30天
    };

    this.startMonitoring();
  }

  /**
   * 🎯 获取单例实例
   */
  static getInstance(): APIMonitoringService {
    if (!APIMonitoringService.instance) {
      APIMonitoringService.instance = new APIMonitoringService();
    }
    return APIMonitoringService.instance;
  }

  /**
   * 📝 记录API调用
   */
  recordAPICall(
    provider: string,
    endpoint: string,
    responseTime: number,
    success: boolean,
    cost: number = 0
  ): void {
    
    const key = `${provider}_${endpoint}`;
    const now = new Date();
    const hourKey = `${key}_${now.getFullYear()}_${now.getMonth()}_${now.getDate()}_${now.getHours()}`;

    let metrics = this.metrics.get(hourKey);
    if (!metrics) {
      metrics = [];
      this.metrics.set(hourKey, metrics);
    }

    // 查找或创建当前小时的指标
    let currentMetric = metrics.find(m => 
      m.provider === provider && 
      m.endpoint === endpoint && 
      m.period === 'hour'
    );

    if (!currentMetric) {
      currentMetric = {
        provider,
        endpoint,
        requestCount: 0,
        successCount: 0,
        errorCount: 0,
        avgResponseTime: 0,
        totalCost: 0,
        costPerRequest: 0,
        timestamp: now,
        period: 'hour'
      };
      metrics.push(currentMetric);
    }

    // 更新指标
    currentMetric.requestCount++;
    if (success) {
      currentMetric.successCount++;
    } else {
      currentMetric.errorCount++;
    }

    // 更新平均响应时间
    currentMetric.avgResponseTime = (
      (currentMetric.avgResponseTime * (currentMetric.requestCount - 1) + responseTime) / 
      currentMetric.requestCount
    );

    currentMetric.totalCost += cost;
    currentMetric.costPerRequest = currentMetric.totalCost / currentMetric.requestCount;

    // 检查是否需要发送警报
    this.checkAlerts(currentMetric);
  }

  /**
   * 📊 获取使用统计
   */
  getUsageStats(
    provider?: string,
    period: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): APIUsageMetrics[] {
    
    const now = new Date();
    const stats: APIUsageMetrics[] = [];

    for (const [key, metrics] of this.metrics) {
      for (const metric of metrics) {
        if (provider && metric.provider !== provider) continue;
        if (this.isInPeriod(metric.timestamp, period, now)) {
          stats.push(metric);
        }
      }
    }

    return this.aggregateStats(stats, period);
  }

  /**
   * 💰 获取成本分析
   */
  getCostAnalysis(period: 'day' | 'week' | 'month' = 'month'): {
    totalCost: number;
    costByProvider: Record<string, number>;
    costTrend: Array<{ date: string; cost: number }>;
    projectedMonthlyCost: number;
    savings: {
      caching: number;
      optimization: number;
      alternatives: number;
    };
  } {
    
    const stats = this.getUsageStats(undefined, period);
    const totalCost = stats.reduce((sum, stat) => sum + stat.totalCost, 0);
    
    const costByProvider: Record<string, number> = {};
    for (const stat of stats) {
      costByProvider[stat.provider] = (costByProvider[stat.provider] || 0) + stat.totalCost;
    }

    // 计算趋势
    const costTrend = this.calculateCostTrend(stats, period);
    
    // 预测月度成本
    const projectedMonthlyCost = this.projectMonthlyCost(totalCost, period);

    // 计算节省
    const savings = this.calculatePotentialSavings(stats);

    return {
      totalCost,
      costByProvider,
      costTrend,
      projectedMonthlyCost,
      savings
    };
  }

  /**
   * ⚡ 获取性能指标
   */
  getPerformanceMetrics(provider?: string): PerformanceMetrics[] {
    const stats = this.getUsageStats(provider, 'hour');
    
    return stats.map(stat => ({
      endpoint: `${stat.provider}/${stat.endpoint}`,
      avgResponseTime: stat.avgResponseTime,
      p95ResponseTime: stat.avgResponseTime * 1.5, // 简化估算
      p99ResponseTime: stat.avgResponseTime * 2,   // 简化估算
      errorRate: stat.errorCount / stat.requestCount,
      throughput: stat.requestCount / 3600, // 每秒请求数
      availability: stat.successCount / stat.requestCount,
      timestamp: stat.timestamp
    }));
  }

  /**
   * 🚨 获取活跃警报
   */
  getActiveAlerts(): CostAlert[] {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  /**
   * ✅ 确认警报
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * 💡 获取优化建议
   */
  getOptimizationSuggestions(): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const stats = this.getUsageStats(undefined, 'week');

    // 分析高频调用的端点
    const highVolumeEndpoints = stats
      .filter(stat => stat.requestCount > 1000)
      .sort((a, b) => b.requestCount - a.requestCount);

    for (const stat of highVolumeEndpoints.slice(0, 5)) {
      // 缓存建议
      if (stat.avgResponseTime > 1000) {
        suggestions.push({
          id: `cache_${stat.provider}_${stat.endpoint}`,
          type: 'caching',
          priority: 'high',
          description: `为 ${stat.provider}/${stat.endpoint} 实现智能缓存`,
          expectedSavings: stat.totalCost * 0.6,
          implementation: '实现24小时TTL的智能缓存',
          effort: 'low'
        });
      }

      // 批量处理建议
      if (stat.requestCount > 5000) {
        suggestions.push({
          id: `batch_${stat.provider}_${stat.endpoint}`,
          type: 'batching',
          priority: 'medium',
          description: `为 ${stat.provider}/${stat.endpoint} 实现批量请求`,
          expectedSavings: stat.totalCost * 0.3,
          implementation: '将多个单独请求合并为批量请求',
          effort: 'medium'
        });
      }
    }

    // 替代方案建议
    const expensiveProviders = Object.entries(
      stats.reduce((acc, stat) => {
        acc[stat.provider] = (acc[stat.provider] || 0) + stat.totalCost;
        return acc;
      }, {} as Record<string, number>)
    ).filter(([_, cost]) => cost > 100);

    for (const [provider, cost] of expensiveProviders) {
      if (provider === 'google_places') {
        suggestions.push({
          id: `alt_${provider}`,
          type: 'alternative',
          priority: 'high',
          description: `将 ${provider} 替换为 Nominatim + OSM`,
          expectedSavings: cost * 0.8,
          implementation: '使用免费的Nominatim和OSM API',
          effort: 'medium'
        });
      }
    }

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * 📈 生成监控报告
   */
  generateReport(period: 'day' | 'week' | 'month' = 'week'): {
    summary: {
      totalRequests: number;
      totalCost: number;
      avgResponseTime: number;
      errorRate: number;
    };
    topEndpoints: Array<{ endpoint: string; requests: number; cost: number }>;
    alerts: CostAlert[];
    suggestions: OptimizationSuggestion[];
    trends: {
      requestTrend: string;
      costTrend: string;
      performanceTrend: string;
    };
  } {
    
    const stats = this.getUsageStats(undefined, period);
    const totalRequests = stats.reduce((sum, stat) => sum + stat.requestCount, 0);
    const totalCost = stats.reduce((sum, stat) => sum + stat.totalCost, 0);
    const avgResponseTime = stats.reduce((sum, stat) => sum + stat.avgResponseTime, 0) / stats.length;
    const errorRate = stats.reduce((sum, stat) => sum + stat.errorCount, 0) / totalRequests;

    const topEndpoints = stats
      .map(stat => ({
        endpoint: `${stat.provider}/${stat.endpoint}`,
        requests: stat.requestCount,
        cost: stat.totalCost
      }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10);

    return {
      summary: {
        totalRequests,
        totalCost,
        avgResponseTime,
        errorRate
      },
      topEndpoints,
      alerts: this.getActiveAlerts(),
      suggestions: this.getOptimizationSuggestions(),
      trends: {
        requestTrend: this.calculateTrend(stats, 'requestCount'),
        costTrend: this.calculateTrend(stats, 'totalCost'),
        performanceTrend: this.calculateTrend(stats, 'avgResponseTime')
      }
    };
  }

  // ===== 私有方法 =====

  private checkAlerts(metric: APIUsageMetrics): void {
    const threshold = this.config.costThresholds[metric.provider];
    
    if (threshold && metric.totalCost > threshold) {
      const alert: CostAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'threshold',
        severity: metric.totalCost > threshold * 2 ? 'critical' : 'high',
        provider: metric.provider,
        currentCost: metric.totalCost,
        threshold,
        message: `${metric.provider} 成本超过阈值: $${metric.totalCost.toFixed(2)} > $${threshold}`,
        timestamp: new Date(),
        acknowledged: false
      };

      this.alerts.push(alert);
      this.sendAlert(alert);
    }

    // 检查性能警报
    if (metric.avgResponseTime > this.config.performanceThresholds.responseTime) {
      const alert: CostAlert = {
        id: `perf_alert_${Date.now()}`,
        type: 'threshold',
        severity: 'medium',
        provider: metric.provider,
        currentCost: metric.avgResponseTime,
        threshold: this.config.performanceThresholds.responseTime,
        message: `${metric.provider}/${metric.endpoint} 响应时间过慢: ${metric.avgResponseTime}ms`,
        timestamp: new Date(),
        acknowledged: false
      };

      this.alerts.push(alert);
    }
  }

  private sendAlert(alert: CostAlert): void {
    console.warn(`🚨 API监控警报: ${alert.message}`);
    
    // 这里应该实现实际的警报发送逻辑
    // 例如发送邮件、Slack通知等
  }

  private isInPeriod(timestamp: Date, period: string, now: Date): boolean {
    const diff = now.getTime() - timestamp.getTime();
    
    switch (period) {
      case 'hour':
        return diff <= 60 * 60 * 1000;
      case 'day':
        return diff <= 24 * 60 * 60 * 1000;
      case 'week':
        return diff <= 7 * 24 * 60 * 60 * 1000;
      case 'month':
        return diff <= 30 * 24 * 60 * 60 * 1000;
      default:
        return true;
    }
  }

  private aggregateStats(stats: APIUsageMetrics[], period: string): APIUsageMetrics[] {
    const aggregated = new Map<string, APIUsageMetrics>();

    for (const stat of stats) {
      const key = `${stat.provider}_${stat.endpoint}`;
      const existing = aggregated.get(key);

      if (existing) {
        existing.requestCount += stat.requestCount;
        existing.successCount += stat.successCount;
        existing.errorCount += stat.errorCount;
        existing.totalCost += stat.totalCost;
        existing.avgResponseTime = (existing.avgResponseTime + stat.avgResponseTime) / 2;
        existing.costPerRequest = existing.totalCost / existing.requestCount;
      } else {
        aggregated.set(key, { ...stat, period: period as any });
      }
    }

    return Array.from(aggregated.values());
  }

  private calculateCostTrend(stats: APIUsageMetrics[], period: string): Array<{ date: string; cost: number }> {
    const trend: Array<{ date: string; cost: number }> = [];
    const groupedByDate = new Map<string, number>();

    for (const stat of stats) {
      const dateKey = stat.timestamp.toISOString().split('T')[0];
      groupedByDate.set(dateKey, (groupedByDate.get(dateKey) || 0) + stat.totalCost);
    }

    for (const [date, cost] of groupedByDate) {
      trend.push({ date, cost });
    }

    return trend.sort((a, b) => a.date.localeCompare(b.date));
  }

  private projectMonthlyCost(currentCost: number, period: string): number {
    const multipliers = {
      day: 30,
      week: 4.33,
      month: 1
    };

    return currentCost * (multipliers[period as keyof typeof multipliers] || 1);
  }

  private calculatePotentialSavings(stats: APIUsageMetrics[]): {
    caching: number;
    optimization: number;
    alternatives: number;
  } {
    const totalCost = stats.reduce((sum, stat) => sum + stat.totalCost, 0);

    return {
      caching: totalCost * 0.4,      // 40% 通过缓存节省
      optimization: totalCost * 0.2, // 20% 通过优化节省
      alternatives: totalCost * 0.6  // 60% 通过替代方案节省
    };
  }

  private calculateTrend(stats: APIUsageMetrics[], field: keyof APIUsageMetrics): string {
    if (stats.length < 2) return 'stable';

    const values = stats.map(stat => stat[field] as number).filter(v => typeof v === 'number');
    if (values.length < 2) return 'stable';

    const first = values[0];
    const last = values[values.length - 1];
    const change = (last - first) / first;

    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  private startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      this.performPeriodicChecks();
    }, this.config.reportingInterval);
  }

  private performPeriodicChecks(): void {
    // 清理过期数据
    this.cleanupOldData();
    
    // 检查月度预算
    this.checkMonthlyBudget();
    
    // 生成自动报告
    if (new Date().getHours() === 9) { // 每天上午9点
      const report = this.generateReport('day');
      console.log('📊 日度API监控报告:', report);
    }
  }

  private cleanupOldData(): void {
    const cutoff = Date.now() - this.config.retentionPeriod;
    
    for (const [key, metrics] of this.metrics) {
      const filtered = metrics.filter(metric => metric.timestamp.getTime() > cutoff);
      if (filtered.length === 0) {
        this.metrics.delete(key);
      } else {
        this.metrics.set(key, filtered);
      }
    }

    // 清理旧警报
    this.alerts = this.alerts.filter(alert => 
      alert.timestamp.getTime() > cutoff || !alert.acknowledged
    );
  }

  private checkMonthlyBudget(): void {
    const monthlyStats = this.getUsageStats(undefined, 'month');
    const totalMonthlyCost = monthlyStats.reduce((sum, stat) => sum + stat.totalCost, 0);
    const threshold = this.config.costThresholds.total_monthly;

    if (totalMonthlyCost > threshold * 0.8) { // 80% 预警
      const alert: CostAlert = {
        id: `budget_alert_${Date.now()}`,
        type: 'budget',
        severity: totalMonthlyCost > threshold ? 'critical' : 'high',
        provider: 'all',
        currentCost: totalMonthlyCost,
        threshold,
        message: `月度总成本接近预算: $${totalMonthlyCost.toFixed(2)} / $${threshold}`,
        timestamp: new Date(),
        acknowledged: false
      };

      this.alerts.push(alert);
      this.sendAlert(alert);
    }
  }

  /**
   * ⚙️ 更新配置
   */
  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 📊 导出数据
   */
  exportData(format: 'json' | 'csv' = 'json'): string {
    const data = {
      metrics: Array.from(this.metrics.entries()),
      alerts: this.alerts,
      config: this.config
    };

    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else {
      // 简化的CSV导出
      return 'CSV export not implemented';
    }
  }

  /**
   * 🔧 销毁实例
   */
  destroy(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.metrics.clear();
    this.alerts = [];
  }
}

export default APIMonitoringService;
