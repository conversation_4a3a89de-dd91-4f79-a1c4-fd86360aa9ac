{"$schema": "https://json.schemastore.org/package.json", "name": "trekmate", "devDependencies": {"@babel/plugin-transform-export-namespace-from": "7.25.9", "@expo/config-plugins": "9.0.17", "@react-native-community/cli": "^18.0.0", "@react-native-community/cli-platform-android": "^18.0.0", "@react-native-community/cli-platform-ios": "^18.0.0", "@react-native/metro-config": "^0.76.9", "@testing-library/jest-native": "5.4.3", "@testing-library/react-native": "13.2.0", "@types/expo__vector-icons": "9.0.1", "@types/jest": "29.5.14", "@types/node": "22.13.13", "@types/react": "~18.3.12", "@types/react-test-renderer": "19.0.0", "@typescript-eslint/eslint-plugin": "8.35.1", "@typescript-eslint/parser": "8.35.1", "babel-plugin-module-resolver": "5.0.2", "babel-plugin-transform-remove-console": "6.9.4", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.5.1", "expo-doctor": "latest", "expo-module-scripts": "4.0.5", "jest": "~29.7.0", "jest-expo": "~52.0.6", "jest-watch-typeahead": "3.0.1", "metro": "^0.81.0", "metro-core": "^0.81.0", "metro-runtime": "^0.81.0", "msw": "2.10.2", "patch-package": "8.0.0", "postinstall-postinstall": "2.1.0", "prettier": "3.6.2", "react-native-dotenv": "3.4.11", "react-native-svg-transformer": "1.5.0", "ts-jest": "29.4.0", "typescript": "5.3.3"}, "dependencies": {"@babel/preset-typescript": "7.27.0", "@expo/webpack-config": "0.12.52", "@ismaelmoreiraa/vision-camera-ocr": "3.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "1.16.2", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/geolocation": "3.4.0", "@react-native-community/netinfo": "11.4.1", "@react-native-ml-kit/text-recognition": "1.5.2", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "7.4.2", "@react-navigation/native": "7.1.14", "@react-navigation/native-stack": "7.3.21", "@react-three/drei": "10.0.7", "@react-three/fiber": "9.1.2", "@reduxjs/toolkit": "2.8.2", "@supabase/supabase-js": "2.49.4", "@tanstack/react-query": "5.81.5", "ar-bridge": "file:../ARBridge", "axios": "1.8.4", "dotenv": "17.2.1", "expo": "~52.0.47", "expo-av": "~15.0.2", "expo-blur": "14.0.3", "expo-camera": "16.0.18", "expo-clipboard": "7.0.1", "expo-constants": "17.0.8", "expo-dev-client": "~5.0.20", "expo-file-system": "~18.0.12", "expo-font": "13.0.4", "expo-gl": "~15.0.5", "expo-haptics": "14.0.1", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "7.0.5", "expo-location": "18.0.10", "expo-media-library": "17.0.6", "expo-modules-core": "2.2.3", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-sensors": "~14.0.2", "expo-speech": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "2.0.1", "expo-symbols": "0.2.2", "expo-system-ui": "~4.0.9", "fuse.js": "7.1.0", "lodash.debounce": "4.0.8", "metro-resolver": "^0.81.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "2.20.2", "react-native-maps": "1.18.0", "react-native-markdown-display": "7.0.2", "react-native-paper": "5.13.1", "react-native-reanimated": "3.16.7", "react-native-safe-area-context": "4.12.0", "react-native-screens": "4.4.0", "react-native-svg": "15.8.0", "react-native-vision-camera": "4.6.4", "react-native-vision-camera-text-recognition": "3.1.1", "react-native-web": "~0.19.10", "react-native-webview": "13.12.5", "react-native-worklets-core": "1.5.0", "react-redux": "9.2.0", "tesseract.js": "6.0.1", "three": "0.176.0", "zod": "4.0.10"}, "main": "index_traditional.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "check-deps": "npx expo-doctor", "postinstall": "patch-package", "clean": "expo start --clear", "clean-install": "rm -rf node_modules && npm install", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "validate": "ts-node scripts/validate-app.ts", "verify-fixes": "ts-node scripts/verify-integration-fixes.ts", "test:apis": "node testAPIs.js", "health-check": "ts-node -e \"import('./utils/health/HealthChecker').then(m => m.healthChecker.performHealthCheck().then(console.log))\"", "performance-report": "ts-node -e \"import('./utils/performance/PerformanceMonitor').then(m => console.log(m.performanceMonitor.getPerformanceReport()))\"", "build": "expo build", "optimize": "npm run clean && npm run type-check"}, "private": true, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1", "@types/react": "18.3.12", "@react-native-async-storage/async-storage": "1.23.1", "metro": "^0.81.0", "metro-resolver": "^0.81.0", "metro-core": "^0.81.0", "metro-runtime": "^0.81.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-maps", "@expo/webpack-config", "i18next"]}}}, "version": "1.0.0"}