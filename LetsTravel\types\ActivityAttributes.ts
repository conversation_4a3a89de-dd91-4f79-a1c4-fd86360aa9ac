/**
 * 🏷️ 活动属性类型定义
 * 
 * 用于替换模糊标签系统，提供真实的活动属性信息
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 核心活动属性接口 =====

/**
 * 🎯 真实活动属性
 * 替换"精选体验"、"超值选择"等模糊标签
 */
export interface RealActivityAttributes {
  /** 营业时间信息 */
  operatingHours: OperatingHours;
  
  /** 评分信息 */
  rating: RatingInfo;
  
  /** 价格区间信息 */
  priceRange: PriceRangeInfo;
  
  /** 可用性状态 */
  availability: AvailabilityStatus;
  
  /** 预订要求 */
  bookingRequirement: BookingRequirement;
  
  /** 季节性信息 */
  seasonality: SeasonalityInfo;
}

/**
 * 🕐 营业时间信息
 */
export interface OperatingHours {
  /** 是否当前开放 */
  isCurrentlyOpen: boolean;
  
  /** 今日营业时间 */
  todayHours: string; // "09:00-18:00" | "24小时营业" | "暂停营业"
  
  /** 一周营业时间 */
  weeklyHours: WeeklyHours;
  
  /** 特殊营业时间说明 */
  specialNotes?: string; // "节假日时间可能调整"
}

/**
 * 📅 一周营业时间
 */
export interface WeeklyHours {
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
}

/**
 * ⭐ 评分信息
 */
export interface RatingInfo {
  /** 总体评分 (1-5) */
  overallRating: number;
  
  /** 评价总数 */
  reviewCount: number;
  
  /** 评分来源 */
  source: 'google' | 'tripadvisor' | 'yelp' | 'local' | 'estimated';
  
  /** 子评分 */
  subRatings?: {
    service?: number;      // 服务质量
    value?: number;        // 性价比
    cleanliness?: number;  // 清洁度
    location?: number;     // 位置便利性
    atmosphere?: number;   // 氛围
  };
  
  /** 评分趋势 */
  trend?: 'improving' | 'stable' | 'declining';
}

/**
 * 💰 价格区间信息
 */
export interface PriceRangeInfo {
  /** 价格等级 */
  level: PriceLevel;
  
  /** 具体价格范围 */
  range: {
    min: number;
    max: number;
    currency: string;
  };
  
  /** 价格类型 */
  type: 'per_person' | 'per_group' | 'per_hour' | 'per_item' | 'entrance_fee';
  
  /** 价格说明 */
  description: string; // "成人票价" | "套餐价格" | "人均消费"
  
  /** 是否包含额外费用 */
  includesExtras: boolean;
  
  /** 额外费用说明 */
  extraFeesNote?: string;
}

/**
 * 💰 价格等级枚举
 */
export enum PriceLevel {
  FREE = 'free',           // 免费 (RM0)
  BUDGET = 'budget',       // 经济 (RM1-25)
  MODERATE = 'moderate',   // 适中 (RM26-60)
  EXPENSIVE = 'expensive', // 较贵 (RM61-100)
  LUXURY = 'luxury'        // 奢华 (RM100+)
}

/**
 * 🟢 可用性状态
 */
export interface AvailabilityStatus {
  /** 当前状态 */
  status: 'available' | 'busy' | 'closed' | 'fully_booked' | 'seasonal_closed';
  
  /** 状态描述 */
  description: string;
  
  /** 下次可用时间 */
  nextAvailable?: string;
  
  /** 拥挤程度 (1-5, 1=空闲, 5=非常拥挤) */
  crowdLevel: number;
  
  /** 最佳访问时间建议 */
  bestTimeToVisit: string[];
}

/**
 * 📋 预订要求
 */
export interface BookingRequirement {
  /** 是否需要预订 */
  required: boolean;
  
  /** 预订类型 */
  type?: 'advance' | 'same_day' | 'walk_in' | 'recommended';
  
  /** 提前预订时间 */
  advanceTime?: string; // "提前1天" | "提前1周"
  
  /** 预订平台 */
  platforms?: string[];
  
  /** 预订说明 */
  notes?: string;
}

/**
 * 🌤️ 季节性信息
 */
export interface SeasonalityInfo {
  /** 最佳季节 */
  bestSeasons: string[];
  
  /** 当前季节适宜度 (1-5) */
  currentSeasonRating: number;
  
  /** 季节性说明 */
  seasonalNotes: string;
  
  /** 天气依赖性 */
  weatherDependent: boolean;
}

// ===== 工具函数类型 =====

/**
 * 🎨 属性显示配置
 */
export interface AttributeDisplayConfig {
  /** 显示模式 */
  mode: 'compact' | 'detailed' | 'minimal';
  
  /** 主题色彩 */
  theme: 'light' | 'dark' | 'auto';
  
  /** 显示的属性类型 */
  showAttributes: AttributeType[];
  
  /** 本地化语言 */
  locale: string;
}

/**
 * 🏷️ 属性类型枚举
 */
export enum AttributeType {
  OPERATING_HOURS = 'operating_hours',
  RATING = 'rating',
  PRICE_RANGE = 'price_range',
  AVAILABILITY = 'availability',
  BOOKING = 'booking',
  SEASONALITY = 'seasonality'
}

// ===== 错误处理 =====

/**
 * ❌ 属性获取错误
 */
export class AttributeError extends Error {
  constructor(
    message: string,
    public attributeType: AttributeType,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'AttributeError';
  }
}

// ===== 导出所有类型 =====
export type {
  RealActivityAttributes,
  OperatingHours,
  WeeklyHours,
  RatingInfo,
  PriceRangeInfo,
  AvailabilityStatus,
  BookingRequirement,
  SeasonalityInfo,
  AttributeDisplayConfig
};
