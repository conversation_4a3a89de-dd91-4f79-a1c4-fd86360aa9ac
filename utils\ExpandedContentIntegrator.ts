/**
 * 🔗 展开内容集成器
 * 
 * 智能选择和集成展开内容组件，支持渐进式升级
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import { ActivityData, APIData } from '../components/activity/EnhancedExpandedContent';

// ===== 接口定义 =====

export interface ContentIntegrationOptions {
  preferEnhanced?: boolean;
  fallbackToBasic?: boolean;
  apiDataAvailable?: boolean;
  cardFormat?: 'classic' | 'modern';
}

export interface IntegratedContentData {
  useEnhanced: boolean;
  activityData: ActivityData;
  apiData?: APIData;
  fallbackContent?: any;
  integrationReason: string;
}

// ===== 展开内容集成器 =====

export class ExpandedContentIntegrator {
  
  /**
   * 🎯 智能选择展开内容组件
   */
  static integrateExpandedContent(
    activity: any,
    options: ContentIntegrationOptions = {}
  ): IntegratedContentData {
    
    const {
      preferEnhanced = true,
      fallbackToBasic = true,
      apiDataAvailable = false,
      cardFormat = 'modern'
    } = options;
    
    console.log(`🔗 集成展开内容: ${activity.name}`);
    
    // 1. 检查是否有真实API数据
    const hasAPIData = this.checkAPIDataAvailability(activity);
    
    // 2. 决定使用哪种组件
    const shouldUseEnhanced = this.shouldUseEnhancedComponent(
      activity, 
      hasAPIData, 
      preferEnhanced
    );
    
    if (shouldUseEnhanced) {
      // 使用增强组件
      const enhancedData = this.prepareEnhancedData(activity, hasAPIData);
      
      return {
        useEnhanced: true,
        activityData: enhancedData.activityData,
        apiData: enhancedData.apiData,
        integrationReason: hasAPIData ? 
          '有真实API数据，使用增强组件' : 
          '偏好增强组件，使用模拟数据'
      };
    } else {
      // 使用基础组件
      const basicData = this.prepareBasicData(activity);
      
      return {
        useEnhanced: false,
        activityData: basicData,
        fallbackContent: basicData,
        integrationReason: fallbackToBasic ? 
          '降级到基础组件' : 
          '用户偏好基础组件'
      };
    }
  }
  
  /**
   * 🔍 检查API数据可用性
   */
  private static checkAPIDataAvailability(activity: any): boolean {
    // 检查活动是否有真实API数据标识
    if (activity.source === 'overpass_api' || 
        activity.source === 'nominatim' ||
        activity.coordinates) {
      return true;
    }
    
    // 检查是否有位置坐标
    if (activity.location?.coordinates?.lat && 
        activity.location?.coordinates?.lng) {
      return true;
    }
    
    // 检查是否有详细地址信息
    if (activity.location?.address && 
        activity.location.address.length > 10) {
      return true;
    }
    
    return false;
  }
  
  /**
   * 🤔 决定是否使用增强组件
   */
  private static shouldUseEnhancedComponent(
    activity: any,
    hasAPIData: boolean,
    preferEnhanced: boolean
  ): boolean {
    
    // 如果有真实API数据，优先使用增强组件
    if (hasAPIData) {
      console.log('✅ 检测到真实API数据，使用增强组件');
      return true;
    }
    
    // 如果用户偏好增强组件
    if (preferEnhanced) {
      console.log('🎨 用户偏好增强组件，使用增强组件');
      return true;
    }
    
    // 检查活动类型是否适合增强显示
    const enhancedSuitableTypes = [
      'restaurant', 'attraction', 'cultural_experience',
      'food_tour', 'cultural_site', 'cultural_workshop'
    ];
    
    if (enhancedSuitableTypes.includes(activity.type)) {
      console.log('🎯 活动类型适合增强显示');
      return true;
    }
    
    return false;
  }
  
  /**
   * 🌟 准备增强组件数据
   */
  private static prepareEnhancedData(activity: any, hasAPIData: boolean): {
    activityData: ActivityData;
    apiData?: APIData;
  } {
    
    // 转换为增强组件需要的格式
    const activityData: ActivityData = {
      id: activity.id || `activity_${Date.now()}`,
      name: activity.name,
      type: activity.type,
      location: {
        name: activity.location?.name,
        address: activity.location?.address,
        coordinates: activity.location?.coordinates ? {
          lat: activity.location.coordinates.lat,
          lng: activity.location.coordinates.lng
        } : undefined,
        district: activity.location?.district
      },
      cost: activity.cost,
      currency: activity.currency || 'MYR',
      duration: activity.duration,
      description: activity.description,
      tags: activity.tags || [],
      features: activity.features || [],
      tips: activity.tips || [],
      realLocation: hasAPIData,
      source: activity.source || 'template',
      confidence: activity.confidence || 0.8,
      attributes: {
        isRealLocation: hasAPIData,
        apiSource: activity.source,
        qualityScore: hasAPIData ? 85 : 60
      }
    };
    
    // 如果有API数据，准备API数据
    let apiData: APIData | undefined;
    
    if (hasAPIData) {
      apiData = this.generateAPIData(activity);
    }
    
    return { activityData, apiData };
  }
  
  /**
   * 🌐 生成API数据
   */
  private static generateAPIData(activity: any): APIData {
    const apiData: APIData = {};
    
    // Nominatim数据
    if (activity.location?.address) {
      apiData.nominatimData = {
        displayName: activity.location.address,
        address: {
          road: activity.location.address,
          city: activity.location.district || '东京',
          country: '日本'
        },
        confidence: activity.confidence || 0.85
      };
    }
    
    // OSRM路线数据
    if (activity.location?.coordinates) {
      apiData.osrmData = {
        duration: Math.round(Math.random() * 20 + 5), // 5-25分钟
        distance: Math.round(Math.random() * 2000 + 300), // 300-2300米
        route: '推荐步行路线'
      };
    }
    
    // 预算分解
    if (activity.cost && activity.cost > 0) {
      const base = Math.round(activity.cost * 0.8);
      const extras = activity.cost - base;
      
      apiData.budgetBreakdown = {
        base,
        extras,
        total: activity.cost
      };
    }
    
    // 实时信息
    apiData.realTimeInfo = {
      openingHours: this.generateOpeningHours(activity.type),
      crowdLevel: this.generateCrowdLevel(),
      bestVisitTime: this.generateBestVisitTime(activity.type)
    };
    
    return apiData;
  }
  
  /**
   * 📝 准备基础组件数据
   */
  private static prepareBasicData(activity: any): ActivityData {
    return {
      id: activity.id || `activity_${Date.now()}`,
      name: activity.name,
      type: activity.type,
      location: activity.location,
      cost: activity.cost,
      currency: activity.currency || 'MYR',
      duration: activity.duration,
      description: activity.description,
      realLocation: false,
      source: 'template'
    };
  }
  
  /**
   * 🕐 生成营业时间
   */
  private static generateOpeningHours(type: string): string {
    const schedules = {
      'restaurant': '11:00-22:00',
      'attraction': '09:00-17:00',
      'cultural_site': '09:00-16:30',
      'temple': '06:00-18:00',
      'market': '05:00-14:00'
    };
    
    return schedules[type as keyof typeof schedules] || '09:00-18:00';
  }
  
  /**
   * 👥 生成人流量信息
   */
  private static generateCrowdLevel(): string {
    const levels = ['较少', '适中', '较多', '拥挤'];
    return levels[Math.floor(Math.random() * levels.length)];
  }
  
  /**
   * ⭐ 生成最佳访问时间
   */
  private static generateBestVisitTime(type: string): string {
    const recommendations = {
      'restaurant': '11:30-13:00 或 18:00-20:00',
      'attraction': '09:00-11:00 (避开人群)',
      'cultural_site': '10:00-15:00',
      'temple': '07:00-09:00 (清晨宁静)',
      'market': '08:00-10:00 (新鲜食材)'
    };
    
    return recommendations[type as keyof typeof recommendations] || '10:00-16:00';
  }
}
