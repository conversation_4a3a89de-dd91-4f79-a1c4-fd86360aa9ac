/**
 * Trekmate 4.0 - AI行程规划页面
 * 集成AI Plan功能到Journey模块
 */

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import DateTimePicker from '@react-native-community/datetimepicker';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { JOURNEY, COMMON, AI_ASSISTANT } from '../../constants/Strings';
import { coreServiceRegistry } from '../../services/core/CoreServiceRegistry';
import type { NativeStackScreenProps } from '../../types/Navigation';
import type { RootStackParamList } from '../../types/Navigation';
import type { Journey, Location } from '../../types/CoreServices';
import { APIKeyDebugger } from '../../components/debug/APIKeyDebugger';

// 导入 Google Places 组件和配置
import GooglePlacesInput from '../../components/input/GooglePlacesInput';
import ModernDestinationInput from '../../components/input/ModernDestinationInput';
import { GOOGLE_PLACES_CONFIG, isGooglePlacesConfigured, getCountryCodeFromLocation } from '../../config/GooglePlacesConfig';

// 导入时长选择器组件
import DurationSelector from '../../components/input/DurationSelector';

// 导入货币选择器组件和配置
import CurrencySelector from '../../components/input/CurrencySelector';
import { CurrencyInfo, SUPPORTED_CURRENCIES, DEFAULT_CURRENCY } from '../../config/CurrencyConfig';

// 导入AI助手屏幕感知Hook
import { useScreenAware, useGlobalAIAssistant } from '../../components/ai/GlobalAIAssistantManager';

// 导入现代化选项卡片组件
import ModernOptionCard, { OptionItem } from '../../components/input/ModernOptionCard';

// 导入智能偏好选择系统
import EnhancedAIPlanningInterface from '../../components/ai/EnhancedAIPlanningInterface';

// 导入预算智能分析系统
import { postProcessBudget } from '../../services/budget/AutoBudgetAdjuster';
import { SeasonDateUtils, type SeasonChinese } from '../../utils/SeasonDateUtils';

// 使用动态导入来避免undefined模块错误

// 季节选项
const SEASON_OPTIONS = [
  { value: '春季', label: '春季', emoji: '🌸', gradient: ['#4CAF50', '#81C784'] },
  { value: '夏季', label: '夏季', emoji: '☀️', gradient: ['#FF9800', '#FFB74D'] },
  { value: '秋季', label: '秋季', emoji: '🍂', gradient: ['#FF5722', '#FF8A65'] },
  { value: '冬季', label: '冬季', emoji: '❄️', gradient: ['#2196F3', '#64B5F6'] },
];

// 旅客类型
const TRAVELER_TYPES = {
  adults: { label: '成人', description: '12岁以上' },
  children: { label: '儿童', description: '2-11岁' },
  infants: { label: '婴儿', description: '0-2岁' },
};

// 旅行风格选项
const TRAVEL_STYLE_OPTIONS = [
  { key: 'cultural', label: '文化深度', icon: 'library', description: '博物馆、历史遗迹' },
  { key: 'adventure', label: '冒险刺激', icon: 'trail-sign', description: '户外运动、极限体验' },
  { key: 'relaxation', label: '休闲度假', icon: 'sunny', description: '海滩、温泉、SPA' },
  { key: 'foodie', label: '美食探索', icon: 'restaurant', description: '当地美食、米其林餐厅' },
  { key: 'shopping', label: '购物天堂', icon: 'bag', description: '商场、市集、特色商品' },
  { key: 'nightlife', label: '夜生活', icon: 'wine', description: '酒吧、夜市、娱乐' },
];

// 住宿选项
const ACCOMMODATION_OPTIONS = [
  { key: 'luxury_hotel', label: '豪华酒店', icon: 'diamond', description: '5星级服务' },
  { key: 'mid_range_hotel', label: '中档酒店', icon: 'bed', description: '舒适性价比' },
  { key: 'budget_hotel', label: '经济酒店', icon: 'home', description: '基础设施' },
  { key: 'boutique_hotel', label: '精品酒店', icon: 'flower', description: '独特设计' },
  { key: 'resort', label: '度假村', icon: 'business', description: '一站式度假' },
  { key: 'hostel', label: '青年旅社', icon: 'people', description: '社交氛围' },
];

// 交通选项
const TRANSPORT_OPTIONS = [
  { key: 'walking', label: '步行', icon: 'walk', description: '慢节奏探索' },
  { key: 'public', label: '公共交通', icon: 'bus', description: '地铁、公交' },
  { key: 'taxi', label: '出租车', icon: 'car', description: '便捷出行' },
  { key: 'rental_car', label: '租车', icon: 'car-sport', description: '自由驾驶' },
  { key: 'bike', label: '自行车', icon: 'bicycle', description: '环保健康' },
  { key: 'flight', label: '飞机', icon: 'airplane', description: '长途快速' },
];

// Ultra Think桥接器服务实例
let ultraThinkBridgeService: any = null;

// 延迟加载Ultra Think桥接器服务 - 强制刷新版本
const loadUltraThinkService = async () => {
  // 🔧 强制重新加载，清除缓存
  try {
    console.log('🔄 强制重新加载Ultra Think Master Solver服务...');
    const module = await import('../../services/UltraThinkBridge');
    ultraThinkBridgeService = module.ultraThinkBridge;
    await ultraThinkBridgeService.initialize();
    console.log('✅ Ultra Think Master Solver桥接器服务加载成功');
    console.log('🎯 当前版本: Master Solver集成版');
  } catch (error) {
    console.error('❌ Ultra Think桥接器服务加载失败:', error);
    throw new Error('无法加载Ultra Think规划服务');
  }
  return ultraThinkBridgeService;
};

interface AIJourneyParams {
  destination: string;
  duration: string;
  season: string;
  budget: number;
  budgetCurrency: string;
  preferences: string;
  travelers: {
    adults: number;
    children: number;
    infants: number;
  };
  travelStyle?: string[]; // 改为数组支持多选
  accommodation?: string[]; // 改为数组支持多选
  transport?: string[];
}

type AIJourneyPlanningScreenProps = NativeStackScreenProps<RootStackParamList, 'AIJourneyPlanning'>;



const AIJourneyPlanningScreen = ({ navigation }: AIJourneyPlanningScreenProps) => {
  const insets = useSafeAreaInsets();
  const { forceShowAssistant } = useGlobalAIAssistant();

  // 🛡️ Ultra Safe 日期序列化函数 (全局使用)
  const safeDateToString = useCallback((dateValue: any): string => {
    try {
      if (!dateValue) {
        return new Date().toISOString();
      }

      if (typeof dateValue === 'string') {
        const parsed = new Date(dateValue);
        if (isNaN(parsed.getTime())) {
          console.warn('⚠️ 无效日期字符串:', dateValue);
          return new Date().toISOString();
        }
        return parsed.toISOString();
      }

      if (dateValue instanceof Date) {
        if (isNaN(dateValue.getTime())) {
          console.warn('⚠️ 无效Date对象:', dateValue);
          return new Date().toISOString();
        }
        return dateValue.toISOString();
      }

      console.warn('⚠️ 未知日期类型:', typeof dateValue, dateValue);
      return new Date().toISOString();
    } catch (error) {
      console.error('❌ 日期序列化失败:', error, dateValue);
      return new Date().toISOString();
    }
  }, []);

  // AI规划参数状态
  const [aiParams, setAiParams] = useState({
    destination: '',
    duration: '3天',
    season: '春季',
    startDate: new Date(), // 🔧 修复：添加startDate字段
    budget: 3000,
    budgetCurrency: 'MYR',
    preferences: '',
    travelers: {
      adults: 2,
      children: 0,
      infants: 0,
    },
    travelStyle: ['cultural'], // 默认选择文化深度
    accommodation: ['mid_range_hotel'], // 默认选择中档酒店
    transport: ['walking', 'public'], // 默认选择步行和公共交通
  });

  // 🔧 季节到日期的转换函数 - 使用统一工具
  const getSeasonDate = (season: string): Date => {
    try {
      const seasonDateResult = SeasonDateUtils.getSmartSeasonDate(season as SeasonChinese);
      console.log(`📅 季节日期选择:`, SeasonDateUtils.formatSeasonDate(seasonDateResult));
      return seasonDateResult.date;
    } catch (error) {
      console.error('❌ 季节日期转换失败:', error);
      return new Date();
    }
  };

  // 更新参数 (提前声明，供后续回调依赖)
  const updateParams = useCallback((key: string, value: any) => {
    // 🔧 修复：当季节改变时，自动更新开始日期
    if (key === 'season') {
      const seasonDate = getSeasonDate(value);
      console.log(`📅 季节${value}对应日期:`, seasonDate.toDateString());
      setAiParams((prev: any) => ({
        ...prev,
        [key]: value,
        startDate: seasonDate // 自动设置对应季节的日期
      }));
    } else {
      setAiParams((prev: any) => ({ ...prev, [key]: value }));
    }
  }, []);

  const updateTravelers = useCallback((type: string, value: number) => {
    setAiParams((prev: any) => ({
      ...prev,
      travelers: { ...prev.travelers, [type]: Math.max(0, value) }
    }));
  }, []);

  // 调试：打印初始状态
  React.useEffect(() => {
    console.log('🔍 AI参数初始状态:', {
      travelStyle: aiParams.travelStyle,
      accommodation: aiParams.accommodation,
      transport: aiParams.transport
    });
  }, []);

  // UI状态
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStep, setGenerationStep] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);

  // 🎨 固定使用Modern格式 (简化开发复杂度)
  const cardFormat = 'modern';

  // 智能偏好选择状态
  const [useSmartPreferences, setUseSmartPreferences] = useState(false);
  const [smartPreferencesReady, setSmartPreferencesReady] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Google Places 相关状态
  const [countryCode, setCountryCode] = useState('MY');
  const [selectedPlace, setSelectedPlace] = useState(null);
  const [isPlacesEnabled, setIsPlacesEnabled] = useState(false);

  // 启用AI助手屏幕感知 - 使用useMemo稳定对象引用，防止无限循环
  const screenContext = useMemo(() => ({
    screen: 'AIJourneyPlanning',
    destination: aiParams.destination,
    duration: aiParams.duration,
    budget: aiParams.budget,
    isGenerating,
    generationStep,
  }), [aiParams.destination, aiParams.duration, aiParams.budget, isGenerating, generationStep]);

  useScreenAware('AIJourneyPlanning', screenContext);

  // 强制显示AI助手悬浮按钮
  useEffect(() => {
    const timer = setTimeout(() => {
      forceShowAssistant();
    }, 1000); // 延迟1秒确保组件完全加载

    return () => clearTimeout(timer);
  }, [forceShowAssistant]);

  // 货币相关状态
  const [currentCurrency, setCurrentCurrency] = useState(SUPPORTED_CURRENCIES[DEFAULT_CURRENCY]);

  // 已移除预算建议逻辑，以上状态变量不再需要

  // 预算充足性 useEffect 已删除

  // 🎯 处理预算建议接受
  const handleAcceptRecommendation = useCallback((type: 'increase_budget' | 'adjust_itinerary' | 'optimize_choices', newBudget?: number) => {
    console.log('🎯 接受预算建议:', type, newBudget);

    if (type === 'increase_budget' && newBudget) {
      // 调整预算
      updateParams('budget', newBudget);
      Alert.alert(
        '预算已调整',
        `预算已调整至 ${newBudget.toLocaleString()} ${aiParams.budgetCurrency}，将为您提供更好的旅行体验。`,
        [{ text: '确定' }]
      );
    } else if (type === 'adjust_itinerary') {
      // 调整行程偏好
      Alert.alert(
        '行程优化',
        '系统将在当前预算内为您优化行程安排，选择性价比更高的选项。',
        [{ text: '确定' }]
      );
    }

    // setShowBudgetSuggestion(false); // 已移除
    // setBudgetCheckPerformed(false); // 已移除
  }, [aiParams.budgetCurrency, updateParams]);

  // 🔄 处理替代方案选择
  const handleSelectAlternative = useCallback((alternative: any) => {
    console.log('🔄 选择替代方案:', alternative.title);

    // 调整预算
    updateParams('budget', alternative.adjustedBudget);

    Alert.alert(
      '方案已应用',
      `已选择"${alternative.title}"方案，预算调整为 ${alternative.adjustedBudget.toLocaleString()} ${aiParams.budgetCurrency}。`,
      [{ text: '确定' }]
    );

    // setShowBudgetSuggestion(false); // 已移除
    // setBudgetCheckPerformed(false); // 已移除
  }, [aiParams.budgetCurrency, updateParams]);

  // 智能偏好生成AI行程（使用多Agent系统）
  const handleSmartAIGeneration = useCallback(async (preferences: any) => {
    if (!aiParams.destination.trim()) {
      Alert.alert('提示', '请输入目的地');
      return;
    }

    try {
      setIsGenerating(true);
      setGenerationStep('🤖 启动多Agent智能规划...');

      // 清理偏好对象，移除React事件属性
      const cleanPreferences = {
        styles: preferences.styles || ['cultural'],
        budgetLevel: preferences.budgetLevel || 'mid_range',
        pace: preferences.pace || 'moderate',
        accommodation: preferences.accommodation || ['mid_range_hotel'],
        transport: preferences.transport || ['walking', 'public'],
        accommodationPriorities: preferences.accommodationPriorities || ['location', 'cleanliness', 'value_for_money'],
        transportPriorities: preferences.transportPriorities || ['convenience', 'cost_effective', 'cultural_experience'],
        intensity: preferences.intensity || 'moderate',
        specialInterests: preferences.specialInterests || [],
        avoidances: preferences.avoidances || []
      };

      // 动态加载并使用Ultra Think桥接器生成计划
      const planningService = await loadUltraThinkService();

      // 🚀 使用Ultra Think智能路由系统生成行程
      console.log('🚀 启动Ultra Think智能路由系统');

      // 计算总旅客人数
      const totalTravelers = typeof aiParams.travelers === 'object' && aiParams.travelers !== null
        ? (aiParams.travelers.adults || 0) + (aiParams.travelers.children || 0) + (aiParams.travelers.infants || 0)
        : aiParams.travelers || 1;

      const travelPreferences = {
        destination: aiParams.destination,
        duration: parseInt(aiParams.duration) || 3,
        budget: parseFloat(aiParams.budget) || 2000,
        currency: aiParams.budgetCurrency || 'MYR',
        travelers: totalTravelers,
        startDate: new Date(aiParams.startDate || Date.now()),
        travelStyle: cleanPreferences.styles || ['文化探索'],
        accommodation: cleanPreferences.accommodation || ['酒店'],
        transport: cleanPreferences.transport || ['公共交通']
      };

      // 使用Ultra Think桥接器路由到Master Solver
      console.log('🎯 强制使用Ultra Think Master Solver');
      let response = await planningService.routeRequest('activity-generation', {
        destination: travelPreferences.destination,
        duration: travelPreferences.duration,
        travelers: travelPreferences.travelers,
        budget: travelPreferences.budget,
        currency: travelPreferences.currency,
        startDate: travelPreferences.startDate,
        preferences: {
          travelStyle: travelPreferences.travelStyle,
          accommodation: travelPreferences.accommodation,
          transport: travelPreferences.transport
        }
      }, {
        forceUltraThink: true, // 🔧 强制使用Ultra Think
        enableMasterSolver: true // 🔧 启用Master Solver
      });

      // 如果Ultra Think失败，尝试使用简化参数
      if (!response.success) {
        console.log('🔄 Ultra Think生成失败，尝试简化参数');

        // 使用更简化的偏好设置重试
        response = await planningService.routeRequest('activity-generation', {
          destination: aiParams.destination,
          duration: parseInt(aiParams.duration) || 3,
          budget: parseFloat(aiParams.budget) || 2000,
          currency: aiParams.budgetCurrency || 'MYR',
          travelers: totalTravelers,
          preferences: {
            travelStyle: ['文化探索'],
            accommodation: ['酒店'],
            transport: ['公共交通']
          }
        });
      }

      if (response.success && response.data) {
        setGenerationStep('✅ Ultra Think智能规划生成完成！');

        // 处理Ultra Think响应数据
        const journeyData = response.data.activities ? {
          title: `${aiParams.destination}${aiParams.duration}日精彩之旅`,
          destination: aiParams.destination,
          activities: response.data.activities,
          summary: response.data.summary,
          optimization: response.data.optimization,
          source: response.source,
          // 🎯 保存Ultra Think原始JSON数据到metadata
          metadata: {
            jsonData: response.data, // 完整的Ultra Think响应数据
            generationSource: 'ultra_think_master_solver_v2',
            qualityScore: response.data.metadata?.qualityScore || 0.95,
            processingTime: response.executionTime || 0,
            llmEnhanced: response.data.metadata?.llmEnhanced || true
          }
        } : {
          ...response.data,
          // 🎯 确保所有Ultra Think数据都有metadata.jsonData
          metadata: {
            jsonData: response.data,
            generationSource: 'ultra_think_master_solver_v2',
            qualityScore: response.data.metadata?.qualityScore || 0.95,
            processingTime: response.executionTime || 0,
            llmEnhanced: response.data.metadata?.llmEnhanced || true
          }
        };

        // 显示预览而不是自动保存
        console.log('🎯 Ultra Think生成的Journey数据:', {
          title: journeyData.title,
          activitiesCount: journeyData.activities?.length || 0,
          destination: journeyData.destination,
          source: response.source,
          executionTime: response.executionTime
        });

        setGenerationStep('🎉 智能规划生成完成！正在跳转预览...');

        // 直接保存并跳转到预览，不显示选择对话框
        await saveAndViewJourney(response.data);
      } else {
        throw new Error(response.error || '智能规划生成失败');
      }
    } catch (error) {
      console.error('❌ 智能规划失败:', error);
      Alert.alert(
        '生成失败',
        error instanceof Error ? error.message : '未知错误，请稍后重试'
      );
    } finally {
      setIsGenerating(false);
      setGenerationStep('');
    }
  }, [aiParams, navigation]);

  // 保存并查看Journey
  const saveAndViewJourney = useCallback(async (journeyData: any) => {
    try {

      // 在保存前进行自动预算降级处理（如有需要）
      const processedJourney = postProcessBudget(journeyData, aiParams.budget || 0, aiParams.travelers.adults + aiParams.travelers.children + aiParams.travelers.infants);

      console.log('💾 开始保存Journey...');
      const journeyToSave = processedJourney;
      const journeyService = coreServiceRegistry.getJourneyService();
      const saveResponse = await journeyService.createJourney(journeyToSave);

      if (saveResponse.success) {
        console.log('✅ Journey保存成功');
        console.log('🎯 导航到JourneyDetail，传递完整数据:', {
          journeyId: saveResponse.data!.id,
          activitiesCount: saveResponse.data!.activities?.length || 0
        });

        // 🔧 使用全局安全日期函数

        // 🔧 使用Ultra Safe序列化Journey数据
        const serializedJourneyData = {
          ...saveResponse.data,
          startDate: safeDateToString(saveResponse.data!.startDate),
          endDate: safeDateToString(saveResponse.data!.endDate),
          createdAt: safeDateToString(saveResponse.data!.createdAt),
          updatedAt: safeDateToString(saveResponse.data!.updatedAt),
          activities: saveResponse.data!.activities?.map((activity: any) => ({
            ...activity,
            startTime: safeDateToString(activity.startTime),
            endTime: safeDateToString(activity.endTime),
            createdAt: safeDateToString(activity.createdAt),
            updatedAt: safeDateToString(activity.updatedAt),
          })) || []
        };

        navigation.navigate('JourneyDetail', {
          journeyId: saveResponse.data!.id,
          journeyData: serializedJourneyData // 传递序列化后的Journey数据
        });
        if (Platform.OS === 'android') {
          // ToastAndroid.show('行程已保存！', ToastAndroid.SHORT);
        }
      } else {
        throw new Error(saveResponse.error || '保存失败');
      }
    } catch (saveError) {
      console.error('❌ Journey保存失败:', saveError);
      Alert.alert('保存失败', '保存时出现问题，请重试。');
    }
  }, [navigation, aiParams]);

  // 多Agent智能行程生成 (带错误恢复)
  const handleGenerateAIJourney = useCallback(async () => {
    if (!aiParams.destination.trim()) {
      Alert.alert('提示', '请输入目的地');
      return;
    }

    try {
      setIsGenerating(true);
      setGenerationStep('🤖 启动多Agent智能规划系统...');

      // 添加延迟确保模块加载完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 构建用户偏好对象 - 使用实际选择的选项
      const userPreferences = {
        // 使用选择的旅行风格，如果没有选择则使用文化深度作为默认
        styles: Array.isArray(aiParams.travelStyle) && aiParams.travelStyle.length > 0
          ? aiParams.travelStyle
          : ['cultural'],

        // 根据预算自动判断预算级别
        budgetLevel: aiParams.budget <= 1000 ? 'budget' : aiParams.budget <= 3000 ? 'mid_range' : 'luxury',

        // 根据旅行风格推断节奏
        pace: aiParams.travelStyle?.includes('relaxed') ? 'slow' :
              aiParams.travelStyle?.includes('adventure') ? 'fast' : 'moderate',

        // 使用选择的住宿偏好
        accommodation: Array.isArray(aiParams.accommodation) && aiParams.accommodation.length > 0
          ? aiParams.accommodation
          : ['mid_range_hotel'],

        // 使用选择的交通偏好
        transport: Array.isArray(aiParams.transport) && aiParams.transport.length > 0
          ? aiParams.transport
          : ['walking', 'public'],

        // 根据住宿选择推断优先级
        accommodationPriorities: aiParams.accommodation?.includes('luxury_hotel')
          ? ['service_quality', 'amenities', 'location']
          : aiParams.accommodation?.includes('local_guesthouse')
          ? ['cultural_experience', 'authenticity', 'location']
          : ['location', 'cleanliness', 'value_for_money'],

        // 根据交通选择推断优先级
        transportPriorities: aiParams.transport?.includes('walking')
          ? ['cultural_experience', 'environmental_friendly', 'cost_effective']
          : aiParams.transport?.includes('car_rental')
          ? ['flexibility', 'convenience', 'comfort']
          : ['convenience', 'cost_effective', 'cultural_experience'],

        // 根据旅行风格推断强度
        intensity: aiParams.travelStyle?.includes('adventure') ? 'high' :
                  aiParams.travelStyle?.includes('relaxed') ? 'low' : 'moderate'
      };

      setGenerationStep('✨ 智能偏好分析完成，开始Ultra Think智能路由...');

      // 动态加载并调用Ultra Think桥接器服务
      const planningService = await loadUltraThinkService();

      // 计算总旅客人数
      const totalTravelers = typeof aiParams.travelers === 'object' && aiParams.travelers !== null
        ? (aiParams.travelers.adults || 0) + (aiParams.travelers.children || 0) + (aiParams.travelers.infants || 0)
        : aiParams.travelers || 1;

      // 🔧 修复：使用用户选择的开始日期，而不是当前日期
      const userStartDate = aiParams.startDate ? new Date(aiParams.startDate) : new Date();
      console.log('📅 用户选择的开始日期:', userStartDate.toDateString());

      const travelPreferences = {
        destination: aiParams.destination,
        duration: parseInt(aiParams.duration.replace(/\D/g, '')),
        budget: parseFloat(aiParams.budget) || 2000,
        currency: aiParams.budgetCurrency,
        travelers: totalTravelers,
        startDate: userStartDate, // 使用用户选择的日期
        travelStyle: userPreferences.styles || ['文化探索'],
        accommodation: userPreferences.accommodation || ['酒店'],
        transport: userPreferences.transport || ['公共交通']
      };

      // 使用Ultra Think桥接器路由到最佳AI系统
      const response = await planningService.routeRequest('activity-generation', {
        destination: travelPreferences.destination,
        duration: travelPreferences.duration,
        travelers: travelPreferences.travelers,
        budget: travelPreferences.budget,
        currency: travelPreferences.currency,
        startDate: travelPreferences.startDate,
        preferences: {
          travelStyle: travelPreferences.travelStyle,
          accommodation: travelPreferences.accommodation,
          transport: travelPreferences.transport
        }
      });

      if (response.success && response.data) {
        setGenerationStep('💾 保存智能规划结果...');

        // 显示生成结果信息
        if (response.metadata) {
          console.log(`🎯 使用模型: ${response.metadata.model || '未知'}`);
          console.log(`📊 质量评分: ${response.metadata.qualityScore?.toFixed(1) || '未知'}/10`);
          console.log(`⏱️ 执行时间: ${response.metadata.executionTime || '未知'}ms`);
          console.log(`💾 缓存状态: ${response.metadata.cached ? '命中' : '新生成'}`);
        }

        // 不直接保存，而是让用户预览
        try {
          let generatedJourney = response.data;

          // 生成临时预览ID
          if (!generatedJourney.id || generatedJourney.id.startsWith('temp_')) {
            generatedJourney.id = `preview_journey_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          }

          setGenerationStep('🎉 多Agent智能规划完成！');

          // 直接保存并导航到行程详情，不显示预览对话框
          try {
            setGenerationStep('💾 正在保存行程...');

            // 🛡️ Ultra Safe 日期序列化，确保日期有效
            const safeDateToString = (dateValue: any): string => {
              try {
                if (!dateValue) {
                  return new Date().toISOString();
                }

                if (typeof dateValue === 'string') {
                  const parsed = new Date(dateValue);
                  if (isNaN(parsed.getTime())) {
                    console.warn('⚠️ 无效日期字符串:', dateValue);
                    return new Date().toISOString();
                  }
                  return parsed.toISOString();
                }

                if (dateValue instanceof Date) {
                  if (isNaN(dateValue.getTime())) {
                    console.warn('⚠️ 无效Date对象:', dateValue);
                    return new Date().toISOString();
                  }
                  return dateValue.toISOString();
                }

                console.warn('⚠️ 未知日期类型:', typeof dateValue, dateValue);
                return new Date().toISOString();
              } catch (error) {
                console.error('❌ 日期序列化失败:', error, dateValue);
                return new Date().toISOString();
              }
            };

            // 序列化Journey数据以避免导航警告
            const serializedJourney = {
              ...generatedJourney,
              startDate: safeDateToString(generatedJourney.startDate),
              endDate: safeDateToString(generatedJourney.endDate),
              activities: generatedJourney.activities || []
            };

            // 🔄 修复保存流程：先缓存，不直接保存到数据库
            console.log('💾 缓存生成的行程数据，等待用户确认保存');

            // 将生成的行程数据缓存到本地存储
            await AsyncStorage.setItem(`temp_journey_${generatedJourney.id}`, JSON.stringify(serializedJourney));

            setGenerationStep('✅ 行程生成完成！');

            // 导航到行程详情页面（预览模式，未保存到数据库）
            navigation.navigate('JourneyDetail', {
              journeyId: generatedJourney.id,
              journeyData: serializedJourney,
              metadata: response.metadata,
              isTemporary: true, // 🔧 标记为临时数据，需要用户确认保存
              source: 'ai_generated' // 🔧 标记数据来源
            });

            // 重置生成状态
            setTimeout(() => {
              setIsGenerating(false);
              setGenerationStep('');
            }, 1000);

          } catch (saveError) {
            console.error('保存行程失败:', saveError);
            setGenerationStep('');
            setIsGenerating(false);

            // 🔧 使用全局安全日期函数

            // 保存失败时，仍然导航到预览页面
            const serializedJourney = {
              ...generatedJourney,
              startDate: safeDateToString(generatedJourney.startDate),
              endDate: safeDateToString(generatedJourney.endDate),
              activities: generatedJourney.activities || []
            };

            Alert.alert(
              '🎉 行程生成成功',
              '行程已生成，但保存时出现问题。您可以在行程详情页面手动保存。',
              [
                {
                  text: '查看行程',
                  onPress: () => {
                    navigation.navigate('JourneyDetail', {
                      journeyData: serializedJourney,
                      isPreview: true,
                      metadata: response.metadata
                    });
                  }
                }
              ]
            );
          }
        } catch (saveError) {
          console.error('保存多Agent Journey时出错:', saveError);
          // 即使保存失败，也显示生成的内容
          Alert.alert(
            '🎉 智能规划成功',
            '多Agent系统已生成高质量行程，但保存时出现问题。请手动保存。',
            [{ text: '确定', onPress: () => {} }]
          );
        }
      } else {
        console.error('❌ 多Agent规划失败:', response.error);
        Alert.alert(
          '生成失败',
          `多Agent智能规划失败：${response.error || '未知错误'}。\n\n💡 建议：\n• 检查网络连接\n• 尝试简化目的地描述\n• 稍后重试`
        );
      }
    } catch (error) {
      console.error('💥 多Agent系统异常:', error);

      // 尝试使用备用生成方法
      try {
        setGenerationStep('🔄 切换到备用生成系统...');
        await handleFallbackGeneration();
      } catch (fallbackError) {
        console.error('💥 备用系统也失败:', fallbackError);
        Alert.alert(
          '系统错误',
          `AI规划系统暂时不可用：${error instanceof Error ? error.message : '未知错误'}\n\n建议：\n• 检查网络连接\n• 重启应用\n• 稍后重试`
        );
      }
    } finally {
      setIsGenerating(false);
      setGenerationStep('');
    }
  }, [aiParams, navigation]);

  // 备用生成方法
  const handleFallbackGeneration = useCallback(async () => {
    console.log('🔄 使用备用生成系统...');

    // 创建一个简单的示例行程
    const fallbackJourney = {
      id: `fallback_journey_${Date.now()}`,
      title: `${aiParams.destination}探索之旅`,
      destination: { address: aiParams.destination },
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + parseInt(aiParams.duration.replace(/\D/g, '')) * 24 * 60 * 60 * 1000).toISOString(),
      budget: aiParams.budget,
      currency: aiParams.budgetCurrency,
      activities: [
        {
          id: 'fallback_1',
          title: '抵达目的地',
          description: '从机场或车站前往住宿地点，办理入住手续',
          startTime: new Date().toISOString(),
          duration: 120,
          cost: Math.round(aiParams.budget * 0.1),
          currency: aiParams.budgetCurrency,
          location: { address: aiParams.destination }
        },
        {
          id: 'fallback_2',
          title: '当地美食体验',
          description: '品尝当地特色美食，体验本土饮食文化',
          startTime: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),
          duration: 180,
          cost: Math.round(aiParams.budget * 0.3),
          currency: aiParams.budgetCurrency,
          location: { address: aiParams.destination }
        },
        {
          id: 'fallback_3',
          title: '城市观光',
          description: '参观当地著名景点，了解历史文化',
          startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          duration: 240,
          cost: Math.round(aiParams.budget * 0.4),
          currency: aiParams.budgetCurrency,
          location: { address: aiParams.destination }
        }
      ]
    };

    // 保存备用行程
    try {
      const journeyService = coreServiceRegistry.getJourneyService();
      const saveResponse = await journeyService.createJourney(fallbackJourney as any);

      if (saveResponse.success) {
        Alert.alert(
          '✅ 行程生成成功',
          '已使用备用系统生成基础行程，您可以稍后手动调整。',
          [{ text: '查看行程', onPress: () => navigation.navigate('JourneyDetail', { journeyId: saveResponse.data!.id }) }]
        );
      } else {
        throw new Error('保存失败');
      }
    } catch (saveError) {
      console.error('备用行程保存失败:', saveError);
      Alert.alert('生成成功', '已生成基础行程，但保存时出现问题。');
    }
  }, [aiParams, navigation]);

  // 🔄 修改为缓存函数，不直接保存到数据库
  const cacheJourneyData = useCallback(async (journey: any) => {
    try {
      // 生成临时ID
      const tempId = `temp_journey_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const journeyToCache = { ...journey, id: tempId, isTemporary: true };

      // 缓存到本地存储，等待用户确认保存
      await AsyncStorage.setItem(`temp_journey_${tempId}`, JSON.stringify(journeyToCache));

      console.log('💾 Journey数据已缓存，等待用户确认保存');
      return journeyToCache;
    } catch (error) {
      console.error('❌ 缓存Journey失败:', error);
      throw error;
    }
  }, []);

  // 🔧 新增：用户确认后保存到数据库的函数
  const saveJourneyToDatabase = useCallback(async (journey: any) => {
    try {
      // 生成正式ID
      const finalId = `journey_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const journeyToSave = {
        ...journey,
        id: finalId,
        isTemporary: false, // 标记为正式保存
        savedAt: new Date().toISOString()
      };

      // 保存到数据库
      const journeyService = coreServiceRegistry.getJourneyService();
      const saveResponse = await journeyService.createJourney(journeyToSave);

      if (saveResponse.success) {
        console.log('✅ Journey正式保存到数据库成功');

        // 清理临时缓存
        if (journey.id && journey.id.startsWith('temp_')) {
          await AsyncStorage.removeItem(`temp_journey_${journey.id}`);
        }

        return saveResponse.data;
      } else {
        console.warn('⚠️ 数据库保存失败:', saveResponse.error);
        throw new Error(saveResponse.error || '保存失败');
      }
    } catch (error) {
      console.error('❌ 保存Journey到数据库失败:', error);
      throw error;
    }
  }, []);

  // 处理地点选择
  const handlePlaceSelected = useCallback((place: any) => {
    setSelectedPlace(place);
    updateParams('destination', place.name || place.formatted_address);

    // 可以根据选择的地点自动调整其他参数
    console.log('选择的地点:', place);
  }, [updateParams]);

  // 创建FlatList数据数组 - 避免VirtualizedList嵌套
  const contentSections = useMemo(() => {
    const sections = [
      { id: 'destination', type: 'destination' },
      { id: 'season', type: 'season' },
      { id: 'duration', type: 'duration' },
      { id: 'budget', type: 'budget' },
      { id: 'travelers', type: 'travelers' },
    ];

    if (useSmartPreferences) {
      sections.push({ id: 'smart_preferences', type: 'smart_preferences' });
    } else {
      sections.push(
        { id: 'travel_style', type: 'travel_style' },
        { id: 'accommodation', type: 'accommodation' },
        { id: 'transport', type: 'transport' },
        { id: 'preferences', type: 'preferences' },
        { id: 'generate_button', type: 'generate_button' }
      );
    }

    sections.push({ id: 'bottom_spacing', type: 'bottom_spacing' });
    return sections;
  }, [useSmartPreferences]);

  // 渲染每个部分
  const renderSection = useCallback(({ item }: { item: any }) => {
    switch (item.type) {
      case 'destination':
        return (
          <View style={[styles.modernCard, styles.destinationCard]}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="location" size={20} color="#667eea" />
              </View>
              <Text style={styles.cardTitle}>目的地</Text>
            </View>
            <ModernDestinationInput
              value={aiParams.destination}
              onChangeText={(text) => updateParams('destination', text)}
              onDestinationSelected={handlePlaceSelected}
              placeholder="你想去哪里？"
              apiKey={GOOGLE_PLACES_CONFIG.API_KEY}
              style={styles.modernDestinationInput}
            />
          </View>
        );

      case 'season':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="sunny" size={20} color="#FF9800" />
              </View>
              <Text style={styles.cardTitle}>出行季节</Text>
            </View>
            <View style={styles.seasonGrid}>
              {SEASON_OPTIONS.map((season) => (
                <TouchableOpacity
                  key={season.value}
                  style={[
                    styles.modernSeasonOption,
                    aiParams.season === season.value && styles.modernSeasonOptionSelected
                  ]}
                  onPress={() => updateParams('season', season.value)}
                >
                  <View style={styles.seasonContent}>
                    <Text style={styles.seasonEmoji}>{season.emoji}</Text>
                    <Text style={[
                      styles.seasonText,
                      aiParams.season === season.value && styles.seasonTextSelected
                    ]}>
                      {season.label}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );

      case 'duration':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="calendar" size={20} color="#4CAF50" />
              </View>
              <Text style={styles.cardTitle}>行程时长</Text>
            </View>
            <DurationSelector
              value={aiParams.duration}
              onValueChange={(value: string) => updateParams('duration', value)}
              style={styles.durationSelector}
            />
          </View>
        );

      case 'budget':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="wallet" size={20} color="#4CAF50" />
              </View>
              <Text style={styles.cardTitle}>旅行预算</Text>
              <Text style={styles.cardSubtitle}>根据位置自动设置货币</Text>
            </View>
            <View style={styles.budgetContainer}>
              <CurrencySelector
                value={aiParams.budget}
                onValueChange={(amount: number) => updateParams('budget', amount)}
                currency={currentCurrency}
                onCurrencyChange={handleCurrencyChange}
                style={styles.currencySelector}
                autoDetectLocation={false}
              />
            </View>
          </View>
        );

      case 'travelers':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="people" size={20} color="#FF5722" />
              </View>
              <Text style={styles.cardTitle}>旅客信息</Text>
            </View>
            <View style={styles.travelersContainer}>
              {Object.entries(TRAVELER_TYPES).map(([key, config]) => (
                <View key={key} style={styles.travelerRow}>
                  <View style={styles.travelerInfo}>
                    <Text style={styles.travelerLabel}>{config.label}</Text>
                    <Text style={styles.travelerDescription}>{config.description}</Text>
                  </View>
                  <View style={styles.travelerCounter}>
                    <TouchableOpacity
                      style={styles.counterButton}
                      onPress={() => updateTravelers(key as keyof AIJourneyParams['travelers'], aiParams.travelers[key as keyof AIJourneyParams['travelers']] - 1)}
                    >
                      <Ionicons name="remove" size={16} color="#666" />
                    </TouchableOpacity>
                    <Text style={styles.counterValue}>
                      {aiParams.travelers[key as keyof AIJourneyParams['travelers']]}
                    </Text>
                    <TouchableOpacity
                      style={styles.counterButton}
                      onPress={() => updateTravelers(key as keyof AIJourneyParams['travelers'], aiParams.travelers[key as keyof AIJourneyParams['travelers']] + 1)}
                    >
                      <Ionicons name="add" size={16} color="#666" />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          </View>
        );

      case 'smart_preferences':
        return (
          <View style={styles.modernCard}>
            <EnhancedAIPlanningInterface
              destination={aiParams.destination}
              duration={aiParams.duration}
              budget={aiParams.budget}
              currency={aiParams.budgetCurrency}
              travelers={aiParams.travelers}
              onPlanGenerated={(plan) => {
                navigation.navigate('JourneyDetail', {
                  journeyId: plan.id
                });
              }}
              onError={(error) => {
                Alert.alert('生成失败', error);
              }}
            />
          </View>
        );

      case 'travel_style':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="compass" size={20} color="#FF5722" />
              </View>
              <Text style={styles.cardTitle}>旅行风格</Text>
              <Text style={styles.cardSubtitle}>可多选</Text>
            </View>
            <ModernOptionCard
              options={TRAVEL_STYLE_OPTIONS}
              selectedValue={aiParams.travelStyle || []}
              onSelectionChange={(value: string | string[]) => updateParams('travelStyle', value as string[])}
              multiSelect={true}
              columns={2}
              style={styles.optionCardContainer}
            />
          </View>
        );

      case 'accommodation':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="bed" size={20} color="#E91E63" />
              </View>
              <Text style={styles.cardTitle}>住宿偏好</Text>
              <Text style={styles.cardSubtitle}>可多选</Text>
            </View>
            <ModernOptionCard
              options={ACCOMMODATION_OPTIONS}
              selectedValue={aiParams.accommodation || []}
              onSelectionChange={(value: string | string[]) => updateParams('accommodation', value as string[])}
              multiSelect={true}
              columns={2}
              style={styles.optionCardContainer}
            />
          </View>
        );

      case 'transport':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="car" size={20} color="#2196F3" />
              </View>
              <Text style={styles.cardTitle}>交通偏好</Text>
              <Text style={styles.cardSubtitle}>可多选</Text>
            </View>
            <ModernOptionCard
              options={TRANSPORT_OPTIONS}
              selectedValue={aiParams.transport || []}
              onSelectionChange={(value: string | string[]) => updateParams('transport', value as string[])}
              multiSelect={true}
              columns={2}
              style={styles.optionCardContainer}
            />
          </View>
        );

      case 'preferences':
        return (
          <View style={styles.modernCard}>
            <View style={styles.cardHeader}>
              <View style={styles.cardIconContainer}>
                <Ionicons name="heart" size={20} color="#E91E63" />
              </View>
              <Text style={styles.cardTitle}>特殊偏好</Text>
              <Text style={styles.cardSubtitle}>可选</Text>
            </View>
            <TextInput
              style={styles.modernPreferencesInput}
              placeholder="描述你的旅行偏好，如：美食、历史文化、自然风光..."
              value={aiParams.preferences}
              onChangeText={(text: string) => updateParams('preferences', text)}
              multiline
              numberOfLines={4}
              placeholderTextColor="#a0a0a0"
            />
            <Text style={styles.preferencesHint}>
              💡 详细描述有助于AI生成更符合你需求的行程
            </Text>
          </View>
        );

      case 'generate_button':
        return (
          <TouchableOpacity
            style={[styles.modernGenerateButton, isGenerating && styles.modernGenerateButtonDisabled]}
            onPress={handleSmartAIGeneration}
            disabled={isGenerating}
          >
            <LinearGradient
              colors={isGenerating ? ['#cccccc', '#999999'] : ['#667eea', '#764ba2']}
              style={styles.generateButtonGradient}
            >
              {isGenerating ? (
                <View style={styles.generatingContainer}>
                  <ActivityIndicator size="small" color="#ffffff" />
                  <Text style={styles.generatingText}>{generationStep || '生成中...'}</Text>
                </View>
              ) : (
                <View style={styles.generateButtonContent}>
                  <Ionicons name="sparkles" size={20} color="#ffffff" />
                  <Text style={styles.generateButtonText}>🚀 智能生成行程</Text>
                </View>
              )}
            </LinearGradient>
          </TouchableOpacity>
        );

      case 'bottom_spacing':
        return <View style={{ height: 80 }} />;

      default:
        return null;
    }
  }, [aiParams, updateParams, updateTravelers, handlePlaceSelected, isGenerating, generationStep, navigation, handleSmartAIGeneration]);

  // 处理货币变化
  const handleCurrencyChange = useCallback((currency: any) => {
    setCurrentCurrency(currency);

    // 更新货币代码
    updateParams('budgetCurrency', currency.code);

    // 如果当前预算为0或者不在新货币的合理范围内，设置建议预算
    const { min, max, typical } = currency.suggestedBudgetRange;
    if (aiParams.budget === 0 || aiParams.budget < min || aiParams.budget > max) {
      updateParams('budget', typical[1]); // 使用中等建议金额
    }
  }, [aiParams.budget, updateParams]);

  return (
    <View style={styles.container}>
      {/* 优化的头部设计 */}
      <View style={[styles.modernHeader, { paddingTop: insets.top }]}>
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.headerGradient}
        />

        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.modernBackButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#ffffff" />
          </TouchableOpacity>

          <View style={styles.headerCenter}>
            <View style={styles.aiIconContainer}>
              <View style={styles.aiIcon}>
                <Ionicons name="sparkles" size={24} color="#ffffff" />
              </View>
            </View>
            <Text style={styles.modernHeaderTitle}>AI 智能规划</Text>
            <Text style={styles.modernHeaderSubtitle}>个性化定制你的完美旅程</Text>
          </View>

          <View style={styles.headerRight}>
            {/* 🎨 格式切换已移除，固定使用Modern格式 */}
            {/* 预留空间用于未来功能扩展 */}
          </View>
        </View>
      </View>

      {/* 现代化内容区域 - 使用FlatList避免嵌套问题 */}
      <KeyboardAvoidingView
        style={styles.modernContent}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
      >
        <FlatList
          data={contentSections}
          renderItem={renderSection}
          keyExtractor={(item: any) => item.id}
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          removeClippedSubviews={false}
        />
      </KeyboardAvoidingView>

      {/* 🔍 API密钥调试组件 - 仅开发环境 */}
      {__DEV__ && <APIKeyDebugger />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },

  // 现代化头部样式
  modernHeader: {
    position: 'relative',
    paddingHorizontal: 20,
    paddingBottom: 30,
    backgroundColor: '#ffffff',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 16,
  },
  modernBackButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  aiIconContainer: {
    marginBottom: 8,
  },
  aiIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modernHeaderTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#ffffff',
    marginBottom: 4,
  },
  modernHeaderSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  headerRight: {
    width: 44,
  },

  modernContent: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  contentWrapper: {
    padding: 20,
    paddingTop: 24,
  },
  modernDestinationInput: {
    marginBottom: 8,
  },
  modernCard: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.04)',
  },
  destinationCard: {
    zIndex: 1000,
    elevation: 10,
    marginBottom: 30,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  cardIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a1a1a',
    flex: 1,
  },
  cardSubtitle: {
    fontSize: 12,
    color: '#999999',
    marginLeft: 8,
  },
  seasonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  modernSeasonOption: {
    flex: 1,
    minWidth: '45%',
    borderRadius: 16,
    backgroundColor: '#f8f9fa',
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  modernSeasonOptionSelected: {
    borderColor: '#667eea',
    backgroundColor: '#667eea',
    shadowOpacity: 0.15,
  },
  seasonContent: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
  },
  seasonEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  seasonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  seasonTextSelected: {
    color: '#ffffff',
  },
  budgetContainer: {
    marginTop: 8,
  },
  budgetInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  modernBudgetInput: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: '#1a1a1a',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  currencySelector: {
    minWidth: 100,
  },
  travelersContainer: {
    gap: 16,
  },
  travelerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  travelerInfo: {
    flex: 1,
  },
  travelerLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 2,
  },
  travelerDescription: {
    fontSize: 14,
    color: '#666666',
  },
  travelerCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  counterButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  counterValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    minWidth: 24,
    textAlign: 'center',
  },
  optionCardContainer: {
    marginTop: 8,
  },
  modernPreferencesInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: '#1a1a1a',
    minHeight: 100,
    borderWidth: 2,
    borderColor: 'transparent',
    textAlignVertical: 'top',
  },
  preferencesHint: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'center',
    marginTop: 12,
    lineHeight: 16,
  },
  modernGenerateButton: {
    borderRadius: 20,
    overflow: 'hidden',
    marginTop: 20,
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  modernGenerateButtonDisabled: {
    shadowOpacity: 0.1,
    elevation: 2,
  },
  generateButtonGradient: {
    paddingVertical: 20,
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  generateButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  generateButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#ffffff',
  },
  generatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  generatingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },

  // 🎨 格式切换按钮样式已移除 (简化开发复杂度)
});

export default AIJourneyPlanningScreen;
