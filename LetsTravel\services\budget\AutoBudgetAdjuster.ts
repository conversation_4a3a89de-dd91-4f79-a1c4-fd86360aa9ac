/**
 * AutoBudgetAdjuster - 简化自动降级服务
 * 如果总成本超出预算，将所有成本项降级到最低档。
 * 最低档金额表可根据实际数据调整。
 */
import { Journey, Activity } from '../../types/CoreServices';

// 最低成本常量表（单位：每人 / 每间 / 每次）
const MIN_COST_TABLE = {
  flight: 1400,
  hotel: 1000,
  meals: 1000,
  sight: 300,
  transport: 800
};

// 基于类型快速判断活动分类
function getCategory(activity: Activity): keyof typeof MIN_COST_TABLE {
  switch (activity.type) {
    case 'hotel':
      return 'hotel';
    case 'restaurant':
      return 'meals';
    case 'transport':
      return 'transport';
    case 'attraction':
    default:
      return 'sight';
  }
}

export function postProcessBudget(journey: Journey, userBudget: number, travelerCount = 1): Journey {
  // 🎯 保留Ultra Think metadata（如果存在）
  const originalMetadata = journey.metadata;

  // 计算当前成本
  const totalCost = calcTotalCost(journey);

  if (userBudget >= totalCost) {
    journey.budgetStatus = 'original';
    // 🎯 确保metadata被保留
    if (originalMetadata) {
      journey.metadata = originalMetadata;
    }
    return journey;
  }

  // 执行降级
  journey.activities = journey.activities.map((act) => {
    const cat = getCategory(act);
    const min = MIN_COST_TABLE[cat] * (cat === 'flight' ? travelerCount : 1);
    return { ...act, cost: min, note: '已降级至最低成本配置' };
  });

  // TODO: 住宿、餐饮、交通成本若存于 journey 其他字段，可在此处降级

  const downgradedTotal = calcTotalCost(journey);
  const minCost = calcMinCost(travelerCount);

  if (userBudget >= downgradedTotal) {
    journey.budgetStatus = 'auto_downgraded';
    journey.adjustmentNote = '⚠️ 预算不足，已自动使用最低成本配置';
  } else {
    journey.budgetStatus = 'still_insufficient';
    journey.adjustmentNote = '❗ 您的预算低于最低可行成本，行程已极限降级';
  }

  // 🎯 确保Ultra Think metadata被保留
  if (originalMetadata) {
    journey.metadata = {
      ...originalMetadata,
      budgetAdjusted: true,
      budgetAdjustmentNote: journey.adjustmentNote
    };
  }

  return journey;
}

function calcTotalCost(journey: Journey): number {
  return journey.activities.reduce((sum, a) => sum + (a.cost || 0), 0);
}

function calcMinCost(travelerCount: number): number {
  return (
    MIN_COST_TABLE.flight * travelerCount +
    MIN_COST_TABLE.sight +
    MIN_COST_TABLE.meals +
    MIN_COST_TABLE.hotel +
    MIN_COST_TABLE.transport
  );
}
