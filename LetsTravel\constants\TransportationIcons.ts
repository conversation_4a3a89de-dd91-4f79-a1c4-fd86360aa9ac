/**
 * 🚇 统一交通图标系统
 * 
 * 提供统一的交通方式图标映射，替换分散在各处的图标定义
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

// ===== 核心交通图标映射 =====

/**
 * 🎯 标准交通图标映射
 * 统一所有交通方式的图标显示
 */
export const TRANSPORT_ICONS = {
  // 步行相关
  walking: '🚶‍♂️',
  walk: '🚶‍♂️',
  pedestrian: '🚶‍♂️',
  
  // 地铁/轻轨
  subway: '🚇',
  metro: '🚇',
  underground: '🚇',
  mrt: '🚇',
  lrt: '🚈',
  
  // 公交系统
  bus: '🚌',
  coach: '🚌',
  shuttle: '🚐',
  minibus: '🚐',
  
  // 出租车/网约车
  taxi: '🚕',
  cab: '🚕',
  uber: '🚕',
  grab: '🚕',
  rideshare: '🚕',
  
  // 火车系统
  train: '🚆',
  railway: '🚆',
  intercity: '🚄',
  highspeed: '🚄',
  bullet: '🚅',
  
  // 航空
  flight: '✈️',
  airplane: '✈️',
  aircraft: '✈️',
  domestic: '✈️',
  international: '🛫',
  
  // 水上交通
  ferry: '⛴️',
  boat: '🚤',
  ship: '🚢',
  cruise: '🛳️',
  
  // 自行车
  bicycle: '🚲',
  bike: '🚲',
  cycling: '🚴‍♂️',
  ebike: '🚲',
  
  // 摩托车
  motorcycle: '🏍️',
  scooter: '🛵',
  motorbike: '🏍️',
  
  // 汽车
  car: '🚗',
  vehicle: '🚗',
  private: '🚗',
  rental: '🚗',
  driving: '🚗',
  
  // 特殊交通
  tram: '🚊',
  trolley: '🚋',
  funicular: '🚠',
  cable: '🚡',
  
  // 默认图标
  default: '🚗',
  unknown: '❓'
} as const;

/**
 * 🎨 交通图标颜色映射
 * 为不同交通方式提供统一的颜色方案
 */
export const TRANSPORT_COLORS = {
  walking: '#4CAF50',      // 绿色 - 环保
  subway: '#2196F3',       // 蓝色 - 地铁蓝
  bus: '#FF9800',          // 橙色 - 公交橙
  taxi: '#FFEB3B',         // 黄色 - 出租车黄
  train: '#9C27B0',        // 紫色 - 铁路紫
  flight: '#607D8B',       // 蓝灰色 - 天空色
  ferry: '#00BCD4',        // 青色 - 海洋色
  bicycle: '#8BC34A',      // 浅绿色 - 自然绿
  motorcycle: '#F44336',   // 红色 - 动力红
  car: '#795548',          // 棕色 - 汽车棕
  tram: '#E91E63',         // 粉红色 - 电车粉
  default: '#9E9E9E'       // 灰色 - 默认灰
} as const;

/**
 * 🏷️ 交通方式中文名称映射
 */
export const TRANSPORT_NAMES_ZH = {
  walking: '步行',
  subway: '地铁',
  bus: '公交',
  taxi: '出租车',
  train: '火车',
  flight: '航班',
  ferry: '轮渡',
  bicycle: '自行车',
  motorcycle: '摩托车',
  car: '汽车',
  tram: '电车',
  default: '交通'
} as const;

/**
 * 🌍 交通方式英文名称映射
 */
export const TRANSPORT_NAMES_EN = {
  walking: 'Walking',
  subway: 'Subway',
  bus: 'Bus',
  taxi: 'Taxi',
  train: 'Train',
  flight: 'Flight',
  ferry: 'Ferry',
  bicycle: 'Bicycle',
  motorcycle: 'Motorcycle',
  car: 'Car',
  tram: 'Tram',
  default: 'Transport'
} as const;

// ===== 工具函数 =====

/**
 * 🎯 获取交通图标
 * 
 * @param transportType 交通类型
 * @returns 对应的emoji图标
 */
export function getTransportIcon(transportType: string): string {
  const normalizedType = transportType.toLowerCase().trim();
  
  // 直接匹配
  if (normalizedType in TRANSPORT_ICONS) {
    return TRANSPORT_ICONS[normalizedType as keyof typeof TRANSPORT_ICONS];
  }
  
  // 模糊匹配
  for (const [key, icon] of Object.entries(TRANSPORT_ICONS)) {
    if (normalizedType.includes(key) || key.includes(normalizedType)) {
      return icon;
    }
  }
  
  // 特殊匹配规则
  if (normalizedType.includes('步') || normalizedType.includes('走')) {
    return TRANSPORT_ICONS.walking;
  }
  if (normalizedType.includes('地铁') || normalizedType.includes('捷运')) {
    return TRANSPORT_ICONS.subway;
  }
  if (normalizedType.includes('公交') || normalizedType.includes('巴士')) {
    return TRANSPORT_ICONS.bus;
  }
  if (normalizedType.includes('出租') || normalizedType.includes('计程车')) {
    return TRANSPORT_ICONS.taxi;
  }
  if (normalizedType.includes('火车') || normalizedType.includes('列车')) {
    return TRANSPORT_ICONS.train;
  }
  if (normalizedType.includes('飞机') || normalizedType.includes('航班')) {
    return TRANSPORT_ICONS.flight;
  }
  
  return TRANSPORT_ICONS.default;
}

/**
 * 🎨 获取交通颜色
 * 
 * @param transportType 交通类型
 * @returns 对应的颜色代码
 */
export function getTransportColor(transportType: string): string {
  const normalizedType = transportType.toLowerCase().trim();
  
  // 获取对应的图标键
  const iconKey = Object.keys(TRANSPORT_ICONS).find(key => 
    normalizedType === key || 
    normalizedType.includes(key) || 
    key.includes(normalizedType)
  );
  
  if (iconKey && iconKey in TRANSPORT_COLORS) {
    return TRANSPORT_COLORS[iconKey as keyof typeof TRANSPORT_COLORS];
  }
  
  return TRANSPORT_COLORS.default;
}

/**
 * 🏷️ 获取交通名称
 * 
 * @param transportType 交通类型
 * @param language 语言 ('zh' | 'en')
 * @returns 对应的名称
 */
export function getTransportName(
  transportType: string, 
  language: 'zh' | 'en' = 'zh'
): string {
  const normalizedType = transportType.toLowerCase().trim();
  
  // 获取对应的图标键
  const iconKey = Object.keys(TRANSPORT_ICONS).find(key => 
    normalizedType === key || 
    normalizedType.includes(key) || 
    key.includes(normalizedType)
  );
  
  if (iconKey) {
    const nameMap = language === 'zh' ? TRANSPORT_NAMES_ZH : TRANSPORT_NAMES_EN;
    if (iconKey in nameMap) {
      return nameMap[iconKey as keyof typeof nameMap];
    }
  }
  
  // 如果找不到匹配，返回原始输入的首字母大写形式
  return transportType.charAt(0).toUpperCase() + transportType.slice(1);
}

/**
 * 🎯 获取完整的交通信息
 * 
 * @param transportType 交通类型
 * @param language 语言
 * @returns 包含图标、颜色、名称的完整信息
 */
export function getTransportInfo(
  transportType: string,
  language: 'zh' | 'en' = 'zh'
): {
  icon: string;
  color: string;
  name: string;
  type: string;
} {
  return {
    icon: getTransportIcon(transportType),
    color: getTransportColor(transportType),
    name: getTransportName(transportType, language),
    type: transportType
  };
}

/**
 * 🔍 搜索交通类型
 * 
 * @param query 搜索关键词
 * @param language 语言
 * @returns 匹配的交通类型列表
 */
export function searchTransportTypes(
  query: string,
  language: 'zh' | 'en' = 'zh'
): Array<{
  type: string;
  icon: string;
  color: string;
  name: string;
}> {
  const normalizedQuery = query.toLowerCase().trim();
  const results: Array<{
    type: string;
    icon: string;
    color: string;
    name: string;
  }> = [];
  
  const nameMap = language === 'zh' ? TRANSPORT_NAMES_ZH : TRANSPORT_NAMES_EN;
  
  for (const [type, icon] of Object.entries(TRANSPORT_ICONS)) {
    if (type === 'default' || type === 'unknown') continue;
    
    const name = nameMap[type as keyof typeof nameMap] || type;
    const color = TRANSPORT_COLORS[type as keyof typeof TRANSPORT_COLORS] || TRANSPORT_COLORS.default;
    
    // 检查是否匹配
    if (
      type.includes(normalizedQuery) ||
      name.toLowerCase().includes(normalizedQuery) ||
      normalizedQuery.includes(type) ||
      normalizedQuery.includes(name.toLowerCase())
    ) {
      results.push({
        type,
        icon,
        color,
        name
      });
    }
  }
  
  return results;
}

// ===== 类型定义 =====

export type TransportType = keyof typeof TRANSPORT_ICONS;
export type TransportColor = keyof typeof TRANSPORT_COLORS;

/**
 * 🚇 交通信息接口
 */
export interface TransportationInfo {
  type: string;
  icon: string;
  color: string;
  name: string;
  nameEn?: string;
}

/**
 * 🎨 交通显示配置
 */
export interface TransportDisplayConfig {
  showIcon: boolean;
  showName: boolean;
  showColor: boolean;
  language: 'zh' | 'en';
  size: 'small' | 'medium' | 'large';
}

// ===== 默认导出 =====
export default {
  TRANSPORT_ICONS,
  TRANSPORT_COLORS,
  TRANSPORT_NAMES_ZH,
  TRANSPORT_NAMES_EN,
  getTransportIcon,
  getTransportColor,
  getTransportName,
  getTransportInfo,
  searchTransportTypes
};
