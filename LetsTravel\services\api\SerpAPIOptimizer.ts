/**
 * 🔧 SerpAPI调用优化器 - Ultra Think系统性修复
 * 优化SerpAPI调用参数和错误处理，提升API调用成功率
 */

import axios, { AxiosError } from 'axios';

export interface SerpAPIConfig {
  apiKey: string;
  timeout: number;
  maxRetries: number;
  retryDelay: number;
  enableFallback: boolean;
}

export interface SerpAPICallResult {
  success: boolean;
  data: any;
  source: 'api' | 'fallback';
  executionTime: number;
  retryCount: number;
  error?: string;
  statusCode?: number;
}

export interface SerpAPIMetrics {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageResponseTime: number;
  successRate: number;
  lastError?: string;
  lastSuccessTime?: Date;
}

export class SerpAPIOptimizer {
  private config: SerpAPIConfig;
  private metrics: SerpAPIMetrics;
  private rateLimitTracker: Map<string, { count: number; resetTime: number }>;

  constructor(config: Partial<SerpAPIConfig> = {}) {
    this.config = {
      apiKey: config.apiKey || process.env.SERP_API_KEY || '',
      timeout: config.timeout || 15000,
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 1000,
      enableFallback: config.enableFallback !== false
    };

    this.metrics = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageResponseTime: 0,
      successRate: 0
    };

    this.rateLimitTracker = new Map();
  }

  /**
   * 🔍 优化的地点搜索
   */
  async searchPlaces(query: string, location: string, options: any = {}): Promise<SerpAPICallResult> {
    const startTime = Date.now();
    
    console.log(`🔍 SerpAPI地点搜索: ${query} in ${location}`);
    
    // 构建优化的搜索参数
    const searchParams = this.buildOptimizedPlaceParams(query, location, options);
    
    // 执行带重试的API调用
    const result = await this.executeWithRetry('places', searchParams, startTime);
    
    // 更新指标
    this.updateMetrics(result);
    
    return result;
  }

  /**
   * ✈️ 优化的航班搜索
   */
  async searchFlights(params: any): Promise<SerpAPICallResult> {
    const startTime = Date.now();
    
    console.log(`✈️ SerpAPI航班搜索:`, params);
    
    // 构建优化的搜索参数
    const searchParams = this.buildOptimizedFlightParams(params);
    
    // 执行带重试的API调用
    const result = await this.executeWithRetry('flights', searchParams, startTime);
    
    // 更新指标
    this.updateMetrics(result);
    
    return result;
  }

  /**
   * 🏨 优化的酒店搜索
   */
  async searchHotels(destination: string, checkIn: string, checkOut: string, guests: number): Promise<SerpAPICallResult> {
    const startTime = Date.now();
    
    console.log(`🏨 SerpAPI酒店搜索: ${destination}`);
    
    // 构建优化的搜索参数
    const searchParams = this.buildOptimizedHotelParams(destination, checkIn, checkOut, guests);
    
    // 执行带重试的API调用
    const result = await this.executeWithRetry('hotels', searchParams, startTime);
    
    // 更新指标
    this.updateMetrics(result);
    
    return result;
  }

  /**
   * 🔄 带重试的API执行
   */
  private async executeWithRetry(
    searchType: string,
    params: any,
    startTime: number
  ): Promise<SerpAPICallResult> {
    let lastError: any;
    
    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      try {
        // 检查速率限制
        if (this.isRateLimited(searchType)) {
          console.warn(`⚠️ ${searchType}搜索触发速率限制，等待重置`);
          await this.waitForRateLimit(searchType);
        }

        // 执行API调用
        const response = await this.makeAPICall(params);
        
        // 检查响应有效性
        if (this.isValidResponse(response.data)) {
          console.log(`✅ ${searchType}搜索成功 (尝试${attempt + 1}/${this.config.maxRetries})`);
          
          return {
            success: true,
            data: response.data,
            source: 'api',
            executionTime: Date.now() - startTime,
            retryCount: attempt,
            statusCode: response.status
          };
        } else {
          throw new Error('API返回无效数据');
        }

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ ${searchType}搜索失败 (尝试${attempt + 1}/${this.config.maxRetries}):`, error.message);
        
        // 处理特定错误类型
        if (this.shouldStopRetrying(error)) {
          break;
        }
        
        // 等待后重试
        if (attempt < this.config.maxRetries - 1) {
          const delay = this.calculateRetryDelay(attempt);
          console.log(`⏳ ${delay}ms后重试...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // 所有重试失败，返回降级数据
    console.error(`❌ ${searchType}搜索完全失败，使用降级数据`);
    
    if (this.config.enableFallback) {
      const fallbackData = this.generateFallbackData(searchType, params);
      
      return {
        success: true,
        data: fallbackData,
        source: 'fallback',
        executionTime: Date.now() - startTime,
        retryCount: this.config.maxRetries,
        error: lastError?.message
      };
    }

    return {
      success: false,
      data: null,
      source: 'api',
      executionTime: Date.now() - startTime,
      retryCount: this.config.maxRetries,
      error: lastError?.message,
      statusCode: lastError?.response?.status
    };
  }

  /**
   * 🌐 执行API调用
   */
  private async makeAPICall(params: any): Promise<any> {
    const response = await axios.get('https://serpapi.com/search', {
      params: {
        ...params,
        api_key: this.config.apiKey
      },
      timeout: this.config.timeout,
      headers: {
        'User-Agent': 'Trekmate-Optimized/1.0',
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate'
      },
      validateStatus: (status) => status < 500 // 只有5xx错误才抛出异常
    });

    // 检查SerpAPI特定错误
    if (response.data.error) {
      throw new Error(`SerpAPI错误: ${response.data.error}`);
    }

    return response;
  }

  /**
   * 🏗️ 构建优化的地点搜索参数
   */
  private buildOptimizedPlaceParams(query: string, location: string, options: any): any {
    const coordinates = this.parseLocation(location);
    const params: any = {
      engine: 'google_maps',
      q: query,
      type: options.type || 'search',
      hl: options.language || 'zh-cn',
      gl: options.country || 'jp', // 🔧 修复：东京使用jp
      num: Math.min(options.limit || 20, 20), // 限制结果数量
      start: options.offset || 0
    };

    // 🔧 修复：只有在有有效坐标时才添加ll参数
    if (coordinates && coordinates.length > 0) {
      params.ll = coordinates;
    }

    return params;
  }

  /**
   * ✈️ 构建优化的航班搜索参数
   */
  private buildOptimizedFlightParams(params: any): any {
    return {
      engine: 'google_flights',
      departure_id: params.origin || params.departure,
      arrival_id: params.destination || params.arrival,
      outbound_date: this.formatDate(params.departureDate || params.date),
      return_date: params.returnDate ? this.formatDate(params.returnDate) : undefined,
      currency: params.currency || 'MYR',
      hl: 'zh-cn',
      gl: 'my',
      adults: params.passengers || params.adults || 1,
      travel_class: this.mapTravelClass(params.travelClass || 'economy')
    };
  }

  /**
   * 🏨 构建优化的酒店搜索参数
   */
  private buildOptimizedHotelParams(destination: string, checkIn: string, checkOut: string, guests: number): any {
    return {
      engine: 'google_hotels',
      q: destination,
      check_in_date: this.formatDate(checkIn),
      check_out_date: this.formatDate(checkOut),
      adults: guests || 1,
      currency: 'MYR',
      hl: 'zh-cn',
      gl: 'my',
      sort_by: 'price' // 按价格排序
    };
  }

  /**
   * 🔍 解析位置参数
   */
  private parseLocation(location: string): string {
    // 如果已经是坐标格式，直接返回
    if (/^-?\d+\.?\d*,-?\d+\.?\d*$/.test(location)) {
      return location;
    }

    // 🔧 修复：为常见城市提供正确的坐标
    const cityCoordinates: Record<string, string> = {
      '东京': '35.6762,139.6503',
      '吉隆坡': '3.1390,101.6869',
      '新加坡': '1.3521,103.8198',
      '曼谷': '13.7563,100.5018',
      '首尔': '37.5665,126.9780',
      '香港': '22.3193,114.1694',
      '台北': '25.0330,121.5654'
    };

    // 如果是已知城市，返回正确坐标
    if (cityCoordinates[location]) {
      console.log(`🔧 使用${location}的正确坐标: ${cityCoordinates[location]}`);
      return cityCoordinates[location];
    }

    // 否则不使用ll参数，让SerpAPI自动解析
    return '';
  }

  /**
   * 📅 格式化日期
   */
  private formatDate(date: string | Date): string {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    return d.toISOString().split('T')[0]; // YYYY-MM-DD格式
  }

  /**
   * ✈️ 映射旅行舱位
   */
  private mapTravelClass(travelClass: string): number {
    const classMap: Record<string, number> = {
      'economy': 1,
      'premium_economy': 2,
      'business': 3,
      'first': 4
    };
    
    return classMap[travelClass.toLowerCase()] || 1;
  }

  /**
   * ✅ 验证响应有效性
   */
  private isValidResponse(data: any): boolean {
    if (!data) return false;
    
    // 检查是否有错误
    if (data.error) return false;
    
    // 检查是否有结果
    if (data.local_results && Array.isArray(data.local_results)) {
      return data.local_results.length > 0;
    }
    
    if (data.best_flights && Array.isArray(data.best_flights)) {
      return data.best_flights.length > 0;
    }
    
    if (data.properties && Array.isArray(data.properties)) {
      return data.properties.length > 0;
    }
    
    return true; // 其他情况认为有效
  }

  /**
   * 🚫 判断是否应该停止重试
   */
  private shouldStopRetrying(error: any): boolean {
    if (error.response) {
      const status = error.response.status;
      
      // 4xx错误通常不需要重试
      if (status >= 400 && status < 500) {
        // 除了429 (Too Many Requests)
        return status !== 429;
      }
    }
    
    // API密钥错误不需要重试
    if (error.message.includes('Invalid API key') || error.message.includes('API key')) {
      return true;
    }
    
    return false;
  }

  /**
   * ⏱️ 计算重试延迟
   */
  private calculateRetryDelay(attempt: number): number {
    // 指数退避算法
    const baseDelay = this.config.retryDelay;
    const maxDelay = 10000; // 最大10秒
    
    const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
    
    // 添加随机抖动，避免雷群效应
    const jitter = Math.random() * 0.1 * delay;
    
    return Math.floor(delay + jitter);
  }

  /**
   * 🚦 检查速率限制
   */
  private isRateLimited(searchType: string): boolean {
    const tracker = this.rateLimitTracker.get(searchType);
    if (!tracker) return false;
    
    const now = Date.now();
    
    // 如果重置时间已过，清除限制
    if (now > tracker.resetTime) {
      this.rateLimitTracker.delete(searchType);
      return false;
    }
    
    // 检查是否超过限制（假设每分钟100次）
    return tracker.count >= 100;
  }

  /**
   * ⏳ 等待速率限制重置
   */
  private async waitForRateLimit(searchType: string): Promise<void> {
    const tracker = this.rateLimitTracker.get(searchType);
    if (!tracker) return;
    
    const waitTime = Math.max(0, tracker.resetTime - Date.now());
    if (waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * 🎭 生成降级数据
   */
  private generateFallbackData(searchType: string, params: any): any {
    switch (searchType) {
      case 'places':
        return {
          local_results: [
            {
              title: `${params.q}推荐地点`,
              address: `${params.ll}附近`,
              rating: 4.0,
              reviews: 100,
              type: 'establishment',
              place_id: `fallback_${Date.now()}`
            }
          ]
        };
        
      case 'flights':
        return {
          best_flights: [
            {
              flights: [{
                departure_airport: { id: params.departure_id },
                arrival_airport: { id: params.arrival_id },
                departure_time: '09:00',
                arrival_time: '12:00',
                duration: 180,
                airline: '航空公司',
                price: 800,
                currency: params.currency || 'MYR'
              }]
            }
          ]
        };
        
      case 'hotels':
        return {
          properties: [
            {
              name: `${params.q}推荐酒店`,
              rate_per_night: { lowest: '200 MYR' },
              total_rate: { lowest: '400 MYR' },
              rating: 4.2,
              reviews: 150,
              amenities: ['WiFi', '早餐', '停车场']
            }
          ]
        };
        
      default:
        return { message: '暂无数据' };
    }
  }

  /**
   * 📊 更新指标
   */
  private updateMetrics(result: SerpAPICallResult): void {
    this.metrics.totalCalls++;
    
    if (result.success) {
      this.metrics.successfulCalls++;
      this.metrics.lastSuccessTime = new Date();
    } else {
      this.metrics.failedCalls++;
      this.metrics.lastError = result.error;
    }
    
    // 更新平均响应时间
    const totalTime = (this.metrics.averageResponseTime * (this.metrics.totalCalls - 1)) + result.executionTime;
    this.metrics.averageResponseTime = totalTime / this.metrics.totalCalls;
    
    // 更新成功率
    this.metrics.successRate = this.metrics.successfulCalls / this.metrics.totalCalls;
    
    console.log(`📊 SerpAPI指标更新: 成功率${(this.metrics.successRate * 100).toFixed(1)}%, 平均响应时间${this.metrics.averageResponseTime.toFixed(0)}ms`);
  }

  /**
   * 📈 获取指标
   */
  getMetrics(): SerpAPIMetrics {
    return { ...this.metrics };
  }

  /**
   * 🔧 更新配置
   */
  updateConfig(updates: Partial<SerpAPIConfig>): void {
    this.config = { ...this.config, ...updates };
  }
}
