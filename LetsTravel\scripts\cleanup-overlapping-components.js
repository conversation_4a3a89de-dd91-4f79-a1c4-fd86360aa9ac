/**
 * 🧹 重叠组件清理脚本
 * 
 * 清理被新的统一组件替代的重叠组件：
 * 1. IntelligentTimeScheduler -> UnifiedTimeAllocationEngine
 * 2. HumanizedTimeProcessor -> UnifiedTimeAllocationEngine  
 * 3. UltraThinkBudgetCalculator -> TrulyUnifiedBudgetEngine
 * 4. PreciseBudgetCalculator -> TrulyUnifiedBudgetEngine
 * 5. IntelligentBudgetCalculator -> TrulyUnifiedBudgetEngine
 * 
 * <AUTHOR> Think System
 * @version 1.0.0
 * @created 2025-01-30
 */

const fs = require('fs');
const path = require('path');

console.log('🧹🧹🧹 开始重叠组件清理 🧹🧹🧹');
console.log('='.repeat(60));

// 定义需要清理的组件映射
const componentMappings = {
  // 时间处理组件
  'IntelligentTimeScheduler': {
    replacement: 'UnifiedTimeAllocationEngine',
    files: [
      'utils/CompleteJourneyOrchestrator.ts',
      'scripts/ultra-think-final-comprehensive-test.ts'
    ],
    deprecated: true
  },
  
  'HumanizedTimeProcessor': {
    replacement: 'UnifiedTimeAllocationEngine',
    files: [],
    deprecated: true
  },
  
  // 预算计算组件
  'UltraThinkBudgetCalculator': {
    replacement: 'TrulyUnifiedBudgetEngine',
    files: [
      'services/activity/UltraThinkActivityGenerator.ts'
    ],
    deprecated: true
  },
  
  'PreciseBudgetCalculator': {
    replacement: 'TrulyUnifiedBudgetEngine', 
    files: [
      'services/budget/EnhancedBudgetService.ts',
      'solutions/UltraIntegrationTest.ts',
      'solutions/UltraJourneyOptimizer.ts'
    ],
    deprecated: true
  },
  
  'IntelligentBudgetCalculator': {
    replacement: 'TrulyUnifiedBudgetEngine',
    files: [
      'services/converters/JSONDataConverter.ts'
    ],
    deprecated: true
  }
};

/**
 * 🔍 扫描文件中的组件使用情况
 */
function scanComponentUsage() {
  console.log('🔍 扫描组件使用情况...');
  
  const usageReport = {};
  
  for (const [componentName, config] of Object.entries(componentMappings)) {
    usageReport[componentName] = {
      replacement: config.replacement,
      knownFiles: config.files,
      actualUsage: [],
      deprecated: config.deprecated
    };
    
    // 扫描已知文件
    for (const filePath of config.files) {
      const fullPath = path.join(__dirname, '..', filePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        if (content.includes(componentName)) {
          usageReport[componentName].actualUsage.push({
            file: filePath,
            exists: true,
            hasUsage: true
          });
        }
      } else {
        usageReport[componentName].actualUsage.push({
          file: filePath,
          exists: false,
          hasUsage: false
        });
      }
    }
  }
  
  return usageReport;
}

/**
 * 📊 生成清理报告
 */
function generateCleanupReport(usageReport) {
  console.log('📊 生成清理报告...');
  
  for (const [componentName, info] of Object.entries(usageReport)) {
    console.log(`\\n🔧 ${componentName}:`);
    console.log(`   替代组件: ${info.replacement}`);
    console.log(`   是否废弃: ${info.deprecated ? '是' : '否'}`);
    
    if (info.actualUsage.length > 0) {
      console.log('   使用情况:');
      for (const usage of info.actualUsage) {
        const status = usage.exists ? 
          (usage.hasUsage ? '✅ 存在使用' : '⚠️ 文件存在但无使用') : 
          '❌ 文件不存在';
        console.log(`     - ${usage.file}: ${status}`);
      }
    } else {
      console.log('   使用情况: 无已知使用');
    }
  }
}

/**
 * 🔄 更新文件中的组件引用
 */
function updateComponentReferences(usageReport) {
  console.log('\\n🔄 更新组件引用...');
  
  const updateResults = [];
  
  for (const [componentName, info] of Object.entries(usageReport)) {
    if (!info.deprecated) continue;
    
    for (const usage of info.actualUsage) {
      if (!usage.exists || !usage.hasUsage) continue;
      
      const filePath = path.join(__dirname, '..', usage.file);
      
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        
        // 更新导入语句
        const importPatterns = [
          new RegExp(`import.*${componentName}.*from`, 'g'),
          new RegExp(`import.*{.*${componentName}.*}.*from`, 'g')
        ];
        
        for (const pattern of importPatterns) {
          if (pattern.test(content)) {
            console.log(`   📝 更新 ${usage.file} 中的导入语句`);
            // 这里可以添加具体的替换逻辑
            modified = true;
          }
        }
        
        // 更新使用语句
        const usagePatterns = [
          new RegExp(`${componentName}\\.`, 'g'),
          new RegExp(`new ${componentName}\\(`, 'g')
        ];
        
        for (const pattern of usagePatterns) {
          if (pattern.test(content)) {
            console.log(`   📝 发现 ${usage.file} 中的使用语句需要更新`);
            modified = true;
          }
        }
        
        updateResults.push({
          file: usage.file,
          component: componentName,
          replacement: info.replacement,
          modified: modified,
          needsManualUpdate: modified // 标记需要手动更新
        });
        
      } catch (error) {
        console.error(`   ❌ 更新 ${usage.file} 失败:`, error.message);
        updateResults.push({
          file: usage.file,
          component: componentName,
          error: error.message
        });
      }
    }
  }
  
  return updateResults;
}

/**
 * 📋 生成清理建议
 */
function generateCleanupRecommendations(usageReport, updateResults) {
  console.log('\\n📋 清理建议:');
  
  const recommendations = [];
  
  // 1. 需要手动更新的文件
  const manualUpdates = updateResults.filter(r => r.needsManualUpdate);
  if (manualUpdates.length > 0) {
    console.log('\\n🔧 需要手动更新的文件:');
    for (const update of manualUpdates) {
      console.log(`   - ${update.file}:`);
      console.log(`     将 ${update.component} 替换为 ${update.replacement}`);
      recommendations.push({
        type: 'manual_update',
        file: update.file,
        from: update.component,
        to: update.replacement
      });
    }
  }
  
  // 2. 可以删除的文件
  const deprecatedFiles = [
    'utils/IntelligentTimeScheduler.ts',
    'utils/HumanizedTimeProcessor.ts', 
    'utils/IntelligentBudgetCalculator.ts',
    'services/budget/UltraThinkBudgetCalculator.ts'
  ];
  
  console.log('\\n🗑️ 可以考虑删除的废弃文件:');
  for (const file of deprecatedFiles) {
    const fullPath = path.join(__dirname, '..', file);
    if (fs.existsSync(fullPath)) {
      console.log(`   - ${file} (存在)`);
      recommendations.push({
        type: 'delete_file',
        file: file,
        exists: true
      });
    } else {
      console.log(`   - ${file} (不存在)`);
    }
  }
  
  // 3. 需要验证的集成
  console.log('\\n✅ 需要验证的新组件集成:');
  const newComponents = [
    'UnifiedTimeAllocationEngine',
    'TrulyUnifiedBudgetEngine',
    'EnhancedTransportationGenerator',
    'IntelligentMealScheduler',
    'IntelligentActivityClassifier'
  ];
  
  for (const component of newComponents) {
    console.log(`   - ${component}: 确保在UltraThinkMasterSolverV2中正确集成`);
    recommendations.push({
      type: 'verify_integration',
      component: component
    });
  }
  
  return recommendations;
}

/**
 * 💾 保存清理报告
 */
function saveCleanupReport(usageReport, updateResults, recommendations) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalComponents: Object.keys(usageReport).length,
      deprecatedComponents: Object.values(usageReport).filter(c => c.deprecated).length,
      filesNeedingUpdate: updateResults.filter(r => r.needsManualUpdate).length,
      recommendations: recommendations.length
    },
    componentAnalysis: usageReport,
    updateResults: updateResults,
    recommendations: recommendations,
    nextSteps: [
      '1. 手动更新标记的文件中的组件引用',
      '2. 测试更新后的功能是否正常工作', 
      '3. 删除不再使用的废弃文件',
      '4. 验证新组件的集成是否完整',
      '5. 运行完整的集成测试'
    ]
  };
  
  const reportPath = path.join(__dirname, 'cleanup-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\\n💾 清理报告已保存到: ${reportPath}`);
  
  return report;
}

// 执行清理分析
async function runCleanupAnalysis() {
  try {
    // 1. 扫描组件使用情况
    const usageReport = scanComponentUsage();
    
    // 2. 生成清理报告
    generateCleanupReport(usageReport);
    
    // 3. 更新组件引用
    const updateResults = updateComponentReferences(usageReport);
    
    // 4. 生成清理建议
    const recommendations = generateCleanupRecommendations(usageReport, updateResults);
    
    // 5. 保存清理报告
    const report = saveCleanupReport(usageReport, updateResults, recommendations);
    
    console.log('\\n🎯 清理分析完成!');
    console.log('📋 后续步骤:');
    for (const step of report.nextSteps) {
      console.log(`   ${step}`);
    }
    
    return report;
    
  } catch (error) {
    console.error('❌ 清理分析失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runCleanupAnalysis()
    .then(() => {
      console.log('\\n✅ 重叠组件清理分析完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 清理分析失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runCleanupAnalysis,
  componentMappings
};