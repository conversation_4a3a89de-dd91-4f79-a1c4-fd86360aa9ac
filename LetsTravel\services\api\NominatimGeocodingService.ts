/**
 * 🌍 Nominatim地理编码服务
 * 
 * 集成免费的Nominatim API替换Google Places API，实现地理编码和反向地理编码
 * 成本优化：从$50-80/月降至$0-20/月
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @created 2024-01-29
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// ===== 接口定义 =====

export interface GeocodingResult {
  id: string;
  displayName: string;
  latitude: number;
  longitude: number;
  address: AddressComponents;
  boundingBox?: BoundingBox;
  importance: number;
  type: string;
  category: string;
  confidence: number;
  source: 'nominatim' | 'cache' | 'fallback';
}

export interface AddressComponents {
  houseNumber?: string;
  road?: string;
  neighbourhood?: string;
  suburb?: string;
  city?: string;
  county?: string;
  state?: string;
  postcode?: string;
  country?: string;
  countryCode?: string;
}

export interface BoundingBox {
  south: number;
  north: number;
  west: number;
  east: number;
}

export interface ReverseGeocodingResult {
  displayName: string;
  address: AddressComponents;
  latitude: number;
  longitude: number;
  type: string;
  confidence: number;
  source: 'nominatim' | 'cache';
}

export interface GeocodingOptions {
  limit?: number;
  countryCode?: string;
  language?: string;
  addressDetails?: boolean;
  extraTags?: boolean;
  nameDetails?: boolean;
  useCache?: boolean;
  timeout?: number;
}

// ===== Nominatim地理编码服务 =====

export class NominatimGeocodingService {
  private static instance: NominatimGeocodingService;
  private baseUrl = 'https://nominatim.openstreetmap.org';
  private requestQueue: Array<() => Promise<any>> = [];
  private isProcessing = false;
  private lastRequestTime = 0;
  private readonly minInterval = 1000; // 1秒间隔，遵守Nominatim使用政策
  private readonly maxRetries = 3;
  private readonly cachePrefix = 'nominatim_cache_';
  private readonly cacheTTL = 7 * 24 * 60 * 60 * 1000; // 7天缓存

  private constructor() {}

  /**
   * 🎯 获取单例实例
   */
  static getInstance(): NominatimGeocodingService {
    if (!NominatimGeocodingService.instance) {
      NominatimGeocodingService.instance = new NominatimGeocodingService();
    }
    return NominatimGeocodingService.instance;
  }

  /**
   * 🔍 地理编码 - 地址转坐标
   */
  async geocode(
    query: string,
    options: GeocodingOptions = {}
  ): Promise<GeocodingResult[]> {
    
    // 1. 检查缓存
    if (options.useCache !== false) {
      const cached = await this.getCachedResult(`geocode_${query}`, options);
      if (cached) {
        return cached.map(result => ({ ...result, source: 'cache' as const }));
      }
    }

    // 2. 构建请求参数
    const params = this.buildGeocodingParams(query, options);
    
    // 3. 执行限速请求
    const results = await this.makeRateLimitedRequest(
      `/search?${params.toString()}`
    );

    // 4. 处理响应
    const processedResults = this.processGeocodingResults(results);

    // 5. 缓存结果
    if (options.useCache !== false && processedResults.length > 0) {
      await this.cacheResult(`geocode_${query}`, processedResults, options);
    }

    return processedResults;
  }

  /**
   * 🔄 反向地理编码 - 坐标转地址
   */
  async reverseGeocode(
    latitude: number,
    longitude: number,
    options: GeocodingOptions = {}
  ): Promise<ReverseGeocodingResult | null> {
    
    const cacheKey = `reverse_${latitude}_${longitude}`;
    
    // 1. 检查缓存
    if (options.useCache !== false) {
      const cached = await this.getCachedResult(cacheKey, options);
      if (cached) {
        return { ...cached, source: 'cache' as const };
      }
    }

    // 2. 构建请求参数
    const params = this.buildReverseGeocodingParams(latitude, longitude, options);
    
    // 3. 执行限速请求
    const result = await this.makeRateLimitedRequest(
      `/reverse?${params.toString()}`
    );

    // 4. 处理响应
    const processedResult = this.processReverseGeocodingResult(result);

    // 5. 缓存结果
    if (options.useCache !== false && processedResult) {
      await this.cacheResult(cacheKey, processedResult, options);
    }

    return processedResult;
  }

  /**
   * 🏢 搜索POI
   */
  async searchPOI(
    query: string,
    latitude?: number,
    longitude?: number,
    radius?: number,
    options: GeocodingOptions = {}
  ): Promise<GeocodingResult[]> {
    
    const cacheKey = `poi_${query}_${latitude}_${longitude}_${radius}`;
    
    // 1. 检查缓存
    if (options.useCache !== false) {
      const cached = await this.getCachedResult(cacheKey, options);
      if (cached) {
        return cached.map(result => ({ ...result, source: 'cache' as const }));
      }
    }

    // 2. 构建POI搜索参数
    const params = this.buildPOISearchParams(query, latitude, longitude, radius, options);
    
    // 3. 执行限速请求
    const results = await this.makeRateLimitedRequest(
      `/search?${params.toString()}`
    );

    // 4. 处理响应
    const processedResults = this.processPOIResults(results);

    // 5. 缓存结果
    if (options.useCache !== false && processedResults.length > 0) {
      await this.cacheResult(cacheKey, processedResults, options);
    }

    return processedResults;
  }

  /**
   * 📍 批量地理编码
   */
  async batchGeocode(
    queries: string[],
    options: GeocodingOptions = {}
  ): Promise<Map<string, GeocodingResult[]>> {
    
    const results = new Map<string, GeocodingResult[]>();
    const uncachedQueries: string[] = [];

    // 1. 检查缓存
    if (options.useCache !== false) {
      for (const query of queries) {
        const cached = await this.getCachedResult(`geocode_${query}`, options);
        if (cached) {
          results.set(query, cached.map(result => ({ ...result, source: 'cache' as const })));
        } else {
          uncachedQueries.push(query);
        }
      }
    } else {
      uncachedQueries.push(...queries);
    }

    // 2. 批量处理未缓存的查询
    for (const query of uncachedQueries) {
      try {
        const geocodeResults = await this.geocode(query, { ...options, useCache: false });
        results.set(query, geocodeResults);
        
        // 添加延迟以遵守API限制
        await this.delay(this.minInterval);
      } catch (error) {
        console.error(`批量地理编码失败: ${query}`, error);
        results.set(query, []);
      }
    }

    return results;
  }

  // ===== 私有方法 =====

  /**
   * 🚦 限速请求
   */
  private async makeRateLimitedRequest(endpoint: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const result = await this.executeRequest(endpoint);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  /**
   * 📋 处理请求队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.requestQueue.length > 0) {
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;

      if (timeSinceLastRequest < this.minInterval) {
        await this.delay(this.minInterval - timeSinceLastRequest);
      }

      const request = this.requestQueue.shift();
      if (request) {
        await request();
        this.lastRequestTime = Date.now();
      }
    }

    this.isProcessing = false;
  }

  /**
   * 🌐 执行HTTP请求
   */
  private async executeRequest(endpoint: string): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'LetsTravel/1.0 (<EMAIL>)',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;

      } catch (error) {
        lastError = error as Error;
        console.warn(`Nominatim请求失败 (尝试 ${attempt}/${this.maxRetries}):`, error);

        if (attempt < this.maxRetries) {
          await this.delay(1000 * attempt); // 指数退避
        }
      }
    }

    throw lastError || new Error('Nominatim请求失败');
  }

  /**
   * 🏗️ 构建地理编码参数
   */
  private buildGeocodingParams(query: string, options: GeocodingOptions): URLSearchParams {
    const params = new URLSearchParams({
      q: query,
      format: 'json',
      addressdetails: (options.addressDetails !== false) ? '1' : '0',
      extratags: options.extraTags ? '1' : '0',
      namedetails: options.nameDetails ? '1' : '0',
      limit: (options.limit || 5).toString(),
      'accept-language': options.language || 'zh-CN,zh,en'
    });

    if (options.countryCode) {
      params.append('countrycodes', options.countryCode);
    }

    return params;
  }

  /**
   * 🔄 构建反向地理编码参数
   */
  private buildReverseGeocodingParams(
    lat: number,
    lon: number,
    options: GeocodingOptions
  ): URLSearchParams {
    return new URLSearchParams({
      lat: lat.toString(),
      lon: lon.toString(),
      format: 'json',
      addressdetails: (options.addressDetails !== false) ? '1' : '0',
      extratags: options.extraTags ? '1' : '0',
      namedetails: options.nameDetails ? '1' : '0',
      'accept-language': options.language || 'zh-CN,zh,en'
    });
  }

  /**
   * 🏢 构建POI搜索参数
   */
  private buildPOISearchParams(
    query: string,
    lat?: number,
    lon?: number,
    radius?: number,
    options: GeocodingOptions = {}
  ): URLSearchParams {
    const params = new URLSearchParams({
      q: query,
      format: 'json',
      addressdetails: '1',
      extratags: '1',
      limit: (options.limit || 10).toString(),
      'accept-language': options.language || 'zh-CN,zh,en'
    });

    if (lat && lon) {
      params.append('lat', lat.toString());
      params.append('lon', lon.toString());
      if (radius) {
        params.append('radius', radius.toString());
      }
    }

    if (options.countryCode) {
      params.append('countrycodes', options.countryCode);
    }

    return params;
  }

  /**
   * 🔄 处理地理编码结果
   */
  private processGeocodingResults(results: any[]): GeocodingResult[] {
    if (!Array.isArray(results)) {
      return [];
    }

    return results.map((result, index) => ({
      id: result.place_id?.toString() || `nominatim_${index}`,
      displayName: result.display_name || '',
      latitude: parseFloat(result.lat) || 0,
      longitude: parseFloat(result.lon) || 0,
      address: this.parseAddressComponents(result.address || {}),
      boundingBox: result.boundingbox ? {
        south: parseFloat(result.boundingbox[0]),
        north: parseFloat(result.boundingbox[1]),
        west: parseFloat(result.boundingbox[2]),
        east: parseFloat(result.boundingbox[3])
      } : undefined,
      importance: parseFloat(result.importance) || 0,
      type: result.type || 'unknown',
      category: result.category || 'place',
      confidence: this.calculateConfidence(result),
      source: 'nominatim' as const
    }));
  }

  /**
   * 🔄 处理反向地理编码结果
   */
  private processReverseGeocodingResult(result: any): ReverseGeocodingResult | null {
    if (!result || !result.lat || !result.lon) {
      return null;
    }

    return {
      displayName: result.display_name || '',
      address: this.parseAddressComponents(result.address || {}),
      latitude: parseFloat(result.lat),
      longitude: parseFloat(result.lon),
      type: result.type || 'unknown',
      confidence: this.calculateConfidence(result),
      source: 'nominatim' as const
    };
  }

  /**
   * 🏢 处理POI结果
   */
  private processPOIResults(results: any[]): GeocodingResult[] {
    return this.processGeocodingResults(results)
      .filter(result => this.isPOI(result))
      .sort((a, b) => b.importance - a.importance);
  }

  /**
   * 🏷️ 解析地址组件
   */
  private parseAddressComponents(address: any): AddressComponents {
    return {
      houseNumber: address.house_number,
      road: address.road,
      neighbourhood: address.neighbourhood,
      suburb: address.suburb,
      city: address.city || address.town || address.village,
      county: address.county,
      state: address.state,
      postcode: address.postcode,
      country: address.country,
      countryCode: address.country_code
    };
  }

  /**
   * 📊 计算置信度
   */
  private calculateConfidence(result: any): number {
    let confidence = 0.5; // 基础置信度

    // 基于重要性调整
    if (result.importance) {
      confidence += Math.min(result.importance * 0.5, 0.3);
    }

    // 基于类型调整
    const highConfidenceTypes = ['house', 'building', 'amenity', 'shop', 'tourism'];
    if (highConfidenceTypes.includes(result.type)) {
      confidence += 0.2;
    }

    // 基于地址完整性调整
    if (result.address) {
      const addressFields = Object.keys(result.address).length;
      confidence += Math.min(addressFields * 0.02, 0.1);
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 🏢 判断是否为POI
   */
  private isPOI(result: GeocodingResult): boolean {
    const poiCategories = ['amenity', 'shop', 'tourism', 'leisure', 'historic'];
    const poiTypes = ['restaurant', 'hotel', 'museum', 'park', 'attraction'];
    
    return poiCategories.includes(result.category) || 
           poiTypes.includes(result.type) ||
           result.importance > 0.3;
  }

  /**
   * 💾 缓存结果
   */
  private async cacheResult(key: string, data: any, options: GeocodingOptions): Promise<void> {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        ttl: this.cacheTTL,
        options
      };
      
      await AsyncStorage.setItem(
        `${this.cachePrefix}${key}`,
        JSON.stringify(cacheData)
      );
    } catch (error) {
      console.warn('缓存保存失败:', error);
    }
  }

  /**
   * 📖 获取缓存结果
   */
  private async getCachedResult(key: string, options: GeocodingOptions): Promise<any> {
    try {
      const cached = await AsyncStorage.getItem(`${this.cachePrefix}${key}`);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const now = Date.now();

      // 检查是否过期
      if (now - cacheData.timestamp > cacheData.ttl) {
        await AsyncStorage.removeItem(`${this.cachePrefix}${key}`);
        return null;
      }

      return cacheData.data;
    } catch (error) {
      console.warn('缓存读取失败:', error);
      return null;
    }
  }

  /**
   * ⏱️ 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 🧹 清理过期缓存
   */
  async clearExpiredCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.cachePrefix));
      
      for (const key of cacheKeys) {
        const cached = await AsyncStorage.getItem(key);
        if (cached) {
          const cacheData = JSON.parse(cached);
          const now = Date.now();
          
          if (now - cacheData.timestamp > cacheData.ttl) {
            await AsyncStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.warn('清理缓存失败:', error);
    }
  }

  /**
   * 📊 获取缓存统计
   */
  async getCacheStats(): Promise<{
    totalEntries: number;
    totalSize: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.cachePrefix));
      
      let totalSize = 0;
      let oldestTimestamp = Infinity;
      let newestTimestamp = 0;

      for (const key of cacheKeys) {
        const cached = await AsyncStorage.getItem(key);
        if (cached) {
          totalSize += cached.length;
          const cacheData = JSON.parse(cached);
          oldestTimestamp = Math.min(oldestTimestamp, cacheData.timestamp);
          newestTimestamp = Math.max(newestTimestamp, cacheData.timestamp);
        }
      }

      return {
        totalEntries: cacheKeys.length,
        totalSize,
        oldestEntry: oldestTimestamp === Infinity ? null : new Date(oldestTimestamp),
        newestEntry: newestTimestamp === 0 ? null : new Date(newestTimestamp)
      };
    } catch (error) {
      console.warn('获取缓存统计失败:', error);
      return {
        totalEntries: 0,
        totalSize: 0,
        oldestEntry: null,
        newestEntry: null
      };
    }
  }
}

export default NominatimGeocodingService;
